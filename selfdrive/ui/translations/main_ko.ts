<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE TS>
<TS version="2.1" language="ko_KR">
<context>
    <name>AbstractAlert</name>
    <message>
        <source>Close</source>
        <translation>닫기</translation>
    </message>
    <message>
        <source>Snooze Update</source>
        <translation>업데이트 일시 중지</translation>
    </message>
    <message>
        <source>Reboot and Update</source>
        <translation>업데이트 및 재부팅</translation>
    </message>
</context>
<context>
    <name>AdvancedNetworking</name>
    <message>
        <source>Back</source>
        <translation>뒤로</translation>
    </message>
    <message>
        <source>Enable Tethering</source>
        <translation>테더링 사용</translation>
    </message>
    <message>
        <source>Tethering Password</source>
        <translation>테더링 비밀번호</translation>
    </message>
    <message>
        <source>EDIT</source>
        <translation>편집</translation>
    </message>
    <message>
        <source>Enter new tethering password</source>
        <translation>새 테더링 비밀번호를 입력하세요</translation>
    </message>
    <message>
        <source>IP Address</source>
        <translation>IP 주소</translation>
    </message>
    <message>
        <source>Enable Roaming</source>
        <translation>로밍 사용</translation>
    </message>
    <message>
        <source>APN Setting</source>
        <translation>APN 설정</translation>
    </message>
    <message>
        <source>Enter APN</source>
        <translation>APN 입력</translation>
    </message>
    <message>
        <source>leave blank for automatic configuration</source>
        <translation>자동 설정하려면 빈 칸으로 두세요</translation>
    </message>
    <message>
        <source>Cellular Metered</source>
        <translation>모바일 데이터 종량제</translation>
    </message>
    <message>
        <source>Hidden Network</source>
        <translation>숨겨진 네트워크</translation>
    </message>
    <message>
        <source>CONNECT</source>
        <translation>연결됨</translation>
    </message>
    <message>
        <source>Enter SSID</source>
        <translation>SSID 입력</translation>
    </message>
    <message>
        <source>Enter password</source>
        <translation>비밀번호를 입력하세요</translation>
    </message>
    <message>
        <source>for &quot;%1&quot;</source>
        <translation>&quot;%1&quot;에 접속하려면 비밀번호가 필요합니다</translation>
    </message>
    <message>
        <source>Prevent large data uploads when on a metered cellular connection</source>
        <translation>모바일 데이터 종량제 사용 시 대용량 데이터 업로드 방지</translation>
    </message>
    <message>
        <source>default</source>
        <translation>기본</translation>
    </message>
    <message>
        <source>metered</source>
        <translation>종량제</translation>
    </message>
    <message>
        <source>unmetered</source>
        <translation>무제한</translation>
    </message>
    <message>
        <source>Wi-Fi Network Metered</source>
        <translation>제한된 Wi-Fi 네트워크</translation>
    </message>
    <message>
        <source>Prevent large data uploads when on a metered Wi-Fi connection</source>
        <translation>제한된 Wi-Fi 사용 시 대용량 데이터 업로드 방지</translation>
    </message>
</context>
<context>
    <name>ConfirmationDialog</name>
    <message>
        <source>Ok</source>
        <translation>확인</translation>
    </message>
    <message>
        <source>Cancel</source>
        <translation>취소</translation>
    </message>
</context>
<context>
    <name>DeclinePage</name>
    <message>
        <source>You must accept the Terms and Conditions in order to use openpilot.</source>
        <translation>오픈파일럿을 사용하려면 이용약관에 동의해야 합니다.</translation>
    </message>
    <message>
        <source>Back</source>
        <translation>뒤로</translation>
    </message>
    <message>
        <source>Decline, uninstall %1</source>
        <translation>거절, %1 제거</translation>
    </message>
</context>
<context>
    <name>DeveloperPanel</name>
    <message>
        <source>Joystick Debug Mode</source>
        <translation>조이스틱 디버그 모드</translation>
    </message>
    <message>
        <source>Longitudinal Maneuver Mode</source>
        <translation>가감속 제어 조작 모드</translation>
    </message>
    <message>
        <source>openpilot Longitudinal Control (Alpha)</source>
        <translation>오픈파일럿 가감속 제어 (알파)</translation>
    </message>
    <message>
        <source>WARNING: openpilot longitudinal control is in alpha for this car and will disable Automatic Emergency Braking (AEB).</source>
        <translation>경고: 오픈파일럿 가감속 제어는 알파 기능으로 차량의 자동긴급제동(AEB)기능이 작동하지 않습니다.</translation>
    </message>
    <message>
        <source>On this car, openpilot defaults to the car&apos;s built-in ACC instead of openpilot&apos;s longitudinal control. Enable this to switch to openpilot longitudinal control. Enabling Experimental mode is recommended when enabling openpilot longitudinal control alpha.</source>
        <translation>이 차량에서 오픈파일럿은 오픈파일럿 가감속 제어 대신 기본적으로 차량의 ACC로 가감속을 제어합니다. 오픈파일럿 가감속 제어로 전환하려면 이 기능을 활성화하세요. 오픈파일럿 가감속 제어 알파 기능을 활성화하는 경우 실험 모드 활성화를 권장합니다.</translation>
    </message>
    <message>
        <source>Enable ADB</source>
        <translation>ADB 사용</translation>
    </message>
    <message>
        <source>ADB (Android Debug Bridge) allows connecting to your device over USB or over the network. See https://docs.comma.ai/how-to/connect-to-comma for more info.</source>
        <translation>ADB (안드로이드 디버그 브릿지) USB 또는 네트워크를 통해 장치에 연결할 수 있습니다. 자세한 내용은 https://docs.comma.ai/how-to/connect-to-comma를 참조하세요.</translation>
    </message>
</context>
<context>
    <name>DevicePanel</name>
    <message>
        <source>Dongle ID</source>
        <translation>동글 ID</translation>
    </message>
    <message>
        <source>N/A</source>
        <translation>N/A</translation>
    </message>
    <message>
        <source>Serial</source>
        <translation>시리얼</translation>
    </message>
    <message>
        <source>Driver Camera</source>
        <translation>운전자 카메라</translation>
    </message>
    <message>
        <source>PREVIEW</source>
        <translation>미리보기</translation>
    </message>
    <message>
        <source>Preview the driver facing camera to ensure that driver monitoring has good visibility. (vehicle must be off)</source>
        <translation>운전자 모니터링이 잘 되는지 확인하기 위해 후면 카메라를 미리 봅니다. (차량 시동이 꺼져 있어야 합니다)</translation>
    </message>
    <message>
        <source>Reset Calibration</source>
        <translation>캘리브레이션</translation>
    </message>
    <message>
        <source>RESET</source>
        <translation>초기화</translation>
    </message>
    <message>
        <source>Are you sure you want to reset calibration?</source>
        <translation>캘리브레이션을 초기화하시겠습니까?</translation>
    </message>
    <message>
        <source>Review Training Guide</source>
        <translation>트레이닝 가이드</translation>
    </message>
    <message>
        <source>REVIEW</source>
        <translation>다시보기</translation>
    </message>
    <message>
        <source>Review the rules, features, and limitations of openpilot</source>
        <translation>오픈파일럿의 규칙, 기능 및 제한 다시 확인</translation>
    </message>
    <message>
        <source>Are you sure you want to review the training guide?</source>
        <translation>트레이닝 가이드를 다시 확인하시겠습니까?</translation>
    </message>
    <message>
        <source>Regulatory</source>
        <translation>규제</translation>
    </message>
    <message>
        <source>VIEW</source>
        <translation>보기</translation>
    </message>
    <message>
        <source>Change Language</source>
        <translation>언어 변경</translation>
    </message>
    <message>
        <source>CHANGE</source>
        <translation>변경</translation>
    </message>
    <message>
        <source>Select a language</source>
        <translation>언어를 선택하세요</translation>
    </message>
    <message>
        <source>Reboot</source>
        <translation>재부팅</translation>
    </message>
    <message>
        <source>Power Off</source>
        <translation>전원 끄기</translation>
    </message>
    <message>
        <source> Your device is pointed %1° %2 and %3° %4.</source>
        <translation> 사용자의 장치는 %2 %1° 및 %4 %3° 의 방향으로 장착되어 있습니다.</translation>
    </message>
    <message>
        <source>down</source>
        <translation>아래로</translation>
    </message>
    <message>
        <source>up</source>
        <translation>위로</translation>
    </message>
    <message>
        <source>left</source>
        <translation>좌측으로</translation>
    </message>
    <message>
        <source>right</source>
        <translation>우측으로</translation>
    </message>
    <message>
        <source>Are you sure you want to reboot?</source>
        <translation>재부팅하시겠습니까?</translation>
    </message>
    <message>
        <source>Disengage to Reboot</source>
        <translation>재부팅하려면 연결을 해제하세요</translation>
    </message>
    <message>
        <source>Are you sure you want to power off?</source>
        <translation>전원을 끄시겠습니까?</translation>
    </message>
    <message>
        <source>Disengage to Power Off</source>
        <translation>전원을 끄려면 연결을 해제하세요</translation>
    </message>
    <message>
        <source>Reset</source>
        <translation>초기화</translation>
    </message>
    <message>
        <source>Review</source>
        <translation>다시보기</translation>
    </message>
    <message>
        <source>Pair your device with comma connect (connect.comma.ai) and claim your comma prime offer.</source>
        <translation>장치를 comma connect (connect.comma.ai)에서 동기화하고 comma prime 무료 이용권을 사용하세요.</translation>
    </message>
    <message>
        <source>Pair Device</source>
        <translation>장치 동기화</translation>
    </message>
    <message>
        <source>PAIR</source>
        <translation>동기화</translation>
    </message>
    <message>
        <source>Disengage to Reset Calibration</source>
        <translation>캘리브레이션을 재설정하려면 해제하세요</translation>
    </message>
    <message>
        <source>openpilot requires the device to be mounted within 4° left or right and within 5° up or 9° down.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Steering lag calibration is %1% complete.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Steering lag calibration is complete.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Steering torque response calibration is %1% complete.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Steering torque response calibration is complete.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>openpilot is continuously calibrating, resetting is rarely required. Resetting calibration will restart openpilot if the car is powered on.</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>DriverViewWindow</name>
    <message>
        <source>camera starting</source>
        <translation>카메라 시작 중</translation>
    </message>
</context>
<context>
    <name>ExperimentalModeButton</name>
    <message>
        <source>EXPERIMENTAL MODE ON</source>
        <translation>실험 모드 사용</translation>
    </message>
    <message>
        <source>CHILL MODE ON</source>
        <translation>안정 모드 사용</translation>
    </message>
</context>
<context>
    <name>FirehosePanel</name>
    <message>
        <source>openpilot learns to drive by watching humans, like you, drive.

Firehose Mode allows you to maximize your training data uploads to improve openpilot&apos;s driving models. More data means bigger models, which means better Experimental Mode.</source>
        <translation>오픈파일럿은 여러분과 같은 사람이 운전하는 모습을 보면서 운전하는 법을 배웁니다.

파이어호스 모드를 사용하면 학습 데이터 업로드를 최대화하여 오픈파일럿의 주행 모델을 개선할 수 있습니다. 더 많은 데이터는 더 큰 모델을 의미하며, 이는 더 나은 실험 모드를 의미합니다.</translation>
    </message>
    <message>
        <source>Firehose Mode: ACTIVE</source>
        <translation>파이어호스 모드: 활성화</translation>
    </message>
    <message>
        <source>ACTIVE</source>
        <translation>활성 상태</translation>
    </message>
    <message>
        <source>For maximum effectiveness, bring your device inside and connect to a good USB-C adapter and Wi-Fi weekly.&lt;br&gt;&lt;br&gt;Firehose Mode can also work while you&apos;re driving if connected to a hotspot or unlimited SIM card.&lt;br&gt;&lt;br&gt;&lt;br&gt;&lt;b&gt;Frequently Asked Questions&lt;/b&gt;&lt;br&gt;&lt;br&gt;&lt;i&gt;Does it matter how or where I drive?&lt;/i&gt; Nope, just drive as you normally would.&lt;br&gt;&lt;br&gt;&lt;i&gt;Do all of my segments get pulled in Firehose Mode?&lt;/i&gt; No, we selectively pull a subset of your segments.&lt;br&gt;&lt;br&gt;&lt;i&gt;What&apos;s a good USB-C adapter?&lt;/i&gt; Any fast phone or laptop charger should be fine.&lt;br&gt;&lt;br&gt;&lt;i&gt;Does it matter which software I run?&lt;/i&gt; Yes, only upstream openpilot (and particular forks) are able to be used for training.</source>
        <translation>최대한의 효과를 얻으려면 매주 장치를 실내로 가져와 좋은 USB-C 충전기와 Wi-Fi에 연결하세요.&lt;br&gt;&lt;br&gt;파이어호스 모드는 핫스팟 또는 무제한 네트워크에 연결된 경우 주행 중에도 작동할 수 있습니다.&lt;br&gt;&lt;br&gt;&lt;br&gt;&lt;b&gt;자주 묻는 질문&lt;/b&gt;&lt;br&gt;&lt;br&gt;&lt;i&gt;운전 방법이나 장소가 중요한가요?&lt;/i&gt; 아니요, 평소처럼 운전하시면 됩니다.&lt;br&gt;&lt;br&gt;&lt;i&gt;파이어호스 모드에서 제 모든 구간을 가져오나요?&lt;br&gt;&lt;br&gt;&lt;i&gt; 아니요, 저희는 여러분의 구간 중 일부를 선별적으로 가져옵니다.&lt;br&gt;&lt;br&gt;&lt;i&gt;좋은 USB-C 충전기는 무엇인가요?&lt;/i&gt; 휴대폰이나 노트북충전이 가능한 고속 충전기이면 괜찮습니다.&lt;br&gt;&lt;br&gt;&lt;i&gt;어떤 소프트웨어를 실행하는지가 중요한가요?&lt;/i&gt; 예, 오직 공식 오픈파일럿의 특정 포크만 트레이닝에 사용할 수 있습니다.</translation>
    </message>
    <message numerus="yes">
        <source>&lt;b&gt;%n segment(s)&lt;/b&gt; of your driving is in the training dataset so far.</source>
        <translation>
            <numerusform>&lt;b&gt;%n 구간&lt;/b&gt; 의 운전이 지금까지의 학습 데이터셋에 포함되어 있습니다.</numerusform>
        </translation>
    </message>
    <message>
        <source>&lt;span stylesheet=&apos;font-size: 60px; font-weight: bold; color: #e74c3c;&apos;&gt;INACTIVE&lt;/span&gt;: connect to an unmetered network</source>
        <translation>&lt;span stylesheet=&apos;font-size: 60px; font-weight: bold; color: #e74c3c;&apos;&gt;비활성 상태&lt;/span&gt;: 무제한 네트워크에 연결 하세요</translation>
    </message>
    <message>
        <source>Firehose Mode</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>HudRenderer</name>
    <message>
        <source>km/h</source>
        <translation>km/h</translation>
    </message>
    <message>
        <source>mph</source>
        <translation>mph</translation>
    </message>
    <message>
        <source>MAX</source>
        <translation>MAX</translation>
    </message>
</context>
<context>
    <name>InputDialog</name>
    <message>
        <source>Cancel</source>
        <translation>취소</translation>
    </message>
    <message numerus="yes">
        <source>Need at least %n character(s)!</source>
        <translation>
            <numerusform>최소 %n자 이상이어야 합니다!</numerusform>
        </translation>
    </message>
</context>
<context>
    <name>MultiOptionDialog</name>
    <message>
        <source>Select</source>
        <translation>선택</translation>
    </message>
    <message>
        <source>Cancel</source>
        <translation>취소</translation>
    </message>
</context>
<context>
    <name>Networking</name>
    <message>
        <source>Advanced</source>
        <translation>고급 설정</translation>
    </message>
    <message>
        <source>Enter password</source>
        <translation>비밀번호를 입력하세요</translation>
    </message>
    <message>
        <source>for &quot;%1&quot;</source>
        <translation>&quot;%1&quot;에 접속하려면 비밀번호가 필요합니다</translation>
    </message>
    <message>
        <source>Wrong password</source>
        <translation>비밀번호가 틀렸습니다</translation>
    </message>
</context>
<context>
    <name>OffroadAlert</name>
    <message>
        <source>Immediately connect to the internet to check for updates. If you do not connect to the internet, openpilot won&apos;t engage in %1</source>
        <translation>즉시 인터넷에 연결하여 업데이트를 확인하세요. 인터넷에 연결되어 있지 않으면 %1 이후에는 오픈파일럿이 활성화되지 않습니다.</translation>
    </message>
    <message>
        <source>Connect to internet to check for updates. openpilot won&apos;t automatically start until it connects to internet to check for updates.</source>
        <translation>업데이트 확인을 위해 인터넷 연결이 필요합니다. 오픈파일럿은 업데이트 확인을 위해 인터넷에 연결될 때까지 자동으로 시작되지 않습니다.</translation>
    </message>
    <message>
        <source>Unable to download updates
%1</source>
        <translation>업데이트를 다운로드할 수 없습니다
%1</translation>
    </message>
    <message>
        <source>Taking camera snapshots. System won&apos;t start until finished.</source>
        <translation>카메라 스냅샷 찍기가 완료될 때까지 시스템이 시작되지 않습니다.</translation>
    </message>
    <message>
        <source>An update to your device&apos;s operating system is downloading in the background. You will be prompted to update when it&apos;s ready to install.</source>
        <translation>백그라운드에서 운영 체제에 대한 업데이트가 다운로드되고 있습니다. 설치가 준비되면 업데이트 메시지가 표시됩니다.</translation>
    </message>
    <message>
        <source>Device failed to register. It will not connect to or upload to comma.ai servers, and receives no support from comma.ai. If this is an official device, visit https://comma.ai/support.</source>
        <translation>장치를 등록하지 못했습니다. comma.ai 서버에 연결하거나 데이터를 업로드하지 않으며 comma.ai에서 지원을 받지 않습니다. 공식 장치인 경우 https://comma.ai/support 에 방문하여 문의하세요.</translation>
    </message>
    <message>
        <source>NVMe drive not mounted.</source>
        <translation>NVMe 드라이브가 마운트되지 않았습니다.</translation>
    </message>
    <message>
        <source>Unsupported NVMe drive detected. Device may draw significantly more power and overheat due to the unsupported NVMe.</source>
        <translation>지원되지 않는 NVMe 드라이브가 감지되었습니다. 지원되지 않는 NVMe 드라이브는 많은 전력을 소비하고 장치를 과열시킬 수 있습니다.</translation>
    </message>
    <message>
        <source>openpilot was unable to identify your car. Your car is either unsupported or its ECUs are not recognized. Please submit a pull request to add the firmware versions to the proper vehicle. Need help? Join discord.comma.ai.</source>
        <translation>오픈파일럿이 차량을 식별할 수 없습니다. 지원되지 않는 차량이거나 ECU가 인식되지 않습니다. 해당 차량에 맞는 펌웨어 버전을 추가하려면 PR을 제출하세요. 도움이 필요하시면 discord.comma.ai에 참여하세요.</translation>
    </message>
    <message>
        <source>openpilot detected a change in the device&apos;s mounting position. Ensure the device is fully seated in the mount and the mount is firmly secured to the windshield.</source>
        <translation>오픈파일럿 장치의 장착 위치가 변경되었습니다. 장치가 마운트에 완전히 장착되고 마운트가 앞유리에 단단히 고정되었는지 확인하세요. </translation>
    </message>
    <message>
        <source>Device temperature too high. System cooling down before starting. Current internal component temperature: %1</source>
        <translation>장치 온도가 너무 높습니다. 시작하기 전에 시스템을 냉각하고 있습니다. 현재 내부 구성 요소 온도: %1</translation>
    </message>
</context>
<context>
    <name>OffroadHome</name>
    <message>
        <source>UPDATE</source>
        <translation>업데이트</translation>
    </message>
    <message>
        <source> ALERTS</source>
        <translation> 알림</translation>
    </message>
    <message>
        <source> ALERT</source>
        <translation> 알림</translation>
    </message>
</context>
<context>
    <name>OnroadAlerts</name>
    <message>
        <source>openpilot Unavailable</source>
        <translation>오픈파일럿을 사용할수없습니다</translation>
    </message>
    <message>
        <source>TAKE CONTROL IMMEDIATELY</source>
        <translation>핸들을 잡아주세요</translation>
    </message>
    <message>
        <source>Reboot Device</source>
        <translation>장치를 재부팅하세요</translation>
    </message>
    <message>
        <source>Waiting to start</source>
        <translation>시작을 기다리는중</translation>
    </message>
    <message>
        <source>System Unresponsive</source>
        <translation>시스템이 응답하지않습니다</translation>
    </message>
</context>
<context>
    <name>PairingPopup</name>
    <message>
        <source>Pair your device to your comma account</source>
        <translation>장치를 comma 계정에 동기화합니다</translation>
    </message>
    <message>
        <source>Go to https://connect.comma.ai on your phone</source>
        <translation>https://connect.comma.ai에 접속하세요</translation>
    </message>
    <message>
        <source>Click &quot;add new device&quot; and scan the QR code on the right</source>
        <translation>&quot;새 장치 추가&quot;를 클릭하고 오른쪽 QR 코드를 스캔하세요</translation>
    </message>
    <message>
        <source>Bookmark connect.comma.ai to your home screen to use it like an app</source>
        <translation>connect.comma.ai를 앱처럼 사용하려면 홈 화면에 바로가기를 만드세요</translation>
    </message>
    <message>
        <source>Please connect to Wi-Fi to complete initial pairing</source>
        <translation>초기 동기화를 완료하려면 Wi-Fi에 연결하세요.</translation>
    </message>
</context>
<context>
    <name>ParamControl</name>
    <message>
        <source>Cancel</source>
        <translation>취소</translation>
    </message>
    <message>
        <source>Enable</source>
        <translation>활성화</translation>
    </message>
</context>
<context>
    <name>PrimeAdWidget</name>
    <message>
        <source>Upgrade Now</source>
        <translation>지금 업그레이드하세요</translation>
    </message>
    <message>
        <source>Become a comma prime member at connect.comma.ai</source>
        <translation>connect.comma.ai에서 comma prime 사용자로 등록하세요</translation>
    </message>
    <message>
        <source>PRIME FEATURES:</source>
        <translation>PRIME 기능:</translation>
    </message>
    <message>
        <source>Remote access</source>
        <translation>원격 접속</translation>
    </message>
    <message>
        <source>24/7 LTE connectivity</source>
        <translation>항상 LTE 연결</translation>
    </message>
    <message>
        <source>1 year of drive storage</source>
        <translation>1년간 주행 로그 저장</translation>
    </message>
    <message>
        <source>Remote snapshots</source>
        <translation>원격 스냅샷</translation>
    </message>
</context>
<context>
    <name>PrimeUserWidget</name>
    <message>
        <source>✓ SUBSCRIBED</source>
        <translation>✓ 구독함</translation>
    </message>
    <message>
        <source>comma prime</source>
        <translation>comma prime</translation>
    </message>
</context>
<context>
    <name>QObject</name>
    <message>
        <source>openpilot</source>
        <translation>오픈파일럿</translation>
    </message>
    <message numerus="yes">
        <source>%n minute(s) ago</source>
        <translation>
            <numerusform>%n 분 전</numerusform>
        </translation>
    </message>
    <message numerus="yes">
        <source>%n hour(s) ago</source>
        <translation>
            <numerusform>%n 시간 전</numerusform>
        </translation>
    </message>
    <message numerus="yes">
        <source>%n day(s) ago</source>
        <translation>
            <numerusform>%n 일 전</numerusform>
        </translation>
    </message>
    <message>
        <source>now</source>
        <translation>now</translation>
    </message>
</context>
<context>
    <name>Reset</name>
    <message>
        <source>Reset failed. Reboot to try again.</source>
        <translation>초기화 실패. 재부팅 후 다시 시도하세요.</translation>
    </message>
    <message>
        <source>Are you sure you want to reset your device?</source>
        <translation>장치를 초기화하시겠습니까?</translation>
    </message>
    <message>
        <source>System Reset</source>
        <translation>장치 초기화</translation>
    </message>
    <message>
        <source>Cancel</source>
        <translation>취소</translation>
    </message>
    <message>
        <source>Reboot</source>
        <translation>재부팅</translation>
    </message>
    <message>
        <source>Confirm</source>
        <translation>확인</translation>
    </message>
    <message>
        <source>Unable to mount data partition. Partition may be corrupted. Press confirm to erase and reset your device.</source>
        <translation>데이터 파티션을 마운트할 수 없습니다. 파티션이 손상되었을 수 있습니다. 모든 설정을 삭제하고 장치를 초기화하려면 확인을 누르세요.</translation>
    </message>
    <message>
        <source>Resetting device...
This may take up to a minute.</source>
        <translation>장치를 초기화하는 중...
최대 1분이 소요될 수 있습니다.</translation>
    </message>
    <message>
        <source>System reset triggered. Press confirm to erase all content and settings. Press cancel to resume boot.</source>
        <translation>시스템 재설정이 시작되었습니다. 모든 콘텐츠와 설정을 지우려면 확인을 누르시고 부팅을 재개하려면 취소를 누르세요.</translation>
    </message>
</context>
<context>
    <name>SettingsWindow</name>
    <message>
        <source>×</source>
        <translation>×</translation>
    </message>
    <message>
        <source>Device</source>
        <translation>장치</translation>
    </message>
    <message>
        <source>Network</source>
        <translation>네트워크</translation>
    </message>
    <message>
        <source>Toggles</source>
        <translation>토글</translation>
    </message>
    <message>
        <source>Software</source>
        <translation>소프트웨어</translation>
    </message>
    <message>
        <source>Developer</source>
        <translation>개발자</translation>
    </message>
    <message>
        <source>Firehose</source>
        <translation>파이어호스</translation>
    </message>
</context>
<context>
    <name>Setup</name>
    <message>
        <source>WARNING: Low Voltage</source>
        <translation>경고: 전압이 낮습니다</translation>
    </message>
    <message>
        <source>Power your device in a car with a harness or proceed at your own risk.</source>
        <translation>장치를 하네스를 통해 차량 전원에 연결하세요. USB 전원에서는 예상치 못한 문제가 생길 수 있습니다.</translation>
    </message>
    <message>
        <source>Power off</source>
        <translation>전원 끄기</translation>
    </message>
    <message>
        <source>Continue</source>
        <translation>계속</translation>
    </message>
    <message>
        <source>Getting Started</source>
        <translation>시작하기</translation>
    </message>
    <message>
        <source>Before we get on the road, let’s finish installation and cover some details.</source>
        <translation>출발 전 설정을 완료하고 세부 사항을 살펴봅니다.</translation>
    </message>
    <message>
        <source>Connect to Wi-Fi</source>
        <translation>Wi-Fi 연결</translation>
    </message>
    <message>
        <source>Back</source>
        <translation>뒤로</translation>
    </message>
    <message>
        <source>Continue without Wi-Fi</source>
        <translation>Wi-Fi 연결 없이 진행</translation>
    </message>
    <message>
        <source>Waiting for internet</source>
        <translation>인터넷 연결 대기 중</translation>
    </message>
    <message>
        <source>Enter URL</source>
        <translation>URL 입력</translation>
    </message>
    <message>
        <source>for Custom Software</source>
        <translation>커스텀 소프트웨어</translation>
    </message>
    <message>
        <source>Downloading...</source>
        <translation>다운로드 중...</translation>
    </message>
    <message>
        <source>Download Failed</source>
        <translation>다운로드 실패</translation>
    </message>
    <message>
        <source>Ensure the entered URL is valid, and the device’s internet connection is good.</source>
        <translation>입력된 URL이 유효하고 인터넷 연결이 원활한지 확인하세요.</translation>
    </message>
    <message>
        <source>Reboot device</source>
        <translation>장치 재부팅</translation>
    </message>
    <message>
        <source>Start over</source>
        <translation>다시 시작</translation>
    </message>
    <message>
        <source>Something went wrong. Reboot the device.</source>
        <translation>문제가 발생했습니다. 장치를 재부팅하세요.</translation>
    </message>
    <message>
        <source>No custom software found at this URL.</source>
        <translation>이 URL에서 커스텀 소프트웨어를 찾을 수 없습니다.</translation>
    </message>
    <message>
        <source>Select a language</source>
        <translation>언어를 선택하세요</translation>
    </message>
    <message>
        <source>Choose Software to Install</source>
        <translation>설치할 소프트웨어 선택</translation>
    </message>
    <message>
        <source>openpilot</source>
        <translation>오픈파일럿</translation>
    </message>
    <message>
        <source>Custom Software</source>
        <translation>커스텀 소프트웨어</translation>
    </message>
    <message>
        <source>WARNING: Custom Software</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Use caution when installing third-party software. Third-party software has not been tested by comma, and may cause damage to your device and/or vehicle.

If you&apos;d like to proceed, use https://flash.comma.ai to restore your device to a factory state later.</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>SetupWidget</name>
    <message>
        <source>Finish Setup</source>
        <translation>설정 완료</translation>
    </message>
    <message>
        <source>Pair your device with comma connect (connect.comma.ai) and claim your comma prime offer.</source>
        <translation>장치를 comma connect (connect.comma.ai)에서 동기화하고 comma prime 무료 이용권을 사용하세요.</translation>
    </message>
    <message>
        <source>Pair device</source>
        <translation>장치 동기화</translation>
    </message>
</context>
<context>
    <name>Sidebar</name>
    <message>
        <source>CONNECT</source>
        <translation>커넥트</translation>
    </message>
    <message>
        <source>OFFLINE</source>
        <translation>연결 안됨</translation>
    </message>
    <message>
        <source>ONLINE</source>
        <translation>온라인</translation>
    </message>
    <message>
        <source>ERROR</source>
        <translation>오류</translation>
    </message>
    <message>
        <source>TEMP</source>
        <translation>온도</translation>
    </message>
    <message>
        <source>HIGH</source>
        <translation>높음</translation>
    </message>
    <message>
        <source>GOOD</source>
        <translation>좋음</translation>
    </message>
    <message>
        <source>OK</source>
        <translation>OK</translation>
    </message>
    <message>
        <source>VEHICLE</source>
        <translation>차량</translation>
    </message>
    <message>
        <source>NO</source>
        <translation>NO</translation>
    </message>
    <message>
        <source>PANDA</source>
        <translation>PANDA</translation>
    </message>
    <message>
        <source>--</source>
        <translation>--</translation>
    </message>
    <message>
        <source>Wi-Fi</source>
        <translation>Wi-Fi</translation>
    </message>
    <message>
        <source>ETH</source>
        <translation>LAN</translation>
    </message>
    <message>
        <source>2G</source>
        <translation>2G</translation>
    </message>
    <message>
        <source>3G</source>
        <translation>3G</translation>
    </message>
    <message>
        <source>LTE</source>
        <translation>LTE</translation>
    </message>
    <message>
        <source>5G</source>
        <translation>5G</translation>
    </message>
</context>
<context>
    <name>SoftwarePanel</name>
    <message>
        <source>Updates are only downloaded while the car is off.</source>
        <translation>업데이트는 차량 시동이 꺼졌을 때 다운로드됩니다.</translation>
    </message>
    <message>
        <source>Current Version</source>
        <translation>현재 버전</translation>
    </message>
    <message>
        <source>Download</source>
        <translation>다운로드</translation>
    </message>
    <message>
        <source>Install Update</source>
        <translation>업데이트 설치</translation>
    </message>
    <message>
        <source>INSTALL</source>
        <translation>설치</translation>
    </message>
    <message>
        <source>Target Branch</source>
        <translation>대상 브랜치</translation>
    </message>
    <message>
        <source>SELECT</source>
        <translation>선택</translation>
    </message>
    <message>
        <source>Select a branch</source>
        <translation>브랜치 선택</translation>
    </message>
    <message>
        <source>UNINSTALL</source>
        <translation>제거</translation>
    </message>
    <message>
        <source>Uninstall %1</source>
        <translation>%1 제거</translation>
    </message>
    <message>
        <source>Are you sure you want to uninstall?</source>
        <translation>제거하시겠습니까?</translation>
    </message>
    <message>
        <source>CHECK</source>
        <translation>확인</translation>
    </message>
    <message>
        <source>Uninstall</source>
        <translation>제거</translation>
    </message>
    <message>
        <source>failed to check for update</source>
        <translation>업데이트를 확인하지 못했습니다</translation>
    </message>
    <message>
        <source>up to date, last checked %1</source>
        <translation>최신 버전입니다. 마지막 확인: %1</translation>
    </message>
    <message>
        <source>DOWNLOAD</source>
        <translation>다운로드</translation>
    </message>
    <message>
        <source>update available</source>
        <translation>업데이트 가능</translation>
    </message>
    <message>
        <source>never</source>
        <translation>업데이트 안함</translation>
    </message>
</context>
<context>
    <name>SshControl</name>
    <message>
        <source>SSH Keys</source>
        <translation>SSH 키</translation>
    </message>
    <message>
        <source>Warning: This grants SSH access to all public keys in your GitHub settings. Never enter a GitHub username other than your own. A comma employee will NEVER ask you to add their GitHub username.</source>
        <translation>경고: 이 설정은 GitHub에 등록된 모든 공용 키에 대해 SSH 액세스 권한을 부여합니다. 본인의 GitHub 사용자 아이디 이외에는 입력하지 마십시오. comma에서는 GitHub 아이디를 추가하라는 요청을 하지 않습니다.</translation>
    </message>
    <message>
        <source>ADD</source>
        <translation>추가</translation>
    </message>
    <message>
        <source>Enter your GitHub username</source>
        <translation>GitHub 사용자 ID</translation>
    </message>
    <message>
        <source>LOADING</source>
        <translation>로딩 중</translation>
    </message>
    <message>
        <source>REMOVE</source>
        <translation>삭제</translation>
    </message>
    <message>
        <source>Username &apos;%1&apos; has no keys on GitHub</source>
        <translation>사용자 &apos;%1&apos;의 GitHub에 키가 등록되어 있지 않습니다</translation>
    </message>
    <message>
        <source>Request timed out</source>
        <translation>요청 시간 초과</translation>
    </message>
    <message>
        <source>Username &apos;%1&apos; doesn&apos;t exist on GitHub</source>
        <translation>GitHub 사용자 &apos;%1&apos;를 찾지 못했습니다</translation>
    </message>
</context>
<context>
    <name>SshToggle</name>
    <message>
        <source>Enable SSH</source>
        <translation>SSH 사용</translation>
    </message>
</context>
<context>
    <name>TermsPage</name>
    <message>
        <source>Decline</source>
        <translation>거절</translation>
    </message>
    <message>
        <source>Agree</source>
        <translation>동의</translation>
    </message>
    <message>
        <source>Welcome to openpilot</source>
        <translation>오픈파일럿에 오신 것을 환영합니다.</translation>
    </message>
    <message>
        <source>You must accept the Terms and Conditions to use openpilot. Read the latest terms at &lt;span style=&apos;color: #465BEA;&apos;&gt;https://comma.ai/terms&lt;/span&gt; before continuing.</source>
        <translation>오픈파일럿을 사용하려면 이용약관에 동의해야 합니다. 최신 약관은 &lt;span style=&apos;color: #465BEA;&apos;&gt;https://comma.ai/terms&lt;/span&gt; 에서 최신 약관을 읽은 후 계속하세요.</translation>
    </message>
</context>
<context>
    <name>TogglesPanel</name>
    <message>
        <source>Enable openpilot</source>
        <translation>오픈파일럿 사용</translation>
    </message>
    <message>
        <source>Enable Lane Departure Warnings</source>
        <translation>차선 이탈 경고 활성화</translation>
    </message>
    <message>
        <source>Receive alerts to steer back into the lane when your vehicle drifts over a detected lane line without a turn signal activated while driving over 31 mph (50 km/h).</source>
        <translation>차량이 50km/h(31mph) 이상의 속도로 주행할 때 방향지시등이 켜지지 않은 상태에서 차선을 벗어나면 경고합니다.</translation>
    </message>
    <message>
        <source>Use Metric System</source>
        <translation>미터법 사용</translation>
    </message>
    <message>
        <source>Display speed in km/h instead of mph.</source>
        <translation>mph 대신 km/h로 속도를 표시합니다.</translation>
    </message>
    <message>
        <source>Record and Upload Driver Camera</source>
        <translation>운전자 카메라 녹화 및 업로드</translation>
    </message>
    <message>
        <source>Upload data from the driver facing camera and help improve the driver monitoring algorithm.</source>
        <translation>운전자 카메라의 영상 데이터를 업로드하여 운전자 모니터링 알고리즘을 개선합니다.</translation>
    </message>
    <message>
        <source>Disengage on Accelerator Pedal</source>
        <translation>가속페달 조작 시 해제</translation>
    </message>
    <message>
        <source>When enabled, pressing the accelerator pedal will disengage openpilot.</source>
        <translation>활성화된 경우 가속 페달을 밟으면 오픈파일럿이 해제됩니다.</translation>
    </message>
    <message>
        <source>Experimental Mode</source>
        <translation>실험 모드</translation>
    </message>
    <message>
        <source>openpilot defaults to driving in &lt;b&gt;chill mode&lt;/b&gt;. Experimental mode enables &lt;b&gt;alpha-level features&lt;/b&gt; that aren&apos;t ready for chill mode. Experimental features are listed below:</source>
        <translation>오픈파일럿은 기본적으로 &lt;b&gt;안정 모드&lt;/b&gt;로 주행합니다. 실험 모드는 안정화되지 않은 &lt;b&gt;알파 수준의 기능&lt;/b&gt;을 활성화합니다. 실험 모드의 기능은 아래와 같습니다:</translation>
    </message>
    <message>
        <source>Let the driving model control the gas and brakes. openpilot will drive as it thinks a human would, including stopping for red lights and stop signs. Since the driving model decides the speed to drive, the set speed will only act as an upper bound. This is an alpha quality feature; mistakes should be expected.</source>
        <translation>주행모델이 가감속을 제어하도록 합니다. 오픈파일럿은 빨간불과 정지신호를 보고 정지하는것을 포함하여 사람이 운전하는 방식대로 작동하며 주행 모델이 속도를 결정하므로 설정 속도는 최대 제한 속도로만 작동합니다. 이는 알파 수준의 기능이며 오류가 발생할수있으니 사용에 주의해야 합니다.</translation>
    </message>
    <message>
        <source>New Driving Visualization</source>
        <translation>새로운 주행 시각화</translation>
    </message>
    <message>
        <source>Experimental mode is currently unavailable on this car since the car&apos;s stock ACC is used for longitudinal control.</source>
        <translation>차량의 ACC가 가감속 제어에 사용되기 때문에, 이 차량에서는 실험 모드를 사용할 수 없습니다.</translation>
    </message>
    <message>
        <source>openpilot longitudinal control may come in a future update.</source>
        <translation>오픈파일럿 가감속 제어는 향후 업데이트에서 지원될 수 있습니다.</translation>
    </message>
    <message>
        <source>Aggressive</source>
        <translation>공격적</translation>
    </message>
    <message>
        <source>Standard</source>
        <translation>표준</translation>
    </message>
    <message>
        <source>Relaxed</source>
        <translation>편안한</translation>
    </message>
    <message>
        <source>Driving Personality</source>
        <translation>주행 모드</translation>
    </message>
    <message>
        <source>An alpha version of openpilot longitudinal control can be tested, along with Experimental mode, on non-release branches.</source>
        <translation>오픈파일럿 가감속 제어 알파 버전은 비 릴리즈 브랜치에서 실험 모드와 함께 테스트할 수 있습니다.</translation>
    </message>
    <message>
        <source>Enable the openpilot longitudinal control (alpha) toggle to allow Experimental mode.</source>
        <translation>실험 모드를 사용하려면 오픈파일럿 E2E 가감속 제어 (알파) 토글을 활성화하세요.</translation>
    </message>
    <message>
        <source>End-to-End Longitudinal Control</source>
        <translation>E2E 가감속 제어</translation>
    </message>
    <message>
        <source>Standard is recommended. In aggressive mode, openpilot will follow lead cars closer and be more aggressive with the gas and brake. In relaxed mode openpilot will stay further away from lead cars. On supported cars, you can cycle through these personalities with your steering wheel distance button.</source>
        <translation>표준 모드를 권장합니다. 공격적 모드의 오픈파일럿은 선두 차량을 더 가까이 따라가고 가감속제어를 사용하여 더욱 공격적으로 움직입니다. 편안한 모드의 오픈파일럿은 선두 차량으로부터 더 멀리 떨어져 있습니다. 지원되는 차량에서는 차간거리 버튼을 사용하여 이러한 특성을 순환할 수 있습니다.</translation>
    </message>
    <message>
        <source>The driving visualization will transition to the road-facing wide-angle camera at low speeds to better show some turns. The Experimental mode logo will also be shown in the top right corner.</source>
        <translation>운전 시각화는 일부 회전을 더 잘 보여주기 위해 저속에서 도로를 향한 광각 카메라로 전환됩니다. 우측 상단에 실험 모드 로고가 표시됩니다.</translation>
    </message>
    <message>
        <source>Always-On Driver Monitoring</source>
        <translation>상시 운전자 모니터링</translation>
    </message>
    <message>
        <source>Enable driver monitoring even when openpilot is not engaged.</source>
        <translation>오픈파일럿이 활성화되지 않은 경우에도 드라이버 모니터링을 활성화합니다.</translation>
    </message>
    <message>
        <source>Use the openpilot system for adaptive cruise control and lane keep driver assistance. Your attention is required at all times to use this feature.</source>
        <translation>ACC 및 차선 유지 지원을 위해 오픈파일럿 시스템을 사용하십시오. 이 기능을 사용하려면 항상주의를 기울여야합니다.</translation>
    </message>
    <message>
        <source> Changing this setting will restart openpilot if the car is powered on.</source>
        <translation> 이 설정을 변경하면 차량이 재가동된후 오픈파일럿이 시작됩니다.</translation>
    </message>
</context>
<context>
    <name>Updater</name>
    <message>
        <source>Update Required</source>
        <translation>업데이트 필요</translation>
    </message>
    <message>
        <source>An operating system update is required. Connect your device to Wi-Fi for the fastest update experience. The download size is approximately 1GB.</source>
        <translation>OS 업데이트가 필요합니다. 장치를 Wi-Fi에 연결하면 가장 빠르게 업데이트할 수 있습니다. 다운로드 크기는 약 1GB입니다.</translation>
    </message>
    <message>
        <source>Connect to Wi-Fi</source>
        <translation>Wi-Fi 연결</translation>
    </message>
    <message>
        <source>Install</source>
        <translation>설치</translation>
    </message>
    <message>
        <source>Back</source>
        <translation>뒤로</translation>
    </message>
    <message>
        <source>Loading...</source>
        <translation>로딩 중...</translation>
    </message>
    <message>
        <source>Reboot</source>
        <translation>재부팅</translation>
    </message>
    <message>
        <source>Update failed</source>
        <translation>업데이트 실패</translation>
    </message>
</context>
<context>
    <name>WiFiPromptWidget</name>
    <message>
        <source>Open</source>
        <translation>열기</translation>
    </message>
    <message>
        <source>Maximize your training data uploads to improve openpilot&apos;s driving models.</source>
        <translation>오픈파일럿의 주행 모델 개선을 위해 학습 데이터 업로드를 최대화하세요.</translation>
    </message>
    <message>
        <source>&lt;span style=&apos;font-family: &quot;Noto Color Emoji&quot;;&apos;&gt;🔥&lt;/span&gt; Firehose Mode &lt;span style=&apos;font-family: Noto Color Emoji;&apos;&gt;🔥&lt;/span&gt;</source>
        <translation>&lt;span style=&apos;font-family: &quot;Noto Color Emoji&quot;;&apos;&gt;🔥&lt;/span&gt; 파이어호스 모드 &lt;span style=&apos;font-family: Noto Color Emoji;&apos;&gt;🔥&lt;/span&gt;</translation>
    </message>
</context>
<context>
    <name>WifiUI</name>
    <message>
        <source>Scanning for networks...</source>
        <translation>네트워크 검색 중...</translation>
    </message>
    <message>
        <source>CONNECTING...</source>
        <translation>연결 중...</translation>
    </message>
    <message>
        <source>FORGET</source>
        <translation>삭제</translation>
    </message>
    <message>
        <source>Forget Wi-Fi Network &quot;%1&quot;?</source>
        <translation>Wi-Fi &quot;%1&quot;에 자동으로 연결하지 않겠습니까?</translation>
    </message>
    <message>
        <source>Forget</source>
        <translation>삭제</translation>
    </message>
</context>
</TS>
