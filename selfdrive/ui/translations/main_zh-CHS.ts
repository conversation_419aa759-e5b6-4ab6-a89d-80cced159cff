<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE TS>
<TS version="2.1" language="zh_CN">
<context>
    <name>AbstractAlert</name>
    <message>
        <source>Close</source>
        <translation>关闭</translation>
    </message>
    <message>
        <source>Snooze Update</source>
        <translation>暂停更新</translation>
    </message>
    <message>
        <source>Reboot and Update</source>
        <translation>重启并更新</translation>
    </message>
</context>
<context>
    <name>AdvancedNetworking</name>
    <message>
        <source>Back</source>
        <translation>返回</translation>
    </message>
    <message>
        <source>Enable Tethering</source>
        <translation>启用WiFi热点</translation>
    </message>
    <message>
        <source>Tethering Password</source>
        <translation>WiFi热点密码</translation>
    </message>
    <message>
        <source>EDIT</source>
        <translation>编辑</translation>
    </message>
    <message>
        <source>Enter new tethering password</source>
        <translation>输入新的WiFi热点密码</translation>
    </message>
    <message>
        <source>IP Address</source>
        <translation>IP 地址</translation>
    </message>
    <message>
        <source>Enable Roaming</source>
        <translation>启用数据漫游</translation>
    </message>
    <message>
        <source>APN Setting</source>
        <translation>APN 设置</translation>
    </message>
    <message>
        <source>Enter APN</source>
        <translation>输入 APN</translation>
    </message>
    <message>
        <source>leave blank for automatic configuration</source>
        <translation>留空以自动配置</translation>
    </message>
    <message>
        <source>Cellular Metered</source>
        <translation>按流量计费的手机移动网络</translation>
    </message>
    <message>
        <source>Hidden Network</source>
        <translation>隐藏的网络</translation>
    </message>
    <message>
        <source>CONNECT</source>
        <translation>连线</translation>
    </message>
    <message>
        <source>Enter SSID</source>
        <translation>输入 SSID</translation>
    </message>
    <message>
        <source>Enter password</source>
        <translation>输入密码</translation>
    </message>
    <message>
        <source>for &quot;%1&quot;</source>
        <translation>网络名称：&quot;%1&quot;</translation>
    </message>
    <message>
        <source>Prevent large data uploads when on a metered cellular connection</source>
        <translation>在按流量计费的移动网络上，防止上传大数据</translation>
    </message>
    <message>
        <source>default</source>
        <translation>默认</translation>
    </message>
    <message>
        <source>metered</source>
        <translation>按流量计费</translation>
    </message>
    <message>
        <source>unmetered</source>
        <translation>不按流量计费</translation>
    </message>
    <message>
        <source>Wi-Fi Network Metered</source>
        <translation>按流量计费的 WLAN 网络</translation>
    </message>
    <message>
        <source>Prevent large data uploads when on a metered Wi-Fi connection</source>
        <translation>在按流量计费的 WLAN 网络上，防止上传大数据</translation>
    </message>
</context>
<context>
    <name>ConfirmationDialog</name>
    <message>
        <source>Ok</source>
        <translation>好的</translation>
    </message>
    <message>
        <source>Cancel</source>
        <translation>取消</translation>
    </message>
</context>
<context>
    <name>DeclinePage</name>
    <message>
        <source>You must accept the Terms and Conditions in order to use openpilot.</source>
        <translation>您必须接受条款和条件以使用openpilot。</translation>
    </message>
    <message>
        <source>Back</source>
        <translation>返回</translation>
    </message>
    <message>
        <source>Decline, uninstall %1</source>
        <translation>拒绝并卸载%1</translation>
    </message>
</context>
<context>
    <name>DeveloperPanel</name>
    <message>
        <source>Joystick Debug Mode</source>
        <translation>摇杆调试模式</translation>
    </message>
    <message>
        <source>Longitudinal Maneuver Mode</source>
        <translation>纵向操控测试模式</translation>
    </message>
    <message>
        <source>openpilot Longitudinal Control (Alpha)</source>
        <translation>openpilot纵向控制（Alpha 版）</translation>
    </message>
    <message>
        <source>WARNING: openpilot longitudinal control is in alpha for this car and will disable Automatic Emergency Braking (AEB).</source>
        <translation>警告：此车辆的 openpilot 纵向控制功能目前处于Alpha版本，使用此功能将会停用自动紧急制动（AEB）功能。</translation>
    </message>
    <message>
        <source>On this car, openpilot defaults to the car&apos;s built-in ACC instead of openpilot&apos;s longitudinal control. Enable this to switch to openpilot longitudinal control. Enabling Experimental mode is recommended when enabling openpilot longitudinal control alpha.</source>
        <translation>在这辆车上，openpilot 默认使用车辆内建的主动巡航控制（ACC），而非 openpilot 的纵向控制。启用此项功能可切换至 openpilot 的纵向控制。当启用 openpilot 纵向控制 Alpha 版本时，建议同时启用实验性模式（Experimental mode）。</translation>
    </message>
    <message>
        <source>Enable ADB</source>
        <translation>启用 ADB</translation>
    </message>
    <message>
        <source>ADB (Android Debug Bridge) allows connecting to your device over USB or over the network. See https://docs.comma.ai/how-to/connect-to-comma for more info.</source>
        <translation>ADB（Android调试桥接）允许通过USB或网络连接到您的设备。更多信息请参见 [https://docs.comma.ai/how-to/connect-to-comma](https://docs.comma.ai/how-to/connect-to-comma)。</translation>
    </message>
</context>
<context>
    <name>DevicePanel</name>
    <message>
        <source>Dongle ID</source>
        <translation>设备ID（Dongle ID）</translation>
    </message>
    <message>
        <source>N/A</source>
        <translation>N/A</translation>
    </message>
    <message>
        <source>Serial</source>
        <translation>序列号</translation>
    </message>
    <message>
        <source>Driver Camera</source>
        <translation>驾驶员摄像头</translation>
    </message>
    <message>
        <source>PREVIEW</source>
        <translation>预览</translation>
    </message>
    <message>
        <source>Preview the driver facing camera to ensure that driver monitoring has good visibility. (vehicle must be off)</source>
        <translation>打开并预览驾驶员摄像头，以确保驾驶员监控具有良好视野。（仅熄火时可用）</translation>
    </message>
    <message>
        <source>Reset Calibration</source>
        <translation>重置设备校准</translation>
    </message>
    <message>
        <source>RESET</source>
        <translation>重置</translation>
    </message>
    <message>
        <source>Are you sure you want to reset calibration?</source>
        <translation>您确定要重置设备校准吗？</translation>
    </message>
    <message>
        <source>Review Training Guide</source>
        <translation>新手指南</translation>
    </message>
    <message>
        <source>REVIEW</source>
        <translation>查看</translation>
    </message>
    <message>
        <source>Review the rules, features, and limitations of openpilot</source>
        <translation>查看 openpilot 的使用规则，以及其功能和限制</translation>
    </message>
    <message>
        <source>Are you sure you want to review the training guide?</source>
        <translation>您确定要查看新手指南吗？</translation>
    </message>
    <message>
        <source>Regulatory</source>
        <translation>监管信息</translation>
    </message>
    <message>
        <source>VIEW</source>
        <translation>查看</translation>
    </message>
    <message>
        <source>Change Language</source>
        <translation>切换语言</translation>
    </message>
    <message>
        <source>CHANGE</source>
        <translation>切换</translation>
    </message>
    <message>
        <source>Select a language</source>
        <translation>选择语言</translation>
    </message>
    <message>
        <source>Reboot</source>
        <translation>重启</translation>
    </message>
    <message>
        <source>Power Off</source>
        <translation>关机</translation>
    </message>
    <message>
        <source> Your device is pointed %1° %2 and %3° %4.</source>
        <translation> 您的设备校准为%1° %2、%3° %4。</translation>
    </message>
    <message>
        <source>down</source>
        <translation>朝下</translation>
    </message>
    <message>
        <source>up</source>
        <translation>朝上</translation>
    </message>
    <message>
        <source>left</source>
        <translation>朝左</translation>
    </message>
    <message>
        <source>right</source>
        <translation>朝右</translation>
    </message>
    <message>
        <source>Are you sure you want to reboot?</source>
        <translation>您确定要重新启动吗？</translation>
    </message>
    <message>
        <source>Disengage to Reboot</source>
        <translation>取消openpilot以重新启动</translation>
    </message>
    <message>
        <source>Are you sure you want to power off?</source>
        <translation>您确定要关机吗？</translation>
    </message>
    <message>
        <source>Disengage to Power Off</source>
        <translation>取消openpilot以关机</translation>
    </message>
    <message>
        <source>Reset</source>
        <translation>重置</translation>
    </message>
    <message>
        <source>Review</source>
        <translation>预览</translation>
    </message>
    <message>
        <source>Pair your device with comma connect (connect.comma.ai) and claim your comma prime offer.</source>
        <translation>将您的设备与comma connect （connect.comma.ai）配对并领取您的comma prime优惠。</translation>
    </message>
    <message>
        <source>Pair Device</source>
        <translation>配对设备</translation>
    </message>
    <message>
        <source>PAIR</source>
        <translation>配对</translation>
    </message>
    <message>
        <source>Disengage to Reset Calibration</source>
        <translation>解除以重置校准</translation>
    </message>
    <message>
        <source>openpilot requires the device to be mounted within 4° left or right and within 5° up or 9° down.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Steering lag calibration is %1% complete.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Steering lag calibration is complete.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Steering torque response calibration is %1% complete.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Steering torque response calibration is complete.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>openpilot is continuously calibrating, resetting is rarely required. Resetting calibration will restart openpilot if the car is powered on.</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>DriverViewWindow</name>
    <message>
        <source>camera starting</source>
        <translation>正在启动相机</translation>
    </message>
</context>
<context>
    <name>ExperimentalModeButton</name>
    <message>
        <source>EXPERIMENTAL MODE ON</source>
        <translation>试验模式运行</translation>
    </message>
    <message>
        <source>CHILL MODE ON</source>
        <translation>轻松模式运行</translation>
    </message>
</context>
<context>
    <name>FirehosePanel</name>
    <message>
        <source>openpilot learns to drive by watching humans, like you, drive.

Firehose Mode allows you to maximize your training data uploads to improve openpilot&apos;s driving models. More data means bigger models, which means better Experimental Mode.</source>
        <translation>openpilot 通过观察人类驾驶（包括您）来学习如何驾驶。

“训练数据上传模式”允许您最大化上传训练数据，以改进 openpilot 的驾驶模型。更多数据意味着更强大的模型，也就意味着更优秀的“实验模式”。</translation>
    </message>
    <message>
        <source>Firehose Mode: ACTIVE</source>
        <translation>训练数据上传模式：激活中</translation>
    </message>
    <message>
        <source>ACTIVE</source>
        <translation>激活中</translation>
    </message>
    <message>
        <source>For maximum effectiveness, bring your device inside and connect to a good USB-C adapter and Wi-Fi weekly.&lt;br&gt;&lt;br&gt;Firehose Mode can also work while you&apos;re driving if connected to a hotspot or unlimited SIM card.&lt;br&gt;&lt;br&gt;&lt;br&gt;&lt;b&gt;Frequently Asked Questions&lt;/b&gt;&lt;br&gt;&lt;br&gt;&lt;i&gt;Does it matter how or where I drive?&lt;/i&gt; Nope, just drive as you normally would.&lt;br&gt;&lt;br&gt;&lt;i&gt;Do all of my segments get pulled in Firehose Mode?&lt;/i&gt; No, we selectively pull a subset of your segments.&lt;br&gt;&lt;br&gt;&lt;i&gt;What&apos;s a good USB-C adapter?&lt;/i&gt; Any fast phone or laptop charger should be fine.&lt;br&gt;&lt;br&gt;&lt;i&gt;Does it matter which software I run?&lt;/i&gt; Yes, only upstream openpilot (and particular forks) are able to be used for training.</source>
        <translation>为了达到最佳效果，请每周将您的设备带回室内，并连接到优质的 USB-C 充电器和 Wi-Fi。&lt;br&gt;&lt;br&gt;Firehose 模式在行驶时也能运行，但需连接到移动热点或使用不限流量的 SIM 卡。&lt;br&gt;&lt;br&gt;&lt;br&gt;&lt;b&gt;常见问题&lt;/b&gt;&lt;br&gt;&lt;br&gt;&lt;i&gt;我开车的方式或地点有影响吗？&lt;/i&gt;不会，请像平常一样驾驶即可。&lt;br&gt;&lt;br&gt;&lt;i&gt;Firehose 模式会上传所有的驾驶片段吗？&lt;/i&gt;不会，我们会选择性地上传部分片段。&lt;br&gt;&lt;br&gt;&lt;i&gt;什么是好的 USB-C 充电器？&lt;/i&gt;任何快速手机或笔记本电脑充电器都应该适用。&lt;br&gt;&lt;br&gt;&lt;i&gt;我使用的软件版本有影响吗？&lt;/i&gt;有的，只有官方 openpilot（以及特定的分支）可以用于训练。</translation>
    </message>
    <message numerus="yes">
        <source>&lt;b&gt;%n segment(s)&lt;/b&gt; of your driving is in the training dataset so far.</source>
        <translation>
            <numerusform>&lt;b&gt;目前已有 %n 段&lt;/b&gt; 您的驾驶数据被纳入训练数据集。</numerusform>
        </translation>
    </message>
    <message>
        <source>&lt;span stylesheet=&apos;font-size: 60px; font-weight: bold; color: #e74c3c;&apos;&gt;INACTIVE&lt;/span&gt;: connect to an unmetered network</source>
        <translation>&lt;span stylesheet=&apos;font-size: 60px; font-weight: bold; color: #e74c3c;&apos;&gt;闲置&lt;/span&gt;：请连接到不限流量的网络</translation>
    </message>
    <message>
        <source>Firehose Mode</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>HudRenderer</name>
    <message>
        <source>km/h</source>
        <translation>km/h</translation>
    </message>
    <message>
        <source>mph</source>
        <translation>mph</translation>
    </message>
    <message>
        <source>MAX</source>
        <translation>最高定速</translation>
    </message>
</context>
<context>
    <name>InputDialog</name>
    <message>
        <source>Cancel</source>
        <translation>取消</translation>
    </message>
    <message numerus="yes">
        <source>Need at least %n character(s)!</source>
        <translation>
            <numerusform>至少需要 %n 个字符！</numerusform>
        </translation>
    </message>
</context>
<context>
    <name>MultiOptionDialog</name>
    <message>
        <source>Select</source>
        <translation>选择</translation>
    </message>
    <message>
        <source>Cancel</source>
        <translation>取消</translation>
    </message>
</context>
<context>
    <name>Networking</name>
    <message>
        <source>Advanced</source>
        <translation>高级</translation>
    </message>
    <message>
        <source>Enter password</source>
        <translation>输入密码</translation>
    </message>
    <message>
        <source>for &quot;%1&quot;</source>
        <translation>网络名称：&quot;%1&quot;</translation>
    </message>
    <message>
        <source>Wrong password</source>
        <translation>密码错误</translation>
    </message>
</context>
<context>
    <name>OffroadAlert</name>
    <message>
        <source>Immediately connect to the internet to check for updates. If you do not connect to the internet, openpilot won&apos;t engage in %1</source>
        <translation>请立即连接网络检查更新。如果不连接网络，openpilot 将在 %1 后便无法使用</translation>
    </message>
    <message>
        <source>Connect to internet to check for updates. openpilot won&apos;t automatically start until it connects to internet to check for updates.</source>
        <translation>请连接至互联网以检查更新。在连接至互联网并完成更新检查之前，openpilot 将不会自动启动。</translation>
    </message>
    <message>
        <source>Unable to download updates
%1</source>
        <translation>无法下载更新
%1</translation>
    </message>
    <message>
        <source>Taking camera snapshots. System won&apos;t start until finished.</source>
        <translation>正在使用相机拍摄中。在完成之前，系统将无法启动。</translation>
    </message>
    <message>
        <source>An update to your device&apos;s operating system is downloading in the background. You will be prompted to update when it&apos;s ready to install.</source>
        <translation>一个针对您设备的操作系统更新正在后台下载中。当更新准备好安装时，您将收到提示进行更新。</translation>
    </message>
    <message>
        <source>Device failed to register. It will not connect to or upload to comma.ai servers, and receives no support from comma.ai. If this is an official device, visit https://comma.ai/support.</source>
        <translation>设备注册失败。它将无法连接或上传至 comma.ai 服务器，并且无法获得 comma.ai 的支持。如果这是一个官方设备，请访问 https://comma.ai/support。</translation>
    </message>
    <message>
        <source>NVMe drive not mounted.</source>
        <translation>NVMe固态硬盘未被挂载。</translation>
    </message>
    <message>
        <source>Unsupported NVMe drive detected. Device may draw significantly more power and overheat due to the unsupported NVMe.</source>
        <translation>检测到不支持的 NVMe 固态硬盘。您的设备因为使用了不支持的 NVMe 固态硬盘可能会消耗更多电力并更易过热。</translation>
    </message>
    <message>
        <source>openpilot was unable to identify your car. Your car is either unsupported or its ECUs are not recognized. Please submit a pull request to add the firmware versions to the proper vehicle. Need help? Join discord.comma.ai.</source>
        <translation>openpilot 无法识别您的车辆。您的车辆可能未被支持，或是其电控单元 (ECU) 未被识别。请提交一个 Pull Request 为您的车辆添加正确的固件版本。需要帮助吗？请加入 discord.comma.ai。</translation>
    </message>
    <message>
        <source>openpilot detected a change in the device&apos;s mounting position. Ensure the device is fully seated in the mount and the mount is firmly secured to the windshield.</source>
        <translation>openpilot 检测到设备的安装位置发生变化。请确保设备完全安装在支架上，并确保支架牢固地固定在挡风玻璃上。</translation>
    </message>
    <message>
        <source>Device temperature too high. System cooling down before starting. Current internal component temperature: %1</source>
        <translation>设备温度过高。系统正在冷却中，等冷却完毕后才会启动。目前内部组件温度：%1</translation>
    </message>
</context>
<context>
    <name>OffroadHome</name>
    <message>
        <source>UPDATE</source>
        <translation>更新</translation>
    </message>
    <message>
        <source> ALERTS</source>
        <translation> 警报</translation>
    </message>
    <message>
        <source> ALERT</source>
        <translation> 警报</translation>
    </message>
</context>
<context>
    <name>OnroadAlerts</name>
    <message>
        <source>openpilot Unavailable</source>
        <translation>无法使用 openpilot</translation>
    </message>
    <message>
        <source>TAKE CONTROL IMMEDIATELY</source>
        <translation>立即接管</translation>
    </message>
    <message>
        <source>Reboot Device</source>
        <translation>重启设备</translation>
    </message>
    <message>
        <source>Waiting to start</source>
        <translation>等待开始</translation>
    </message>
    <message>
        <source>System Unresponsive</source>
        <translation>系统无响应</translation>
    </message>
</context>
<context>
    <name>PairingPopup</name>
    <message>
        <source>Pair your device to your comma account</source>
        <translation>将您的设备与comma账号配对</translation>
    </message>
    <message>
        <source>Go to https://connect.comma.ai on your phone</source>
        <translation>在手机上访问 https://connect.comma.ai</translation>
    </message>
    <message>
        <source>Click &quot;add new device&quot; and scan the QR code on the right</source>
        <translation>点击“添加新设备”，扫描右侧二维码</translation>
    </message>
    <message>
        <source>Bookmark connect.comma.ai to your home screen to use it like an app</source>
        <translation>将 connect.comma.ai 收藏到您的主屏幕，以便像应用程序一样使用它</translation>
    </message>
    <message>
        <source>Please connect to Wi-Fi to complete initial pairing</source>
        <translation>请连接 Wi-Fi 以完成初始配对</translation>
    </message>
</context>
<context>
    <name>ParamControl</name>
    <message>
        <source>Cancel</source>
        <translation>取消</translation>
    </message>
    <message>
        <source>Enable</source>
        <translation>启用</translation>
    </message>
</context>
<context>
    <name>PrimeAdWidget</name>
    <message>
        <source>Upgrade Now</source>
        <translation>现在升级</translation>
    </message>
    <message>
        <source>Become a comma prime member at connect.comma.ai</source>
        <translation>打开connect.comma.ai以注册comma prime会员</translation>
    </message>
    <message>
        <source>PRIME FEATURES:</source>
        <translation>comma prime特权：</translation>
    </message>
    <message>
        <source>Remote access</source>
        <translation>远程访问</translation>
    </message>
    <message>
        <source>24/7 LTE connectivity</source>
        <translation>全天候 LTE 连接</translation>
    </message>
    <message>
        <source>1 year of drive storage</source>
        <translation>一年的行驶记录储存空间</translation>
    </message>
    <message>
        <source>Remote snapshots</source>
        <translation>远程快照</translation>
    </message>
</context>
<context>
    <name>PrimeUserWidget</name>
    <message>
        <source>✓ SUBSCRIBED</source>
        <translation>✓ 已订阅</translation>
    </message>
    <message>
        <source>comma prime</source>
        <translation>comma prime</translation>
    </message>
</context>
<context>
    <name>QObject</name>
    <message>
        <source>openpilot</source>
        <translation>openpilot</translation>
    </message>
    <message numerus="yes">
        <source>%n minute(s) ago</source>
        <translation>
            <numerusform>%n 分钟前</numerusform>
        </translation>
    </message>
    <message numerus="yes">
        <source>%n hour(s) ago</source>
        <translation>
            <numerusform>%n 小时前</numerusform>
        </translation>
    </message>
    <message numerus="yes">
        <source>%n day(s) ago</source>
        <translation>
            <numerusform>%n 天前</numerusform>
        </translation>
    </message>
    <message>
        <source>now</source>
        <translation>现在</translation>
    </message>
</context>
<context>
    <name>Reset</name>
    <message>
        <source>Reset failed. Reboot to try again.</source>
        <translation>重置失败。 重新启动以重试。</translation>
    </message>
    <message>
        <source>Are you sure you want to reset your device?</source>
        <translation>您确定要重置您的设备吗？</translation>
    </message>
    <message>
        <source>System Reset</source>
        <translation>恢复出厂设置</translation>
    </message>
    <message>
        <source>Cancel</source>
        <translation>取消</translation>
    </message>
    <message>
        <source>Reboot</source>
        <translation>重启</translation>
    </message>
    <message>
        <source>Confirm</source>
        <translation>确认</translation>
    </message>
    <message>
        <source>Unable to mount data partition. Partition may be corrupted. Press confirm to erase and reset your device.</source>
        <translation>无法挂载数据分区。分区可能已经损坏。请确认是否要删除并重新设置。</translation>
    </message>
    <message>
        <source>Resetting device...
This may take up to a minute.</source>
        <translation>设备重置中…
这可能需要一分钟的时间。</translation>
    </message>
    <message>
        <source>System reset triggered. Press confirm to erase all content and settings. Press cancel to resume boot.</source>
        <translation>系统重置已触发。按下“确认”以清除所有内容和设置，按下“取消”以继续启动。</translation>
    </message>
</context>
<context>
    <name>SettingsWindow</name>
    <message>
        <source>×</source>
        <translation>×</translation>
    </message>
    <message>
        <source>Device</source>
        <translation>设备</translation>
    </message>
    <message>
        <source>Network</source>
        <translation>网络</translation>
    </message>
    <message>
        <source>Toggles</source>
        <translation>设定</translation>
    </message>
    <message>
        <source>Software</source>
        <translation>软件</translation>
    </message>
    <message>
        <source>Developer</source>
        <translation>开发人员</translation>
    </message>
    <message>
        <source>Firehose</source>
        <translation>训练上传</translation>
    </message>
</context>
<context>
    <name>Setup</name>
    <message>
        <source>WARNING: Low Voltage</source>
        <translation>警告：低电压</translation>
    </message>
    <message>
        <source>Power your device in a car with a harness or proceed at your own risk.</source>
        <translation>请使用car harness线束为您的设备供电，或自行承担风险。</translation>
    </message>
    <message>
        <source>Power off</source>
        <translation>关机</translation>
    </message>
    <message>
        <source>Continue</source>
        <translation>继续</translation>
    </message>
    <message>
        <source>Getting Started</source>
        <translation>开始设置</translation>
    </message>
    <message>
        <source>Before we get on the road, let’s finish installation and cover some details.</source>
        <translation>开始旅程之前，让我们完成安装并介绍一些细节。</translation>
    </message>
    <message>
        <source>Connect to Wi-Fi</source>
        <translation>连接到WiFi</translation>
    </message>
    <message>
        <source>Back</source>
        <translation>返回</translation>
    </message>
    <message>
        <source>Continue without Wi-Fi</source>
        <translation>不连接WiFi并继续</translation>
    </message>
    <message>
        <source>Waiting for internet</source>
        <translation>等待网络连接</translation>
    </message>
    <message>
        <source>Enter URL</source>
        <translation>输入网址</translation>
    </message>
    <message>
        <source>for Custom Software</source>
        <translation>以下载自定义软件</translation>
    </message>
    <message>
        <source>Downloading...</source>
        <translation>正在下载……</translation>
    </message>
    <message>
        <source>Download Failed</source>
        <translation>下载失败</translation>
    </message>
    <message>
        <source>Ensure the entered URL is valid, and the device’s internet connection is good.</source>
        <translation>请确保互联网连接良好且输入的URL有效。</translation>
    </message>
    <message>
        <source>Reboot device</source>
        <translation>重启设备</translation>
    </message>
    <message>
        <source>Start over</source>
        <translation>重来</translation>
    </message>
    <message>
        <source>No custom software found at this URL.</source>
        <translation>在此网址找不到自定义软件。</translation>
    </message>
    <message>
        <source>Something went wrong. Reboot the device.</source>
        <translation>发生了一些错误。请重新启动您的设备。</translation>
    </message>
    <message>
        <source>Select a language</source>
        <translation>选择语言</translation>
    </message>
    <message>
        <source>Choose Software to Install</source>
        <translation>选择要安装的软件</translation>
    </message>
    <message>
        <source>openpilot</source>
        <translation>openpilot</translation>
    </message>
    <message>
        <source>Custom Software</source>
        <translation>定制软件</translation>
    </message>
    <message>
        <source>WARNING: Custom Software</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Use caution when installing third-party software. Third-party software has not been tested by comma, and may cause damage to your device and/or vehicle.

If you&apos;d like to proceed, use https://flash.comma.ai to restore your device to a factory state later.</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>SetupWidget</name>
    <message>
        <source>Finish Setup</source>
        <translation>完成设置</translation>
    </message>
    <message>
        <source>Pair your device with comma connect (connect.comma.ai) and claim your comma prime offer.</source>
        <translation>将您的设备与comma connect （connect.comma.ai）配对并领取您的comma prime优惠。</translation>
    </message>
    <message>
        <source>Pair device</source>
        <translation>配对设备</translation>
    </message>
</context>
<context>
    <name>Sidebar</name>
    <message>
        <source>CONNECT</source>
        <translation>CONNECT</translation>
    </message>
    <message>
        <source>OFFLINE</source>
        <translation>离线</translation>
    </message>
    <message>
        <source>ONLINE</source>
        <translation>在线</translation>
    </message>
    <message>
        <source>ERROR</source>
        <translation>连接出错</translation>
    </message>
    <message>
        <source>TEMP</source>
        <translation>设备温度</translation>
    </message>
    <message>
        <source>HIGH</source>
        <translation>过热</translation>
    </message>
    <message>
        <source>GOOD</source>
        <translation>良好</translation>
    </message>
    <message>
        <source>OK</source>
        <translation>一般</translation>
    </message>
    <message>
        <source>VEHICLE</source>
        <translation>车辆连接</translation>
    </message>
    <message>
        <source>NO</source>
        <translation>无</translation>
    </message>
    <message>
        <source>PANDA</source>
        <translation>PANDA</translation>
    </message>
    <message>
        <source>--</source>
        <translation>--</translation>
    </message>
    <message>
        <source>Wi-Fi</source>
        <translation>Wi-Fi</translation>
    </message>
    <message>
        <source>ETH</source>
        <translation>以太网</translation>
    </message>
    <message>
        <source>2G</source>
        <translation>2G</translation>
    </message>
    <message>
        <source>3G</source>
        <translation>3G</translation>
    </message>
    <message>
        <source>LTE</source>
        <translation>LTE</translation>
    </message>
    <message>
        <source>5G</source>
        <translation>5G</translation>
    </message>
</context>
<context>
    <name>SoftwarePanel</name>
    <message>
        <source>Updates are only downloaded while the car is off.</source>
        <translation>车辆熄火时才能下载升级文件。</translation>
    </message>
    <message>
        <source>Current Version</source>
        <translation>当前版本</translation>
    </message>
    <message>
        <source>Download</source>
        <translation>下载</translation>
    </message>
    <message>
        <source>Install Update</source>
        <translation>安装更新</translation>
    </message>
    <message>
        <source>INSTALL</source>
        <translation>安装</translation>
    </message>
    <message>
        <source>Target Branch</source>
        <translation>目标分支</translation>
    </message>
    <message>
        <source>SELECT</source>
        <translation>选择</translation>
    </message>
    <message>
        <source>Select a branch</source>
        <translation>选择分支</translation>
    </message>
    <message>
        <source>UNINSTALL</source>
        <translation>卸载</translation>
    </message>
    <message>
        <source>Uninstall %1</source>
        <translation>卸载 %1</translation>
    </message>
    <message>
        <source>Are you sure you want to uninstall?</source>
        <translation>您确定要卸载吗？</translation>
    </message>
    <message>
        <source>CHECK</source>
        <translation>查看</translation>
    </message>
    <message>
        <source>Uninstall</source>
        <translation>卸载</translation>
    </message>
    <message>
        <source>failed to check for update</source>
        <translation>检查更新失败</translation>
    </message>
    <message>
        <source>up to date, last checked %1</source>
        <translation>已经是最新版本，上次检查时间为 %1</translation>
    </message>
    <message>
        <source>DOWNLOAD</source>
        <translation>下载</translation>
    </message>
    <message>
        <source>update available</source>
        <translation>有可用的更新</translation>
    </message>
    <message>
        <source>never</source>
        <translation>从未更新</translation>
    </message>
</context>
<context>
    <name>SshControl</name>
    <message>
        <source>SSH Keys</source>
        <translation>SSH密钥</translation>
    </message>
    <message>
        <source>Warning: This grants SSH access to all public keys in your GitHub settings. Never enter a GitHub username other than your own. A comma employee will NEVER ask you to add their GitHub username.</source>
        <translation>警告：这将授予SSH访问权限给您GitHub设置中的所有公钥。切勿输入您自己以外的GitHub用户名。comma员工永远不会要求您添加他们的GitHub用户名。</translation>
    </message>
    <message>
        <source>ADD</source>
        <translation>添加</translation>
    </message>
    <message>
        <source>Enter your GitHub username</source>
        <translation>输入您的GitHub用户名</translation>
    </message>
    <message>
        <source>LOADING</source>
        <translation>正在加载</translation>
    </message>
    <message>
        <source>REMOVE</source>
        <translation>删除</translation>
    </message>
    <message>
        <source>Username &apos;%1&apos; has no keys on GitHub</source>
        <translation>用户名“%1”在GitHub上没有密钥</translation>
    </message>
    <message>
        <source>Request timed out</source>
        <translation>请求超时</translation>
    </message>
    <message>
        <source>Username &apos;%1&apos; doesn&apos;t exist on GitHub</source>
        <translation>GitHub上不存在用户名“%1”</translation>
    </message>
</context>
<context>
    <name>SshToggle</name>
    <message>
        <source>Enable SSH</source>
        <translation>启用SSH</translation>
    </message>
</context>
<context>
    <name>TermsPage</name>
    <message>
        <source>Decline</source>
        <translation>拒绝</translation>
    </message>
    <message>
        <source>Agree</source>
        <translation>同意</translation>
    </message>
    <message>
        <source>Welcome to openpilot</source>
        <translation>欢迎使用 openpilot</translation>
    </message>
    <message>
        <source>You must accept the Terms and Conditions to use openpilot. Read the latest terms at &lt;span style=&apos;color: #465BEA;&apos;&gt;https://comma.ai/terms&lt;/span&gt; before continuing.</source>
        <translation>您必须接受《条款与条件》才能使用 openpilot。在继续之前，请先阅读最新条款：&lt;span style=&apos;color: #465BEA;&apos;&gt;https://comma.ai/terms&lt;/span&gt;。</translation>
    </message>
</context>
<context>
    <name>TogglesPanel</name>
    <message>
        <source>Enable openpilot</source>
        <translation>启用openpilot</translation>
    </message>
    <message>
        <source>Enable Lane Departure Warnings</source>
        <translation>启用车道偏离警告</translation>
    </message>
    <message>
        <source>Receive alerts to steer back into the lane when your vehicle drifts over a detected lane line without a turn signal activated while driving over 31 mph (50 km/h).</source>
        <translation>车速超过31mph（50km/h）时，若检测到车辆越过车道线且未打转向灯，系统将发出警告以提醒您返回车道。</translation>
    </message>
    <message>
        <source>Use Metric System</source>
        <translation>使用公制单位</translation>
    </message>
    <message>
        <source>Display speed in km/h instead of mph.</source>
        <translation>显示车速时，以km/h代替mph。</translation>
    </message>
    <message>
        <source>Record and Upload Driver Camera</source>
        <translation>录制并上传驾驶员摄像头</translation>
    </message>
    <message>
        <source>Upload data from the driver facing camera and help improve the driver monitoring algorithm.</source>
        <translation>上传驾驶员摄像头的数据，帮助改进驾驶员监控算法。</translation>
    </message>
    <message>
        <source>Disengage on Accelerator Pedal</source>
        <translation>踩油门时取消控制</translation>
    </message>
    <message>
        <source>When enabled, pressing the accelerator pedal will disengage openpilot.</source>
        <translation>启用后，踩下油门踏板将取消openpilot。</translation>
    </message>
    <message>
        <source>Experimental Mode</source>
        <translation>测试模式</translation>
    </message>
    <message>
        <source>openpilot defaults to driving in &lt;b&gt;chill mode&lt;/b&gt;. Experimental mode enables &lt;b&gt;alpha-level features&lt;/b&gt; that aren&apos;t ready for chill mode. Experimental features are listed below:</source>
        <translation>openpilot 默认 &lt;b&gt;轻松模式&lt;/b&gt;驾驶车辆。试验模式启用一些轻松模式之外的 &lt;b&gt;试验性功能&lt;/b&gt;。试验性功能包括：</translation>
    </message>
    <message>
        <source>Let the driving model control the gas and brakes. openpilot will drive as it thinks a human would, including stopping for red lights and stop signs. Since the driving model decides the speed to drive, the set speed will only act as an upper bound. This is an alpha quality feature; mistakes should be expected.</source>
        <translation>允许驾驶模型控制加速和制动，openpilot将模仿人类驾驶车辆，包括在红灯和停车让行标识前停车。鉴于驾驶模型确定行驶车速，所设定的车速仅作为上限。此功能尚处于早期测试状态，有可能会出现操作错误。</translation>
    </message>
    <message>
        <source>New Driving Visualization</source>
        <translation>新驾驶视角</translation>
    </message>
    <message>
        <source>Experimental mode is currently unavailable on this car since the car&apos;s stock ACC is used for longitudinal control.</source>
        <translation>由于此车辆使用自带的ACC纵向控制，当前无法使用试验模式。</translation>
    </message>
    <message>
        <source>openpilot longitudinal control may come in a future update.</source>
        <translation>openpilot纵向控制可能会在未来的更新中提供。</translation>
    </message>
    <message>
        <source>Aggressive</source>
        <translation>积极</translation>
    </message>
    <message>
        <source>Standard</source>
        <translation>标准</translation>
    </message>
    <message>
        <source>Relaxed</source>
        <translation>舒适</translation>
    </message>
    <message>
        <source>Driving Personality</source>
        <translation>驾驶风格</translation>
    </message>
    <message>
        <source>An alpha version of openpilot longitudinal control can be tested, along with Experimental mode, on non-release branches.</source>
        <translation>在正式（release）版本以外的分支上，可以测试 openpilot 纵向控制的 Alpha 版本以及实验模式。</translation>
    </message>
    <message>
        <source>Enable the openpilot longitudinal control (alpha) toggle to allow Experimental mode.</source>
        <translation>启用 openpilot 纵向控制（alpha）开关以允许实验模式。</translation>
    </message>
    <message>
        <source>End-to-End Longitudinal Control</source>
        <translation>端到端纵向控制</translation>
    </message>
    <message>
        <source>Standard is recommended. In aggressive mode, openpilot will follow lead cars closer and be more aggressive with the gas and brake. In relaxed mode openpilot will stay further away from lead cars. On supported cars, you can cycle through these personalities with your steering wheel distance button.</source>
        <translation>推荐使用标准模式。在积极模式下，openpilot 会更靠近前方车辆，并在油门和刹车方面更加激进。在放松模式下，openpilot 会与前方车辆保持更远距离。在支持的车型上，你可以使用方向盘上的距离按钮来循环切换这些驾驶风格。</translation>
    </message>
    <message>
        <source>The driving visualization will transition to the road-facing wide-angle camera at low speeds to better show some turns. The Experimental mode logo will also be shown in the top right corner.</source>
        <translation>在低速时，驾驶可视化将转换为道路朝向的广角摄像头，以更好地展示某些转弯。测试模式标志也将显示在右上角。</translation>
    </message>
    <message>
        <source>Always-On Driver Monitoring</source>
        <translation>驾驶员监控常开</translation>
    </message>
    <message>
        <source>Enable driver monitoring even when openpilot is not engaged.</source>
        <translation>即使在openpilot未激活时也启用驾驶员监控。</translation>
    </message>
    <message>
        <source>Use the openpilot system for adaptive cruise control and lane keep driver assistance. Your attention is required at all times to use this feature.</source>
        <translation>openpilot 系统提供“自适应巡航”和“车道保持”驾驶辅助功能。使用此功能时，您需要时刻保持专注。</translation>
    </message>
    <message>
        <source> Changing this setting will restart openpilot if the car is powered on.</source>
        <translation>如果车辆已通电，更改此设置将会重新启动 openpilot。</translation>
    </message>
</context>
<context>
    <name>Updater</name>
    <message>
        <source>Update Required</source>
        <translation>需要更新</translation>
    </message>
    <message>
        <source>An operating system update is required. Connect your device to Wi-Fi for the fastest update experience. The download size is approximately 1GB.</source>
        <translation>操作系统需要更新。请将您的设备连接到WiFi以获取更快的更新体验。下载大小约为1GB。</translation>
    </message>
    <message>
        <source>Connect to Wi-Fi</source>
        <translation>连接到WiFi</translation>
    </message>
    <message>
        <source>Install</source>
        <translation>安装</translation>
    </message>
    <message>
        <source>Back</source>
        <translation>返回</translation>
    </message>
    <message>
        <source>Loading...</source>
        <translation>正在加载……</translation>
    </message>
    <message>
        <source>Reboot</source>
        <translation>重启</translation>
    </message>
    <message>
        <source>Update failed</source>
        <translation>更新失败</translation>
    </message>
</context>
<context>
    <name>WiFiPromptWidget</name>
    <message>
        <source>Open</source>
        <translation>开启</translation>
    </message>
    <message>
        <source>Maximize your training data uploads to improve openpilot&apos;s driving models.</source>
        <translation>最大化您的训练数据上传，以改善 openpilot 的驾驶模型。</translation>
    </message>
    <message>
        <source>&lt;span style=&apos;font-family: &quot;Noto Color Emoji&quot;;&apos;&gt;🔥&lt;/span&gt; Firehose Mode &lt;span style=&apos;font-family: Noto Color Emoji;&apos;&gt;🔥&lt;/span&gt;</source>
        <translation>&lt;span style=&apos;font-family: &quot;Noto Color Emoji&quot;;&apos;&gt;🔥&lt;/span&gt; 训练数据上传模式 &lt;span style=&apos;font-family: Noto Color Emoji;&apos;&gt;🔥&lt;/span&gt;</translation>
    </message>
</context>
<context>
    <name>WifiUI</name>
    <message>
        <source>Scanning for networks...</source>
        <translation>正在扫描网络……</translation>
    </message>
    <message>
        <source>CONNECTING...</source>
        <translation>正在连接……</translation>
    </message>
    <message>
        <source>FORGET</source>
        <translation>忽略</translation>
    </message>
    <message>
        <source>Forget Wi-Fi Network &quot;%1&quot;?</source>
        <translation>忽略WiFi网络 &quot;%1&quot;?</translation>
    </message>
    <message>
        <source>Forget</source>
        <translation>忽略</translation>
    </message>
</context>
</TS>
