{
  "editor.tabSize": 2,
  "editor.insertSpaces": true,
  "editor.renderWhitespace": "trailing",
  "files.trimTrailingWhitespace": true,
  "search.exclude": {
    "**/.git": true,
    "**/.venv": true,
    "**/__pycache__": true
  },
  "files.exclude": {
    "**/.git": true,
    "**/.venv": true,
    "**/__pycache__": true
  },
  "python.analysis.exclude": [
    "**/.git",
    "**/.venv",
    "**/__pycache__",
    // exclude directories that should be using the symlinked version
    "common/**",
    "selfdrive/**",
    "system/**",
    "third_party/**",
    "tools/**",
  ]
}
