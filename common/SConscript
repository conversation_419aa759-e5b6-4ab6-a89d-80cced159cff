Import('env', 'envCython', 'arch')

common_libs = [
  'params.cc',
  'swaglog.cc',
  'util.cc',
  'watchdog.cc',
  'ratekeeper.cc'
]

_common = env.Library('common', common_libs, LIBS="json11")

files = [
  'clutil.cc',
]

_gpucommon = env.Library('gpucommon', files)
Export('_common', '_gpucommon')

if GetOption('extras'):
  env.Program('tests/test_common',
              ['tests/test_runner.cc', 'tests/test_params.cc', 'tests/test_util.cc', 'tests/test_swaglog.cc'],
              LIBS=[_common, 'json11', 'zmq', 'pthread'])

# Cython bindings
params_python = envCython.Program('params_pyx.so', 'params_pyx.pyx', LIBS=envCython['LIBS'] + [_common, 'zmq', 'json11'])

SConscript([
  'transformations/SConscript',
])

Import('transformations_python')
common_python = [params_python, transformations_python]

Export('common_python')
