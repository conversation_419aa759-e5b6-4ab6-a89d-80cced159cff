name: release
on:
  schedule:
    - cron: '0 9 * * *'
  workflow_dispatch:

jobs:
  build_masterci:
    name: build master-ci
    env:
      ImageOS: ubuntu24
    container:
      image: ghcr.io/commaai/openpilot-base:latest
    runs-on: ubuntu-latest
    if: github.repository == 'commaai/openpilot'
    permissions:
      checks: read
      contents: write
    steps:
    - name: Install wait-on-check-action dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y libyaml-dev
    - name: Wait for green check mark
      if: ${{ github.event_name == 'schedule' }}
      uses: lewagon/wait-on-check-action@ccfb013c15c8afb7bf2b7c028fb74dc5a068cccc
      with:
        ref: master
        wait-interval: 30
        running-workflow-name: 'build master-ci'
        repo-token: ${{ secrets.GITHUB_TOKEN }}
        check-regexp: ^((?!.*(build prebuilt).*).)*$
    - uses: actions/checkout@v4
      with:
        submodules: true
        fetch-depth: 0
    - name: Pull LFS
      run: |
        git config --global --add safe.directory '*'
        git lfs pull
    - name: Push master-ci
      run: BRANCH=__nightly release/build_devel.sh
