name: repo maintenance

on:
  schedule:
    - cron: "0 14 * * 1" # every Monday at 2am UTC (6am PST)
  workflow_dispatch:

env:
  BASE_IMAGE: openpilot-base
  BUILD: selfdrive/test/docker_build.sh base
  RUN: docker run --shm-size 2G -v $PWD:/tmp/openpilot -w /tmp/openpilot -e CI=1 -e PYTHONWARNINGS=error -e FILEREADER_CACHE=1 -e PYTHONPATH=/tmp/openpilot -e NUM_JOBS -e JOB_ID -e GITHUB_ACTION -e GITHUB_REF -e GITHUB_HEAD_REF -e GITHUB_SHA -e GITHUB_REPOSITORY -e GITHUB_RUN_ID -v $GITHUB_WORKSPACE/.ci_cache/scons_cache:/tmp/scons_cache -v $GITHUB_WORKSPACE/.ci_cache/comma_download_cache:/tmp/comma_download_cache -v $GITHUB_WORKSPACE/.ci_cache/openpilot_cache:/tmp/openpilot_cache $BASE_IMAGE /bin/bash -c

jobs:
  update_translations:
    runs-on: ubuntu-latest
    if: github.repository == 'commaai/openpilot'
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/workflows/setup-with-retry
      - name: Update translations
        run: |
          ${{ env.RUN }} "python3 selfdrive/ui/update_translations.py --vanish"
      - name: Create Pull Request
        uses: peter-evans/create-pull-request@9153d834b60caba6d51c9b9510b087acf9f33f83
        with:
          author: Vehicle Researcher <<EMAIL>>
          commit-message: "Update translations"
          title: "[bot] Update translations"
          body: "Automatic PR from repo-maintenance -> update_translations"
          branch: "update-translations"
          base: "master"
          delete-branch: true
          labels: bot

  package_updates:
    name: package_updates
    runs-on: ubuntu-latest
    container:
      image: ghcr.io/commaai/openpilot-base:latest
    if: github.repository == 'commaai/openpilot'
    steps:
    - uses: actions/checkout@v4
      with:
        submodules: true
    - name: uv lock
      run: |
        python3 -m ensurepip --upgrade
        pip3 install uv
        uv lock --upgrade
    - name: bump submodules
      run: |
        git config --global --add safe.directory '*'
        git submodule update --remote
        git add .
    - name: update car docs
      run: |
        export PYTHONPATH="$PWD"
        scons -j$(nproc) --minimal opendbc_repo
        python selfdrive/car/docs.py
        git add docs/CARS.md
    - name: Create Pull Request
      uses: peter-evans/create-pull-request@9153d834b60caba6d51c9b9510b087acf9f33f83
      with:
        author: Vehicle Researcher <<EMAIL>>
        token: ${{ secrets.ACTIONS_CREATE_PR_PAT }}
        commit-message: Update Python packages
        title: '[bot] Update Python packages'
        branch: auto-package-updates
        base: master
        delete-branch: true
        body: 'Automatic PR from repo-maintenance -> package_updates'
        labels: bot
