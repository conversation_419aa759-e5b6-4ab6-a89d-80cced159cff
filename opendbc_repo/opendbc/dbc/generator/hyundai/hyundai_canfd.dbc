CM_ "IMPORT _hyundai_common.dbc";

BO_ 53 ACCELERATOR: 32 XXX
 SG_ CHECKSUM : 0|16@1+ (1,0) [0|65535] "" XXX
 SG_ COUNTER : 16|8@1+ (1,0) [0|255] "" XXX
 SG_ ACCELERATOR_PEDAL : 40|8@1+ (1,0) [0|255] "" XXX
 SG_ GEAR : 192|3@1+ (1,0) [0|7] "" XXX

BO_ 64 GEAR_ALT: 32 XXX
 SG_ CHECKSUM : 0|16@1+ (1,0) [0|65535] "" XXX
 SG_ COUNTER : 16|8@1+ (1,0) [0|255] "" XXX
 SG_ GEAR : 32|3@1+ (1,0) [0|7] "" XXX

BO_ 69 GEAR: 24 XXX
 SG_ CHECKSUM : 0|16@1+ (1,0) [0|65535] "" XXX
 SG_ COUNTER : 16|8@1+ (1,0) [0|255] "" XXX
 SG_ GEAR : 44|3@1+ (1,0) [0|7] "" XXX

BO_ 96 ESP_STATUS: 32 XXX
 SG_ CHECKSUM : 0|16@1+ (1,0) [0|65535] "" XXX
 SG_ COUNTER : 16|8@1+ (1,0) [0|255] "" XXX
 SG_ TRACTION_AND_STABILITY_CONTROL : 42|3@1+ (1,0) [0|63] "" XXX
 SG_ BRAKE_PRESSURE : 128|10@1+ (1,0) [0|65535] "" XXX
 SG_ BRAKE_PRESSED : 148|1@1+ (1,0) [0|3] "" XXX

BO_ 101 BRAKE: 32 XXX
 SG_ CHECKSUM : 0|16@1+ (1,0) [0|65535] "" XXX
 SG_ COUNTER : 16|8@1+ (1,0) [0|255] "" XXX
 SG_ BRAKE_POSITION : 40|16@1- (1,0) [0|65535] "" XXX
 SG_ BRAKE_PRESSED : 57|1@1+ (1,0) [0|3] "" XXX

BO_ 112 GEAR_ALT_2: 32 XXX
 SG_ CHECKSUM : 0|16@1+ (1,0) [0|65535] "" XXX
 SG_ COUNTER : 16|8@1+ (1,0) [0|255] "" XXX
 SG_ GEAR : 60|3@1+ (1,0) [0|7] "" XXX

BO_ 160 WHEEL_SPEEDS: 24 XXX
 SG_ CHECKSUM : 0|16@1+ (1,0) [0|65535] "" XXX
 SG_ COUNTER : 16|8@1+ (1,0) [0|255] "" XXX
 SG_ MOVING_FORWARD : 56|1@0+ (1,0) [0|1] "" XXX
 SG_ MOVING_BACKWARD : 57|1@0+ (1,0) [0|1] "" XXX
 SG_ MOVING_FORWARD2 : 58|1@0+ (1,0) [0|1] "" XXX
 SG_ MOVING_BACKWARD2 : 59|1@0+ (1,0) [0|1] "" XXX
 SG_ WHL_SpdFLVal : 64|14@1+ (0.03125,0) [0|0] "km^h" XXX
 SG_ WHL_SpdFRVal : 80|14@1+ (0.03125,0) [0|0] "km^h" XXX
 SG_ WHL_SpdRLVal : 96|14@1+ (0.03125,0) [0|0] "km^h" XXX
 SG_ WHL_SpdRRVal : 112|14@1+ (0.03125,0) [0|0] "km^h" XXX

BO_ 203 LFA_ALT: 24 ADRV
 SG_ CHECKSUM : 0|16@1+ (1,0) [0|65535] "" XXX
 SG_ COUNTER : 16|8@1+ (1,0) [0|255] "" XXX
 SG_ LKAS_ANGLE_ACTIVE : 29|2@0+ (1,0) [0|1] "" XXX
 SG_ LKAS_ANGLE_CMD : 32|14@1- (0.1,0) [0|511] "" XXX
 SG_ LKAS_ANGLE_MAX_TORQUE : 55|8@0+ (1,0) [0|255] "" XXX

BO_ 234 MDPS: 24 XXX
 SG_ CHECKSUM : 0|16@1+ (1,0) [0|65535] "" XXX
 SG_ COUNTER : 16|8@1+ (1,0) [0|255] "" XXX
 SG_ LKA_ACTIVE : 48|1@0+ (1,0) [0|16777215] "" XXX
 SG_ LKA_FAULT : 54|1@0+ (1,0) [0|1] "" XXX
 SG_ STEERING_OUT_TORQUE : 64|12@1+ (0.1,-204.8) [0|65535] "" XXX
 SG_ STEERING_COL_TORQUE : 80|13@1+ (1,-4095) [0|4095] "" XXX
 SG_ STEERING_ANGLE : 96|16@1- (0.1,0) [0|255] "deg" XXX
 SG_ STEERING_ANGLE_2 : 128|16@1- (0.1,0) [0|65535] "deg" XXX
 SG_ LKA_ANGLE_ACTIVE : 145|2@0+ (1,0) [0|3] "" XXX
 SG_ LKA_ANGLE_FAULT : 149|1@0+ (1,0) [0|1] "" XXX

BO_ 256 ACCELERATOR_BRAKE_ALT: 32 XXX
 SG_ CHECKSUM : 0|16@1+ (1,0) [0|65535] "" XXX
 SG_ COUNTER : 16|8@1+ (1,0) [0|255] "" XXX
 SG_ BRAKE_PRESSED : 32|1@1+ (1,0) [0|1] "" XXX
 SG_ ACCELERATOR_PEDAL_PRESSED : 176|1@1+ (1,0) [0|1] "" XXX

BO_ 261 ACCELERATOR_ALT: 32 XXX
 SG_ CHECKSUM : 0|16@1+ (1,0) [0|65535] "" XXX
 SG_ COUNTER : 16|8@1+ (1,0) [0|255] "" XXX
 SG_ ACCELERATOR_PEDAL : 103|10@1+ (0.25,0) [0|1022] "" XXX

BO_ 272 LKAS_ALT: 32 XXX
 SG_ CHECKSUM : 0|16@1+ (1,0) [0|65535] "" XXX
 SG_ COUNTER : 16|8@1+ (1,0) [0|255] "" XXX
 SG_ LKA_MODE : 24|3@1+ (1,0) [0|7] "" XXX
 SG_ LKA_AVAILABLE : 27|2@1+ (1,0) [0|255] "" XXX
 SG_ LKA_WARNING : 32|1@1+ (1,0) [0|1] "" XXX
 SG_ LKA_ICON : 38|2@1+ (1,0) [0|255] "" XXX
 SG_ FCA_SYSWARN : 40|1@0+ (1,0) [0|1] "" XXX
 SG_ TORQUE_REQUEST : 41|11@1+ (1,-1024) [0|4095] "" XXX
 SG_ STEER_REQ : 52|1@1+ (1,0) [0|1] "" XXX
 SG_ LFA_BUTTON : 56|1@1+ (1,0) [0|255] "" XXX
 SG_ LKA_ASSIST : 62|1@1+ (1,0) [0|1] "" XXX
 SG_ STEER_MODE : 65|3@1+ (1,0) [0|1] "" XXX
 SG_ NEW_SIGNAL_2 : 70|2@0+ (1,0) [0|3] "" XXX
 SG_ LKAS_ANGLE_ACTIVE : 77|2@0+ (1,0) [0|3] "" XXX
 SG_ HAS_LANE_SAFETY : 80|1@0+ (1,0) [0|1] "" XXX
 SG_ LKAS_ANGLE_CMD : 82|14@1- (0.1,0) [0|511] "" XXX
 SG_ LKAS_ANGLE_MAX_TORQUE : 96|8@1+ (1,0) [0|255] "" XXX
 SG_ NEW_SIGNAL_3 : 111|8@0+ (1,0) [0|255] "" XXX

BO_ 282 FR_CMR_01_10ms: 16 FR_CMR
 SG_ FR_CMR_Crc1Val : 0|16@1+ (1,0) [0|0] "" Dummy,IBU_HS,vBDM
 SG_ FR_CMR_AlvCnt1Val : 16|8@1+ (1,0) [0|0] "" CLU,IBU_HS,vBDM
 SG_ HBA_SysOptSta : 24|2@1+ (1,0) [0|3] "" CLU,IBU_HS,vBDM
 SG_ HBA_SysSta : 26|3@1+ (1,0) [0|7] "" CLU,IBU_HS,vBDM
 SG_ HBA_IndLmpReq : 29|2@1+ (1,0) [0|3] "" CLU,vBDM
 SG_ iHBAref_VehLftSta : 31|2@1+ (1,0) [0|3] "" IBU_HS,ICU,vBDM
 SG_ iHBAref_VehCtrSta : 33|2@1+ (1,0) [0|3] "" IBU_HS,ICU,vBDM
 SG_ iHBAref_VehRtSta : 35|2@1+ (1,0) [0|3] "" IBU_HS,ICU,vBDM
 SG_ iHBAref_ILLAmbtSta : 37|2@1+ (1,0) [0|3] "" IBU_HS,ICU,vBDM
 SG_ FCA_Equip_MFC : 39|3@1+ (1,0) [0|0] "" ADAS_DRV,RR_C_RDR,vBDM
 SG_ HBA_OptUsmSta : 42|2@1+ (1,0) [0|3] "" CLU,H_U_MM
 SG_ FCAref_FusSta : 45|3@1+ (1,0) [0|0] "" vBDM
 SG_ DAW_LVDA_PUDis : 48|2@1+ (1,0) [0|0] "" CLU,vBDM
 SG_ DAW_LVDA_OptUsmSta : 50|2@1+ (1,0) [0|3] "" CLU,H_U_MM,vBDM
 SG_ DAW_OptUsmSta : 52|3@1+ (1,0) [0|0] "" CLU,H_U_MM
 SG_ DAW_SysSta : 55|4@1+ (1,0) [0|0] "" CLU
 SG_ DAW_WrnMsgSta : 59|3@1+ (1,0) [0|0] "" CLU
 SG_ DAW_TimeRstReq : 62|2@1+ (1,0) [0|0] "" CLU
 SG_ DAW_SnstvtyModRetVal : 64|3@1+ (1,0) [0|0] "" CLU,H_U_MMz
 SG_ FR_CMR_SCCEquipSta : 85|2@1+ (1,0) [0|3] "" CGW
 SG_ FR_CMR_ReqADASMapMsgVal : 96|16@1+ (1,0) [0|65535] "" CGW
 SG_ FR_CMR_SwVer1Val : 112|4@1+ (1,0) [0|15] "" CGW
 SG_ FR_CMR_SwVer2Val : 120|8@1+ (1,0) [0|255] "" CGW

BO_ 293 STEERING_SENSORS: 16 XXX
 SG_ CHECKSUM : 0|16@1+ (1,0) [0|65535] "" XXX
 SG_ COUNTER : 16|8@1+ (1,0) [0|255] "" XXX
 SG_ STEERING_ANGLE : 24|16@1- (0.1,0) [0|255] "deg" XXX
 SG_ STEERING_RATE : 40|8@1+ (4,0) [0|1016] "deg/s" XXX

BO_ 298 LFA: 16 ADRV
 SG_ CHECKSUM : 0|16@1+ (1,0) [0|65535] "" XXX
 SG_ COUNTER : 16|8@1+ (1,0) [0|255] "" XXX
 SG_ LKA_MODE : 24|3@1+ (1,0) [0|7] "" XXX
 SG_ NEW_SIGNAL_1 : 27|2@1+ (1,0) [0|255] "" XXX
 SG_ LKA_WARNING : 32|1@1+ (1,0) [0|1] "" XXX
 SG_ LKA_ICON : 38|2@1+ (1,0) [0|255] "" XXX
 SG_ TORQUE_REQUEST : 41|11@1+ (1,-1024) [0|4095] "" XXX
 SG_ STEER_REQ : 52|1@1+ (1,0) [0|1] "" XXX
 SG_ LFA_BUTTON : 56|1@1+ (1,0) [0|255] "" XXX
 SG_ LKA_ASSIST : 62|1@1+ (1,0) [0|1] "" XXX
 SG_ STEER_MODE : 65|3@1+ (1,0) [0|1] "" XXX
 SG_ NEW_SIGNAL_2 : 70|2@0+ (1,0) [0|3] "" XXX
 SG_ NEW_SIGNAL_4 : 72|4@1+ (1,0) [0|15] "" XXX
 SG_ HAS_LANE_SAFETY : 80|1@0+ (1,0) [0|1] "" XXX
 SG_ NEW_SIGNAL_3 : 111|8@0+ (1,0) [0|255] "" XXX

BO_ 304 GEAR_SHIFTER: 16 XXX
 SG_ CHECKSUM : 0|16@1+ (1,0) [0|65535] "" XXX
 SG_ COUNTER : 16|8@1+ (1,0) [0|255] "" XXX
 SG_ PARK_BUTTON : 32|2@1+ (1,0) [0|3] "" XXX
 SG_ KNOB_POSITION : 40|3@1+ (1,0) [0|3] "" XXX
 SG_ GEAR : 64|3@1+ (1,0) [0|7] "" XXX

BO_ 352 ADRV_0x160: 16 ADRV
 SG_ CHECKSUM : 0|16@1+ (1,0) [0|65535] "" XXX
 SG_ COUNTER : 16|8@1+ (1,0) [0|255] "" XXX
 SG_ AEB_SETTING : 24|2@1+ (1,0) [0|255] "" XXX
 SG_ SET_ME_2 : 56|8@1+ (1,0) [0|1] "" XXX
 SG_ SET_ME_FF : 64|8@1+ (1,0) [0|255] "" XXX
 SG_ SET_ME_FC : 72|8@1+ (1,0) [0|255] "" XXX
 SG_ SET_ME_9 : 80|8@1+ (1,0) [0|255] "" XXX

BO_ 353 CCNC_0x161: 32 CCNC
 SG_ CHECKSUM : 0|16@1+ (1,0) [0|65535] "" XXX
 SG_ COUNTER : 16|8@1+ (1,0) [0|255] "" XXX
 SG_ FCA_ICON : 24|3@1+ (1,0) [0|7] "" XXX
 SG_ FCA_ALT_ICON : 27|3@1+ (1,0) [0|7] "" XXX
 SG_ LKA_ICON : 30|3@1+ (1,0) [0|3] "" XXX
 SG_ HBA_ICON : 33|3@1+ (1,0) [0|7] "" XXX
 SG_ ZEROS_1 : 36|4@1+ (1,0) [0|15] "" XXX
 SG_ ZEROS_2 : 40|2@1+ (1,0) [0|3] "" XXX
 SG_ FCA_IMAGE : 42|3@1+ (1,0) [0|7] "" XXX
 SG_ ZEROS_3 : 45|3@1+ (1,0) [0|7] "" XXX
 SG_ ZEROS_4 : 48|3@1+ (1,0) [0|7] "" XXX
 SG_ BCA_LEFT : 51|3@1+ (1,0) [0|7] "" XXX
 SG_ BCA_RIGHT : 54|3@1+ (1,0) [0|7] "" XXX
 SG_ LCA_LEFT_ARROW : 57|3@1+ (1,0) [0|7] "" XXX
 SG_ LCA_RIGHT_ARROW : 60|3@1+ (1,0) [0|7] "" XXX
 SG_ ZEROS_5 : 63|1@0+ (1,0) [0|1] "" XXX
 SG_ CENTERLINE : 64|2@1+ (1,0) [0|3] "" XXX
 SG_ TARGET : 66|3@1+ (1,0) [0|7] "" XXX
 SG_ TARGET_DISTANCE : 69|11@1+ (0.1,0) [0|204.7] "m" XXX
 SG_ LANELINE_LEFT : 80|4@1+ (1,0) [0|15] "" XXX
 SG_ LANELINE_LEFT_POSITION : 84|6@1+ (1,0) [0|15] "" XXX
 SG_ LANELINE_RIGHT : 90|4@1+ (1,0) [0|15] "" XXX
 SG_ LANELINE_RIGHT_POSITION : 94|6@1+ (1,0) [0|3] "" XXX
 SG_ LANELINE_CURVATURE : 100|5@1- (1,15) [0|31] "" XXX
 SG_ LANE_HIGHLIGHT : 105|4@1+ (1,0) [0|15] "" XXX
 SG_ LANE_HIGHLIGHT_DISTANCE : 109|11@1+ (0.1,0) [0|204.7] "m" XXX
 SG_ LANE_LEFT : 120|3@1+ (1,0) [0|7] "" XXX
 SG_ LANE_RIGHT : 123|3@1+ (1,0) [0|7] "" XXX
 SG_ LANE_ZOOM : 126|2@1+ (1,0) [0|3] "" XXX
 SG_ ALERTS_1 : 128|6@1+ (1,0) [0|63] "" XXX
 SG_ ALERTS_2 : 134|5@1+ (1,0) [0|3] "" XXX
 SG_ ALERTS_3 : 139|5@1+ (1,0) [0|15] "" XXX
 SG_ ALERTS_4 : 144|8@1+ (1,0) [0|511] "" XXX
 SG_ ALERTS_5 : 152|5@1+ (1,0) [0|7] "" XXX
 SG_ MUTE : 157|3@1+ (1,0) [0|7] "" XXX
 SG_ SOUNDS_1 : 160|4@1+ (1,0) [0|3] "" XXX
 SG_ SOUNDS_2 : 164|4@1+ (1,0) [0|3] "" XXX
 SG_ SOUNDS_3 : 168|4@1+ (1,0) [0|15] "" XXX
 SG_ SOUNDS_4 : 172|3@1+ (1,0) [0|7] "" XXX
 SG_ ZEROS_6 : 175|1@0+ (1,0) [0|1] "" XXX
 SG_ ZEROS_7 : 176|5@1+ (1,0) [0|31] "" XXX
 SG_ SETSPEED_HUD : 181|3@1+ (1,0) [0|3] "" XXX
 SG_ DISTANCE_LEAD : 184|5@1+ (1,0) [0|31] "" XXX
 SG_ DISTANCE_CAR : 189|3@1+ (1,0) [0|7] "" XXX
 SG_ DISTANCE_SPACING : 192|4@1+ (1,0) [0|15] "" XXX
 SG_ DISTANCE : 196|4@1+ (1,0) [0|7] "" XXX
 SG_ SETSPEED_SPEED : 200|8@1+ (1,0) [0|255] "" XXX
 SG_ SETSPEED : 208|4@1+ (1,0) [0|3] "" XXX
 SG_ HDA_ICON : 212|4@1+ (1,0) [0|3] "" XXX
 SG_ SLA_ICON : 216|4@1+ (1,0) [0|15] "" XXX
 SG_ NAV_ICON : 220|4@1+ (1,0) [0|3] "" XXX
 SG_ LFA_ICON : 224|4@1+ (1,0) [0|3] "" XXX
 SG_ LCA_LEFT_ICON : 228|4@1+ (1,0) [0|15] "" XXX
 SG_ LCA_RIGHT_ICON : 232|4@1+ (1,0) [0|15] "" XXX
 SG_ BACKGROUND : 236|4@1+ (1,0) [0|15] "" XXX
 SG_ DAW_ICON : 240|3@1+ (1,0) [0|7] "" XXX
 SG_ CAR_CIRCLE : 243|3@1+ (1,0) [0|7] "" XXX
 SG_ ZEROS_8 : 246|2@1+ (1,0) [0|3] "" XXX
 SG_ ZEROS_9 : 248|8@1+ (1,0) [0|255] "" XXX

BO_ 354 CCNC_0x162: 32 CCNC
 SG_ CHECKSUM : 0|16@1+ (1,0) [0|65535] "" XXX
 SG_ COUNTER : 16|8@1+ (1,0) [0|255] "" XXX
 SG_ COUNTRY : 24|4@1+ (1,0) [0|7] "" XXX
 SG_ SPEEDLIMIT_FLASH : 28|4@1+ (1,0) [0|15] "" XXX
 SG_ SPEEDLIMIT : 32|8@1+ (1,0) [0|255] "" XXX
 SG_ SIGNS : 40|8@1+ (1,0) [0|15] "" XXX
 SG_ SPEEDLIMIT_WEATHER : 48|4@1+ (1,0) [0|15] "" XXX
 SG_ VIBRATE : 52|3@1+ (1,0) [0|7] "" XXX
 SG_ ZEROS_1 : 55|1@0+ (1,0) [0|1] "" XXX
 SG_ ZEROS_2 : 56|8@1+ (1,0) [0|255] "" XXX
 SG_ LEAD : 64|5@1+ (1,0) [0|31] "" XXX
 SG_ LEAD_DISTANCE : 69|11@1+ (0.1,0) [0|204.7] "m" XXX
 SG_ LEAD_LATERAL : 80|7@1+ (0.1,0) [0|127] "m" XXX
 SG_ ZEROS_3 : 87|1@0+ (1,0) [0|1] "" XXX
 SG_ LEAD_ALT : 88|5@1+ (1,0) [0|31] "" XXX
 SG_ LEAD_ALT_DISTANCE : 93|11@1+ (0.1,0) [0|204.7] "m" XXX
 SG_ LEAD_ALT_LATERAL : 104|7@1+ (0.1,0) [0|127] "m" XXX
 SG_ ZEROS_4 : 111|1@0+ (1,0) [0|1] "" XXX
 SG_ LEAD_LEFT : 112|5@1+ (1,0) [0|31] "" XXX
 SG_ LEAD_LEFT_DISTANCE : 117|11@1+ (0.1,0) [0|204.7] "m" XXX
 SG_ LEAD_LEFT_LATERAL : 128|7@1+ (0.1,0) [0|127] "m" XXX
 SG_ ZEROS_5 : 135|1@0+ (1,0) [0|1] "" XXX
 SG_ LEAD_RIGHT : 136|5@1+ (1,0) [0|31] "" XXX
 SG_ LEAD_RIGHT_DISTANCE : 141|11@1+ (0.1,0) [0|204.7] "m" XXX
 SG_ LEAD_RIGHT_LATERAL : 152|7@1+ (0.1,0) [0|127] "m" XXX
 SG_ ZEROS_6 : 159|1@0+ (1,0) [0|1] "" XXX
 SG_ ZEROS_7 : 162|3@0+ (1,0) [0|7] "" XXX
 SG_ LEAD_LEFT_REAR_STATUS : 167|5@0+ (1,0) [0|31] "" XXX
 SG_ LEAD_LEFT_REAR_DISTANCE : 175|8@0+ (0.1,0) [0|255] "m" XXX
 SG_ LEAD_LEFT_REAR_LATERAL : 182|7@0+ (0.1,0) [0|127] "m" XXX
 SG_ ZEROS_8 : 183|1@0+ (1,0) [0|1] "" XXX
 SG_ ZEROS_9 : 191|8@0+ (1,0) [0|255] "" XXX
 SG_ LEAD_RIGHT_REAR_STATUS : 196|5@0+ (1,0) [0|31] "" XXX
 SG_ LEAD_RIGHT_REAR_DISTANCE : 197|8@1+ (0.1,0) [0|255] "m" XXX
 SG_ LEAD_RIGHT_REAR_LATERAL : 205|7@1+ (0.1,0) [0|127] "m" XXX
 SG_ ZEROS_10 : 212|1@0+ (1,0) [0|1] "" XXX
 SG_ FAULT_FSS : 213|3@1+ (1,0) [0|7] "" XXX
 SG_ FAULT_FCA : 216|3@1+ (1,0) [0|7] "" XXX
 SG_ FAULT_LSS : 219|3@1+ (1,0) [0|7] "" XXX
 SG_ FAULT_SLA : 222|3@1+ (1,0) [0|7] "" XXX
 SG_ FAULT_DAW : 225|3@1+ (1,0) [0|7] "" XXX
 SG_ FAULT_HBA : 228|3@1+ (1,0) [0|7] "" XXX
 SG_ FAULT_SCC : 231|3@1+ (1,0) [0|7] "" XXX
 SG_ FAULT_LFA : 234|3@1+ (1,0) [0|7] "" XXX
 SG_ FAULT_HDA : 237|3@1+ (1,0) [0|7] "" XXX
 SG_ FAULT_LCA : 240|3@1+ (1,0) [0|7] "" XXX
 SG_ FAULT_HDP : 243|3@1+ (1,0) [0|7] "" XXX
 SG_ FAULT_DAS : 246|3@1+ (1,0) [0|7] "" XXX
 SG_ FAULT_ESS : 249|3@1+ (1,0) [0|7] "" XXX
 SG_ ZEROS_11 : 252|4@1+ (1,0) [0|15] "" XXX

BO_ 357 SPAS1: 24 APRK
 SG_ CHECKSUM : 0|16@1+ (1,0) [0|65535] "" XXX
 SG_ COUNTER : 16|8@1+ (1,0) [0|0] "" XXX
 SG_ NEW_SIGNAL_2 : 90|3@1+ (1,0) [0|0] "" XXX
 SG_ NEW_SIGNAL_1 : 96|16@1- (0.1,0) [0|0] "" XXX

BO_ 362 SPAS2: 32 APRK
 SG_ CHECKSUM : 0|16@1+ (1,0) [0|65535] "" XXX
 SG_ COUNTER : 16|8@1+ (1,0) [0|0] "" XXX
 SG_ BLINKER_CONTROL : 133|3@1+ (1,0) [0|0] "" XXX

BO_ 373 TCS: 24 XXX
 SG_ CHECKSUM : 0|16@1+ (1,0) [0|65535] "" XXX
 SG_ COUNTER : 16|8@1+ (1,0) [0|255] "" XXX
 SG_ NEW_SIGNAL_4 : 24|7@1+ (1,0) [0|127] "" XXX
 SG_ aBasis : 32|11@1+ (0.01,-10.23) [0|7] "m/s^2" XXX
 SG_ ACCEL_REF_ACC : 48|11@1- (1,0) [0|1023] "" XXX
 SG_ EQUIP_MAYBE : 64|1@0+ (1,0) [0|1] "" XXX
 SG_ ACCEnable : 67|2@0+ (1,0) [0|3] "" XXX
 SG_ ACC_REQ : 68|1@0+ (1,0) [0|1] "" XXX
 SG_ NEW_SIGNAL_5 : 72|1@0+ (1,0) [0|1] "" XXX
 SG_ NEW_SIGNAL_2 : 74|1@0+ (1,0) [0|1] "" XXX
 SG_ NEW_SIGNAL_3 : 76|1@0+ (1,0) [0|1] "" XXX
 SG_ NEW_SIGNAL_1 : 80|1@0+ (1,0) [0|1] "" XXX
 SG_ DriverBraking : 81|1@0+ (1,0) [0|1] "" XXX
 SG_ DriverBrakingLowSens : 84|1@1+ (1,0) [0|1] "" XXX
 SG_ AEB_EQUIP_MAYBE : 96|1@0+ (1,0) [0|1] "" XXX
 SG_ NEW_SIGNAL_6 : 128|4@1+ (1,0) [0|15] "" XXX
 SG_ NEW_SIGNAL_7 : 135|2@0+ (1,0) [0|3] "" XXX
 SG_ PROBABLY_EQUIP : 136|2@1+ (1,0) [0|3] "" XXX

BO_ 416 SCC_CONTROL: 32 ADRV
 SG_ CHECKSUM : 0|16@1+ (1,0) [0|65535] "" XXX
 SG_ COUNTER : 16|8@1+ (1,0) [0|255] "" XXX
 SG_ ACC_ObjDist : 24|11@1+ (0.1,0) [0|204.7] "m" XXX
 SG_ ACC_ObjRelSpd : 35|9@1+ (0.1,-16.4) [-16.4|34.7] "m/s" XXX
 SG_ SET_ME_3 : 45|2@0+ (1,0) [0|3] "" XXX
 SG_ ObjValid : 46|1@0+ (1,0) [0|3] "" XXX
 SG_ SET_ME_TMP_64 : 55|8@0+ (1,0) [0|63] "" XXX
 SG_ ZEROS_7 : 63|8@0+ (1,0) [0|255] "" XXX
 SG_ NEW_SIGNAL_1 : 64|2@1+ (1,0) [0|3] "" XXX
 SG_ MainMode_ACC : 66|1@1+ (1,0) [0|1] "" XXX
 SG_ ACCMode : 68|3@1+ (1,0) [0|7] "" XXX
 SG_ ZEROS_9 : 71|5@1+ (1,0) [0|15] "" XXX
 SG_ CRUISE_STANDSTILL : 76|1@1+ (1,0) [0|1] "" XXX
 SG_ ZEROS_5 : 77|11@1+ (1,0) [0|2047] "" XXX
 SG_ DISTANCE_SETTING : 88|3@1+ (1,0) [0|3] "" XXX
 SG_ ZEROS_8 : 95|5@0+ (1,0) [0|31] "" XXX
 SG_ VSetDis : 103|8@0+ (1,0) [0|255] "km/h or mph" XXX
 SG_ NEW_SIGNAL_6 : 104|1@0+ (1,0) [0|1] "" XXX
 SG_ SET_ME_2 : 105|3@1+ (1,0) [0|7] "" XXX
 SG_ NEW_SIGNAL_3 : 109|2@0+ (1,0) [0|1] "" XXX
 SG_ ZEROS_10 : 111|2@0+ (1,0) [0|3] "" XXX
 SG_ ZEROS_6 : 119|16@0+ (1,0) [0|65535] "" XXX
 SG_ aReqValue : 128|11@1+ (0.01,-10.23) [-10.23|10.24] "m/s^2" XXX
 SG_ aReqRaw : 140|11@1+ (0.01,-10.23) [-10.23|10.24] "m/s^2" XXX
 SG_ JerkUpperLimit : 158|7@0+ (0.1,0) [0|0] "" XXX
 SG_ JerkLowerLimit : 166|7@0+ (0.1,0) [0|12.7] "m/s^3" XXX
 SG_ NEW_SIGNAL_2 : 168|2@1+ (1,0) [0|3] "" XXX
 SG_ NEW_SIGNAL_8 : 170|4@1+ (1,0) [0|15] "" XXX
 SG_ OBJ_STATUS : 176|3@1+ (1,0) [0|7] "" XXX
 SG_ ZEROS_4 : 183|4@0+ (1,0) [0|63] "" XXX
 SG_ StopReq : 184|1@0+ (1,0) [0|1] "" XXX
 SG_ ZEROS_3 : 191|7@0+ (1,0) [0|127] "" XXX
 SG_ NEW_SIGNAL_15 : 192|11@1+ (0.1,0) [0|204.7] "m" XXX
 SG_ ZEROS_2 : 207|5@0+ (1,0) [0|63] "" XXX
 SG_ ZEROS : 215|48@0+ (1,0) [0|281474976710655] "" XXX

BO_ 426 CRUISE_BUTTONS_ALT: 16 XXX
 SG_ CHECKSUM : 0|16@1+ (1,0) [0|65535] "" XXX
 SG_ COUNTER : 16|8@1+ (1,0) [0|255] "" XXX
 SG_ NEW_SIGNAL_1 : 24|4@1+ (1,0) [0|15] "" XXX
 SG_ SET_ME_1 : 28|2@1+ (1,0) [0|3] "" XXX
 SG_ DISTANCE_UNIT : 30|1@1+ (1,0) [0|1] "" XXX
 SG_ NEW_SIGNAL_2 : 31|3@1+ (1,0) [0|7] "" XXX
 SG_ ADAPTIVE_CRUISE_MAIN_BTN : 34|1@1+ (1,0) [0|1] "" XXX
 SG_ NEW_SIGNAL_3 : 35|1@1+ (1,0) [0|1] "" XXX
 SG_ CRUISE_BUTTONS : 36|3@1+ (1,0) [0|4] "" XXX
 SG_ LDA_BTN : 39|1@1+ (1,0) [0|1] "" XXX
 SG_ NEW_SIGNAL_4 : 40|1@1+ (1,0) [0|1] "" XXX
 SG_ NORMAL_CRUISE_MAIN_BTN : 41|1@1+ (1,0) [0|1] "" XXX
 SG_ NEW_SIGNAL_5 : 42|2@1+ (1,0) [0|3] "" XXX
 SG_ SET_ME_2 : 44|3@1+ (1,0) [0|7] "" XXX
 SG_ NEW_SIGNAL_6 : 47|1@1+ (1,0) [0|1] "" XXX
 SG_ BYTE6 : 48|8@1+ (1,0) [0|255] "" XXX
 SG_ BYTE7 : 56|8@1+ (1,0) [0|255] "" XXX
 SG_ BYTE8 : 64|8@1+ (1,0) [0|255] "" XXX
 SG_ BYTE9 : 72|8@1+ (1,0) [0|255] "" XXX
 SG_ BYTE10 : 80|8@1+ (1,0) [0|255] "" XXX
 SG_ BYTE11 : 88|8@1+ (1,0) [0|255] "" XXX
 SG_ BYTE12 : 96|8@1+ (1,0) [0|255] "" XXX
 SG_ BYTE13 : 104|8@1+ (1,0) [0|255] "" XXX
 SG_ BYTE14 : 112|8@1+ (1,0) [0|255] "" XXX
 SG_ BYTE15 : 120|8@1+ (1,0) [0|255] "" XXX

BO_ 437 FR_CMR_03_50ms: 32 FR_CMR
 SG_ FR_CMR_Crc3Val : 0|16@1+ (1,0) [0|65535] "" RR_C_RDR,CGW
 SG_ FR_CMR_AlvCnt3Val : 16|8@1+ (1,0) [0|255] "" RR_C_RDR,CGW
 SG_ Info_LftLnQualSta : 24|3@1+ (1,0) [0|7] "" RR_C_RDR,CGW
 SG_ Info_LftLnDptSta : 27|2@1+ (1,0) [0|3] "" RR_C_RDR,CGW
 SG_ Info_LftLnPosVal : 29|14@1- (0.0039625,0) [-32.4608|32.4568375] "m" RR_C_RDR,CGW
 SG_ Info_LftLnHdingAnglVal : 43|10@1- (0.000976563,0) [-0.500000256|0.499023693] "rad" RR_C_RDR,CGW
 SG_ Info_LftLnCvtrVal : 64|16@1- (1e-06,0) [-0.032768|0.032767] "1/m" CGW
 SG_ Info_LftLnCrvtrDrvtvVal : 80|16@1- (4e-09,0) [-0.000131072|0.000131068] "1/m2" CGW
 SG_ Info_RtLnQualSta : 96|3@1+ (1,0) [0|7] "" RR_C_RDR,CGW
 SG_ Info_RtLnDptSta : 99|2@1+ (1,0) [0|3] "" RR_C_RDR,CGW
 SG_ Info_RtLnPosVal : 101|14@1- (0.0039625,0) [-32.4608|32.4568375] "m" RR_C_RDR,CGW
 SG_ Info_RtLnHdingAnglVal : 115|10@1- (0.000976563,0) [-0.500000256|0.499023693] "rad" RR_C_RDR,CGW
 SG_ Info_RtLnCvtrVal : 128|16@1- (1,0) [0|65535] "" CGW
 SG_ Info_RtLnCrvtrDrvtvVal : 144|16@1- (1,0) [0|65535] "" CGW
 SG_ ID_CIPV : 192|7@1+ (1,0) [0|127] "" Dummy
 SG_ Longitudinal_Distance : 212|12@1+ (0.05,0) [0|204.75] "m" Dummy

BO_ 442 BLINDSPOTS_REAR_CORNERS: 24 XXX
 SG_ CHECKSUM : 0|16@1+ (1,0) [0|65535] "" XXX
 SG_ COUNTER : 16|8@1+ (1,0) [0|255] "" XXX
 SG_ LEFT_BLOCKED : 24|1@0+ (1,0) [0|1] "" XXX
 SG_ LEFT_MB : 30|1@0+ (1,0) [0|3] "" XXX
 SG_ MORE_LEFT_PROB : 32|1@1+ (1,0) [0|3] "" XXX
 SG_ FL_INDICATOR : 46|6@0+ (1,0) [0|1] "" XXX
 SG_ FR_INDICATOR : 54|6@0+ (1,0) [0|63] "" XXX
 SG_ RIGHT_BLOCKED : 64|1@0+ (1,0) [0|1] "" XXX
 SG_ COLLISION_AVOIDANCE_ACTIVE : 68|1@0+ (1,0) [0|1] "" XXX
 SG_ NEW_SIGNAL_2 : 96|1@0+ (1,0) [0|1] "" XXX
 SG_ FL_INDICATOR_ALT : 138|1@0+ (1,0) [0|1] "" XXX
 SG_ FR_INDICATOR_ALT : 141|1@0+ (1,0) [0|1] "" XXX

BO_ 463 CRUISE_BUTTONS: 8 XXX
 SG_ _CHECKSUM : 0|8@1+ (1,0) [0|65535] "" XXX
 SG_ COUNTER : 12|4@1+ (1,0) [0|255] "" XXX
 SG_ CRUISE_BUTTONS : 16|3@1+ (1,0) [0|3] "" XXX
 SG_ ADAPTIVE_CRUISE_MAIN_BTN : 19|1@1+ (1,0) [0|1] "" XXX
 SG_ NORMAL_CRUISE_MAIN_BTN : 21|1@1+ (1,0) [0|1] "" XXX
 SG_ LDA_BTN : 23|1@1+ (1,0) [0|1] "" XXX
 SG_ RIGHT_PADDLE : 25|1@1+ (1,0) [0|1] "" XXX
 SG_ LEFT_PADDLE : 27|1@1+ (1,0) [0|1] "" XXX
 SG_ SET_ME_1 : 29|1@1+ (1,0) [0|1] "" XXX

BO_ 474 ADRV_0x1da: 32 ADRV
 SG_ CHECKSUM : 0|16@1+ (1,0) [0|65535] "" XXX
 SG_ COUNTER : 16|8@1+ (1,0) [0|255] "" XXX
 SG_ SET_ME_22 : 31|8@0+ (1,0) [0|255] "" XXX
 SG_ SET_ME_41 : 47|8@0+ (1,0) [0|255] "" XXX

BO_ 480 LFAHDA_CLUSTER: 16 ADRV
 SG_ CHECKSUM : 0|16@1+ (1,0) [0|65535] "" XXX
 SG_ COUNTER : 16|8@1+ (1,0) [0|255] "" XXX
 SG_ NEW_SIGNAL_4 : 24|1@0+ (1,0) [0|1] "" XXX
 SG_ NEW_SIGNAL_5 : 25|1@0+ (1,0) [0|1] "" XXX
 SG_ NEW_SIGNAL_2 : 30|1@0+ (1,0) [0|1] "" XXX
 SG_ HDA_ICON : 31|1@1+ (1,0) [0|1] "" XXX
 SG_ NEW_SIGNAL_1 : 32|3@1+ (1,0) [0|7] "" XXX
 SG_ LFA_ICON : 47|2@1+ (1,0) [0|3] "" XXX
 SG_ NEW_SIGNAL_3 : 49|1@0+ (1,0) [0|1] "" XXX

BO_ 485 BLINDSPOTS_FRONT_CORNER_1: 16 XXX
 SG_ CHECKSUM : 0|16@1+ (1,0) [0|65535] "" XXX
 SG_ COUNTER : 16|8@1+ (1,0) [0|255] "" XXX
 SG_ REVERSING : 24|1@0+ (1,0) [0|1] "" XXX
 SG_ NEW_SIGNAL_5 : 31|2@0+ (1,0) [0|3] "" XXX
 SG_ NEW_SIGNAL_7 : 32|2@1+ (1,0) [0|3] "" XXX
 SG_ NEW_SIGNAL_8 : 47|8@0+ (1,0) [0|255] "" XXX
 SG_ NEW_SIGNAL_9 : 55|8@0+ (1,0) [0|255] "" XXX
 SG_ NEW_SIGNAL_4 : 80|1@0+ (1,0) [0|1] "" XXX
 SG_ NEW_SIGNAL_3 : 88|1@0+ (1,0) [0|1] "" XXX
 SG_ NEW_SIGNAL_2 : 96|1@0+ (1,0) [0|1] "" XXX
 SG_ NEW_SIGNAL_1 : 108|2@0+ (1,0) [0|3] "" XXX

BO_ 490 ADRV_0x1ea: 32 ADRV
 SG_ CHECKSUM : 0|16@1+ (1,0) [0|65535] "" XXX
 SG_ COUNTER : 16|8@1+ (1,0) [0|255] "" XXX
 SG_ SET_ME_1C : 31|8@0+ (1,0) [0|255] "" XXX
 SG_ NEW_SIGNAL_1 : 32|2@1+ (1,0) [0|3] "" XXX
 SG_ NEW_SIGNAL_2 : 47|2@0+ (1,0) [0|3] "" XXX
 SG_ NEW_SIGNAL_3 : 55|8@0+ (1,0) [0|255] "" XXX
 SG_ NEW_SIGNAL_4 : 64|6@1+ (1,0) [0|31] "" XXX
 SG_ NEW_SIGNAL_5 : 72|2@1+ (1,0) [0|3] "" XXX
 SG_ NEW_SIGNAL_6 : 75|5@1+ (1,0) [0|31] "" XXX
 SG_ NEW_SIGNAL_7 : 80|5@1+ (1,0) [0|31] "" XXX
 SG_ NEW_SIGNAL_8 : 88|7@1+ (1,0) [0|127] "" XXX
 SG_ NEW_SIGNAL_9 : 96|1@0+ (1,0) [0|1] "" XXX
 SG_ SET_ME_FF : 120|8@1+ (1,0) [0|255] "" XXX
 SG_ NEW_SIGNAL_10 : 143|5@0+ (1,0) [0|31] "" XXX
 SG_ NEW_SIGNAL_11 : 144|3@1+ (1,0) [0|7] "" XXX
 SG_ NEW_SIGNAL_12 : 152|6@1+ (1,0) [0|63] "" XXX
 SG_ NEW_SIGNAL_13 : 160|1@0+ (1,0) [0|1] "" XXX
 SG_ NEW_SIGNAL_14 : 163|5@1+ (1,0) [0|31] "" XXX
 SG_ NEW_SIGNAL_16 : 168|3@1+ (1,0) [0|7] "" XXX
 SG_ NEW_SIGNAL_15 : 175|4@0+ (1,0) [0|63] "" XXX
 SG_ NEW_SIGNAL_17 : 176|2@1+ (1,0) [0|3] "" XXX
 SG_ NEW_SIGNAL_18 : 184|1@0+ (1,0) [0|1] "" XXX
 SG_ NEW_SIGNAL_19 : 208|3@1+ (1,0) [0|7] "" XXX
 SG_ NEW_SIGNAL_20 : 212|1@0+ (1,0) [0|1] "" XXX
 SG_ SET_ME_TMP_F : 232|5@1+ (1,0) [0|31] "" XXX
 SG_ SET_ME_TMP_F_2 : 240|5@1+ (1,0) [0|31] "" XXX

BO_ 506 FR_CMR_02_100ms: 32 FR_CMR
 SG_ FR_CMR_Crc2Val : 0|16@1+ (1,0) [0|65535] "" CGW
 SG_ FR_CMR_AlvCnt2Val : 16|8@1+ (1,0) [0|255] "" CGW
 SG_ ISLW_OptUsmSta : 24|2@1+ (1,0) [0|3] "" CLU,CGW
 SG_ ISLW_SysSta : 26|2@1+ (1,0) [0|3] "" CLU,CGW
 SG_ ISLW_NoPassingInfoDis : 28|3@1+ (1,0) [0|7] "" CLU,CGW
 SG_ ISLW_OvrlpSignDis : 31|2@1+ (1,0) [0|3] "" CLU,CGW
 SG_ ISLW_SpdCluMainDis : 33|8@1+ (1,0) [0|255] "" CLU,CGW
 SG_ ISLW_SpdNaviMainDis : 41|8@1+ (1,0) [0|255] "" CGW
 SG_ ISLW_SubCondinfoSta1 : 49|4@1+ (1,0) [0|15] "" CLU,CGW
 SG_ ISLW_SubCondinfoSta2 : 53|4@1+ (1,0) [0|15] "" CLU,CGW
 SG_ ISLW_SpdCluSubMainDis : 64|8@1+ (1,0) [0|255] "" CLU
 SG_ ISLW_SpdCluDisSubCond1 : 72|8@1+ (1,0) [0|255] "" CLU,CGW
 SG_ ISLW_SpdCluDisSubCond2 : 80|8@1+ (1,0) [0|255] "" CLU,CGW
 SG_ ISLW_SpdNaviSubMainDis : 88|8@1+ (1,0) [0|255] "" CLU
 SG_ ISLW_SpdNaviDisSubCond1 : 96|8@1+ (1,0) [0|255] "" CLU,CGW
 SG_ ISLW_SpdNaviDisSubCond2 : 104|8@1+ (1,0) [0|255] "" CLU,CGW
 SG_ ISLA_SpdwOffst : 112|8@1+ (1,0) [0|255] "" CLU,CGW
 SG_ ISLA_SwIgnoreReq : 120|2@1+ (1,0) [0|3] "" CGW
 SG_ ISLA_SpdChgReq : 122|2@1+ (1,0) [0|3] "" CGW
 SG_ ISLA_SpdWrn : 124|2@1+ (1,0) [0|3] "" CLU,CGW
 SG_ ISLA_IcyWrn : 126|2@1+ (1,0) [0|3] "" CLU,CGW
 SG_ ISLA_SymFlashMod : 128|3@1+ (1,0) [0|7] "" CLU,CGW
 SG_ ISLA_Popup : 131|3@1+ (1,0) [0|7] "" CLU
 SG_ ISLA_OptUsmSta : 136|3@1+ (1,0) [0|7] "" CLU,CGW
 SG_ ISLA_OffstUsmSta : 139|3@1+ (1,0) [0|7] "" CLU,CGW
 SG_ ISLA_AutoUsmSta : 142|2@1+ (1,0) [0|3] "" CLU,CGW
 SG_ ISLA_Cntry : 144|4@1+ (1,0) [0|15] "" CLU,CGW
 SG_ ISLA_AddtnlSign : 149|5@1+ (1,0) [0|31] "" CLU,CGW
 SG_ ISLA_SchoolZone : 154|2@1+ (1,0) [0|3] "" CLU,CGW

BO_ 507 CAM_0x1fb: 32 CAMERA
 SG_ CHECKSUM : 0|16@1+ (1,0) [0|65535] "" XXX
 SG_ COUNTER : 16|8@1+ (1,0) [0|255] "" XXX

BO_ 512 ADRV_0x200: 8 ADRV
 SG_ CHECKSUM : 0|16@1+ (1,0) [0|65535] "" XXX
 SG_ COUNTER : 16|8@1+ (1,0) [0|255] "" XXX
 SG_ SET_ME_E1 : 24|8@1+ (1,0) [0|255] "" XXX
 SG_ SET_ME_3A : 32|8@1+ (1,0) [0|255] "" XXX

BO_ 593 RADAR_0x251: 16 FRONT_RADAR
 SG_ CHECKSUM : 0|16@1+ (1,0) [0|65535] "" XXX
 SG_ COUNTER : 16|8@1+ (1,0) [0|255] "" XXX

BO_ 698 FR_CMR_04_40ms: 32 FR_CMR
 SG_ FR_CMR_Crc4Val : 0|16@1+ (1,0) [0|65535] "" Dummy
 SG_ FR_CMR_AlvCnt4Val : 16|8@1+ (1,0) [0|255] "" Dummy
 SG_ IFSref_FR_CMR_Sta : 24|2@1+ (1,0) [0|3] "" CGW
 SG_ IFSref_VehNumVal : 26|4@1+ (1,0) [0|15] "" CGW
 SG_ IFSref_ILLAmbtSta : 30|2@1+ (0.1,0) [0|0.3] "" CGW
 SG_ IFSref_VehLftAngl1Val : 32|9@1+ (0.1,-25) [-25|26.1] "Deg" CGW
 SG_ IFSref_VehRtAngl1Val : 41|9@1+ (0.1,-25) [-25|26.1] "Deg" CGW
 SG_ IFSref_VehLftAngl2Val : 50|9@1+ (0.1,-25) [-25|26.1] "Deg" CGW
 SG_ IFSref_VehDst1Val : 59|4@1+ (1,0) [0|15] "" CGW
 SG_ IFSref_VehRtAngl2Val : 64|9@1+ (0.1,-25) [-25|26.1] "Deg" CGW
 SG_ IFSref_VehLftAngl3Val : 73|9@1+ (0.1,-25) [-25|26.1] "Deg" CGW
 SG_ IFSref_VehRtAngl3Val : 82|9@1+ (0.1,-25) [-25|26.1] "Deg" CGW
 SG_ IFSref_VehLftAngl4Val : 91|9@1+ (0.1,-25) [-25|26.1] "Deg" CGW
 SG_ IFSref_VehRtAngl4Val : 100|9@1+ (0.1,-25) [-25|26.1] "Deg" CGW
 SG_ IFSref_VehLftAngl5Val : 109|9@1+ (0.1,-25) [-25|26.1] "Deg" CGW
 SG_ IFSref_VehRtAngl5Val : 118|9@1+ (0.1,-25) [-25|26.1] "Deg" CGW
 SG_ IFSref_VehLftAngl6Val : 128|9@1+ (0.1,-25) [-25|26.1] "Deg" CGW
 SG_ IFSref_VehRtAngl6Val : 137|9@1+ (0.1,-25) [-25|26.1] "Deg" CGW
 SG_ IFSref_VehLftAngl7Val : 146|9@1+ (0.1,-25) [-25|26.1] "Deg" CGW
 SG_ IFSref_VehRtAngl7Val : 155|9@1+ (0.1,-25) [-25|26.1] "Deg" CGW
 SG_ IFSref_VehLftAngl8Val : 164|9@1+ (0.1,-25) [-25|26.1] "Deg" CGW
 SG_ IFSref_VehRtAngl8Val : 173|9@1+ (0.1,-25) [-25|26.1] "Deg" CGW
 SG_ IFSref_VehLftAngl9Val : 182|9@1+ (0.1,-25) [-25|26.1] "Deg" CGW
 SG_ IFSref_VehRtAngl9Val : 192|9@1+ (0.1,-25) [-25|26.1] "Deg" CGW
 SG_ IFSref_VehLftAngl10Val : 201|9@1+ (0.1,-25) [-25|26.1] "Deg" CGW
 SG_ IFSref_VehRtAngl10Val : 210|9@1+ (0.1,-25) [-25|26.1] "Deg" CGW
 SG_ IFSref_VehDst2Val : 219|4@1+ (1,0) [0|15] "" CGW
 SG_ IFSref_VehDst3Val : 223|4@1+ (1,0) [0|15] "" CGW
 SG_ IFSref_VehDst4Val : 227|4@1+ (1,0) [0|15] "" CGW
 SG_ IFSref_VehDst5Val : 231|4@1+ (1,0) [0|15] "" CGW
 SG_ IFSref_VehDst6Val : 235|4@1+ (1,0) [0|15] "" CGW
 SG_ IFSref_VehDst7Val : 239|4@1+ (1,0) [0|15] "" CGW
 SG_ IFSref_VehDst8Val : 243|4@1+ (1,0) [0|15] "" CGW
 SG_ IFSref_VehDst9Val : 247|4@1+ (1,0) [0|15] "" CGW
 SG_ IFSref_VehDst10Val : 251|4@1+ (1,0) [0|15] "" CGW

BO_ 736 MANUAL_SPEED_LIMIT_ASSIST: 32 XXX
 SG_ CHECKSUM : 0|16@1+ (1,0) [0|65535] "" XXX
 SG_ COUNTER : 16|8@1+ (1,0) [0|255] "" XXX
 SG_ MSLA_STATUS : 26|2@1+ (1,0) [0|3] "" XXX
 SG_ MSLA_ENABLED : 38|1@1+ (1,0) [0|1] "" XXX
 SG_ MAX_SPEED : 55|8@0+ (1,0) [0|255] "" XXX
 SG_ MAX_SPEED_COPY : 144|8@1+ (1,0) [0|255] "" XXX

BO_ 837 ADRV_0x345: 8 ADRV
 SG_ CHECKSUM : 0|16@1+ (1,0) [0|65535] "" XXX
 SG_ COUNTER : 16|8@1+ (1,0) [0|255] "" XXX
 SG_ SET_ME_15 : 24|8@1+ (1,0) [0|255] "" XXX

BO_ 866 CAM_0x362: 32 CAMERA
 SG_ CHECKSUM : 0|16@1+ (1,0) [0|65535] "" XXX
 SG_ COUNTER : 16|8@1+ (1,0) [0|255] "" XXX
 SG_ BYTE3 : 24|8@1+ (1,0) [0|255] "" XXX
 SG_ BYTE4 : 32|8@1+ (1,0) [0|255] "" XXX
 SG_ BYTE5 : 40|8@1+ (1,0) [0|255] "" XXX
 SG_ BYTE6 : 48|8@1+ (1,0) [0|255] "" XXX
 SG_ LEFT_LANE_LINE : 56|2@1+ (1,0) [0|3] "" XXX
 SG_ SET_ME_0 : 58|2@1+ (1,0) [0|3] "" XXX
 SG_ RIGHT_LANE_LINE : 60|2@1+ (1,0) [0|3] "" XXX
 SG_ SET_ME_0_2 : 62|2@1+ (1,0) [0|3] "" XXX
 SG_ BYTE8 : 64|8@1+ (1,0) [0|255] "" XXX
 SG_ BYTE9 : 72|8@1+ (1,0) [0|255] "" XXX
 SG_ BYTE10 : 80|8@1+ (1,0) [0|255] "" XXX
 SG_ BYTE11 : 88|8@1+ (1,0) [0|255] "" XXX
 SG_ BYTE12 : 96|8@1+ (1,0) [0|255] "" XXX
 SG_ BYTE13 : 104|8@1+ (1,0) [0|255] "" XXX
 SG_ BYTE14 : 112|8@1+ (1,0) [0|255] "" XXX
 SG_ BYTE15 : 120|8@1+ (1,0) [0|255] "" XXX
 SG_ BYTE16 : 128|8@1+ (1,0) [0|255] "" XXX
 SG_ BYTE17 : 136|8@1+ (1,0) [0|255] "" XXX
 SG_ BYTE18 : 144|8@1+ (1,0) [0|255] "" XXX
 SG_ BYTE19 : 152|8@1+ (1,0) [0|255] "" XXX
 SG_ BYTE20 : 160|8@1+ (1,0) [0|255] "" XXX
 SG_ BYTE21 : 168|8@1+ (1,0) [0|255] "" XXX
 SG_ BYTE22 : 176|8@1+ (1,0) [0|255] "" XXX
 SG_ BYTE23 : 184|8@1+ (1,0) [0|255] "" XXX
 SG_ BYTE24 : 192|8@1+ (1,0) [0|255] "" XXX
 SG_ BYTE25 : 200|8@1+ (1,0) [0|255] "" XXX
 SG_ BYTE26 : 208|8@1+ (1,0) [0|255] "" XXX
 SG_ BYTE27 : 216|8@1+ (1,0) [0|255] "" XXX
 SG_ BYTE28 : 224|8@1+ (1,0) [0|255] "" XXX
 SG_ BYTE29 : 232|8@1+ (1,0) [0|255] "" XXX
 SG_ BYTE30 : 240|8@1+ (1,0) [0|255] "" XXX
 SG_ BYTE31 : 248|8@1+ (1,0) [0|255] "" XXX

BO_ 874 BLINDSPOTS_FRONT_CORNER_2: 16 XXX
 SG_ CHECKSUM : 0|16@1+ (1,0) [0|65535] "" XXX
 SG_ COUNTER : 16|8@1+ (1,0) [0|255] "" XXX

BO_ 961 BLINKER_STALKS: 8 XXX
 SG_ CHECKSUM_MAYBE : 7|8@0+ (1,0) [0|255] "" XXX
 SG_ COUNTER_ALT : 15|4@0+ (1,0) [0|15] "" XXX
 SG_ HIGHBEAM_FORWARD : 18|1@0+ (1,0) [0|1] "" XXX
 SG_ LIGHT_KNOB_POSITION : 21|2@0+ (1,0) [0|3] "" XXX
 SG_ HIGHBEAM_BACKWARD : 26|1@0+ (1,0) [0|1] "" XXX
 SG_ LEFT_BLINKER : 30|1@0+ (1,0) [0|1] "" XXX
 SG_ RIGHT_BLINKER : 32|1@0+ (1,0) [0|1] "" XXX

BO_ 1041 DOORS_SEATBELTS: 8 XXX
 SG_ CHECKSUM_MAYBE : 7|8@0+ (1,0) [0|65535] "" XXX
 SG_ COUNTER_ALT : 15|4@0+ (1,0) [0|15] "" XXX
 SG_ DRIVER_DOOR : 24|1@1+ (1,0) [0|1] "" XXX
 SG_ PASSENGER_DOOR : 34|1@0+ (1,0) [0|1] "" XXX
 SG_ PASSENGER_SEATBELT : 36|1@0+ (1,0) [0|1] "" XXX
 SG_ DRIVER_SEATBELT : 42|1@0+ (1,0) [0|1] "" XXX
 SG_ DRIVER_REAR_DOOR : 52|1@0+ (1,0) [0|1] "" XXX
 SG_ PASSENGER_REAR_DOOR : 56|1@0+ (1,0) [0|1] "" XXX

BO_ 1043 BLINKERS: 8 XXX
 SG_ LEFT_STALK : 8|1@0+ (1,0) [0|1] "" XXX
 SG_ RIGHT_STALK : 10|1@0+ (1,0) [0|1] "" XXX
 SG_ COUNTER_ALT : 15|4@0+ (1,0) [0|15] "" XXX
 SG_ LEFT_LAMP : 20|1@0+ (1,0) [0|1] "" XXX
 SG_ RIGHT_LAMP : 22|1@0+ (1,0) [0|1] "" XXX
 SG_ LEFT_LAMP_ALT : 59|1@0+ (1,0) [0|1] "" XXX
 SG_ RIGHT_LAMP_ALT : 61|1@0+ (1,0) [0|1] "" XXX
 SG_ USE_ALT_LAMP : 62|1@0+ (1,0) [0|1] "" XXX

BO_ 1144 DRIVE_MODE: 8 XXX
 SG_ DRIVE_MODE : 0|16@1+ (1,-61611) [0|61611] "" XXX
 SG_ DRIVE_MODE2 : 28|3@1+ (1,0) [1|3] "" XXX

BO_ 1151 HVAC_TOUCH_BUTTONS: 8 XXX
 SG_ AUTO_BUTTON : 8|1@0+ (1,0) [0|1] "" XXX
 SG_ SYNC_BUTTON : 12|1@0+ (1,0) [0|1] "" XXX
 SG_ FR_DEFROST_BUTTON : 20|1@0+ (1,0) [0|1] "" XXX
 SG_ RR_DEFROST_BUTTON : 22|1@0+ (1,0) [0|1] "" XXX
 SG_ FAN_SPEED_UP_BUTTON : 24|1@0+ (1,0) [0|1] "" XXX
 SG_ FAN_SPEED_DOWN_BUTTON : 26|1@0+ (1,0) [0|1] "" XXX
 SG_ AIR_DIRECTION_BUTTON : 28|1@0+ (1,0) [0|1] "" XXX
 SG_ AC_BUTTON : 40|1@0+ (1,0) [0|1] "" XXX
 SG_ DRIVER_ONLY_BUTTON : 44|1@0+ (1,0) [0|1] "" XXX
 SG_ RECIRC_BUTTON : 48|1@0+ (1,0) [0|1] "" XXX
 SG_ HEAT_BUTTON : 52|1@0+ (1,0) [0|1] "" XXX

BO_ 1240 CLUSTER_INFO: 8 XXX
 SG_ DISTANCE_UNIT : 0|1@1+ (1,0) [0|1] "" XXX

BO_ 1259 LOCAL_TIME2: 8 XXX
 SG_ HOURS : 15|5@0+ (1,0) [0|31] "" XXX
 SG_ MINUTES : 21|6@0+ (1,0) [0|63] "" XXX
 SG_ SECONDS : 24|6@1+ (1,0) [0|63] "" XXX
 SG_ NEW_SIGNAL_3 : 39|1@0+ (1,0) [0|1] "" XXX

BO_ 1264 LOCAL_TIME: 8 XXX
 SG_ HOURS : 12|5@0+ (1,0) [0|31] "" XXX
 SG_ MINUTES : 21|6@0+ (1,0) [0|63] "" XXX
 SG_ SECONDS : 31|8@0+ (1,0) [0|59] "" XXX

CM_ SG_ 96 BRAKE_PRESSURE "User applied brake pedal pressure. Ramps from computer applied pressure on falling edge of cruise. Cruise cancels if !=0";
CM_ SG_ 101 BRAKE_POSITION "User applied brake pedal position, max is ~700. Signed on some vehicles";
CM_ BO_ 272 "Alternative LKAS message, used on cars such as 2023 Ioniq 6, 2nd gen Kona. Matches LKAS except size is 32 bytes";
CM_ SG_ 272 LKA_AVAILABLE "Angle control cars: 3 when LKA is generally available, goes to 0 during changes with LFA";
CM_ SG_ 272 LKAS_ANGLE_CMD "tracks MDPS->STEERING_ANGLE when not engaged, not STEERING_SENSORS->STEERING_ANGLE";
CM_ SG_ 298 NEW_SIGNAL_4 "todo: figure out why always set to 9";
CM_ SG_ 352 SET_ME_9 "has something to do with AEB settings";
CM_ SG_ 373 ACCEnable "Likely a copy of CAN's TCS13->ACCEnable";
CM_ SG_ 373 DriverBraking "Likely derived from BRAKE->BRAKE_POSITION";
CM_ SG_ 373 DriverBrakingLowSens "Higher threshold version of DriverBraking";
CM_ SG_ 373 PROBABLY_EQUIP "aeb equip?";
CM_ SG_ 416 VSetDis "set speed in display units";
CM_ SG_ 480 NEW_SIGNAL_5 "todo: figure out why always set to 1";
CM_ SG_ 736 MAX_SPEED "Display units. Restricts car from driving above this speed unless accelerator pedal is depressed beyond pressure point";
CM_ BO_ 866 "Contains signals with detailed lane line information. Used by ADAS ECU on HDA 2 vehicles to operate LFA. Used on cars that use message 272.";
CM_ SG_ 866 LEFT_LANE_LINE "Left lane line confidence";
CM_ SG_ 866 RIGHT_LANE_LINE "Right lane line confidence";
CM_ SG_ 961 COUNTER_ALT "only increments on change";
CM_ SG_ 1041 COUNTER_ALT "only increments on change";
CM_ BO_ 1043 "Lamp signals do not seem universal on cars that use LKAS_ALT, but stalk signals do.";
CM_ SG_ 1043 COUNTER_ALT "only increments on change";
CM_ SG_ 1043 USE_ALT_LAMP "likely 1 on cars that use alt lamp signals";
VAL_ 53 GEAR 0 "P" 5 "D" 6 "N" 7 "R";
VAL_ 64 GEAR 0 "P" 5 "D" 6 "N" 7 "R";
VAL_ 69 GEAR 0 "P" 5 "D" 6 "N" 7 "R";
VAL_ 96 TRACTION_AND_STABILITY_CONTROL 0 "On" 5 "Limited" 1 "Off";
VAL_ 112 GEAR 0 "P" 5 "D" 6 "N" 7 "R";
VAL_ 234 LKA_FAULT 0 "ok" 1 "lka fault";
VAL_ 272 LKA_MODE 1 "warning only" 2 "assist" 6 "off";
VAL_ 272 LKA_ICON 0 "hidden" 1 "grey" 2 "green" 3 "flashing green";
VAL_ 272 LKAS_ANGLE_ACTIVE 0 "off" 1 "not active" 2 "active";
VAL_ 282 HBA_SysOptSta 0 "None HBA Option (Default)" 1 "HBA Option" 2 "Reserved" 3 "Error indicator";
VAL_ 282 HBA_SysSta 0 "HBA Disable" 1 "HBA Enable & High Beam Off" 2 "HBA Enable & High Beam On" 3 "Reserved" 4 "Reserved" 5 "Reserved" 6 "Reserved" 7 "System Fail";
VAL_ 282 HBA_IndLmpReq 0 "HBA Indicator Lamp Off" 1 "HBA Indicator Lamp On" 2 "Reserved" 3 "Error indicator";
VAL_ 282 FCA_Equip_MFC 0 "No Coding" 1 "Sensor Fusion FCA" 2 "Camera only FCA" 3 "No FCA Option" 4 "ADAS_DRV Option" 5 "Reserved" 6 "Not used" 7 "Error indicator";
VAL_ 282 HBA_OptUsmSta 0 "None HBA Option (Default)" 1 "HBA Function Off" 2 "HBA Function On" 3 "Invalid (Fail)";
VAL_ 282 DAW_LVDA_PUDis 0 "Default" 1 "Display “Leading vehicle departure alert”" 2 "Reserved" 3 "Error indicator";
VAL_ 282 DAW_LVDA_OptUsmSta 0 "No Option (default)" 1 "Off" 2 "On" 3 "Error Indicator";
VAL_ 282 DAW_OptUsmSta 0 "None DAW Option (Default)" 1 "System Off" 2 "System On" 3 "Reserved" 4 "Reserved" 5 "Reserved" 6 "Reserved" 7 "Invalid (Gray)";
VAL_ 282 DAW_SysSta 0 "System Off" 1 "Attention Level  1" 2 "Attention Level  2" 3 "Attention Level  3" 4 "Attention Level  4" 5 "Attention Level  5" 6 "Reserved" 7 "Reserved" 8 "Reserved" 9 "Reserved" 10 "Reserved" 11 "Reserved" 12 "Reserved" 13 "Reserved" 14 "System Standby" 15 "System Fail";
VAL_ 282 DAW_WrnMsgSta 0 "No Warning" 1 "Rest Recommend Warning" 2 "Hands-Off TMS call request" 3 "Reserved" 4 "Reserved" 5 "Reserved" 6 "Reserved" 7 "Error indicator";
VAL_ 282 DAW_TimeRstReq 0 "No signal" 1 "Time reset" 2 "Reserved" 3 "Error indicator";
VAL_ 282 DAW_SnstvtyModRetVal 0 "Default" 1 "Late" 2 "Normal" 3 "Reserved" 4 "Reserved" 5 "Reserved" 6 "Reserved" 7 "Invalid";
VAL_ 282 FR_CMR_SCCEquipSta 0 "Not Applied" 1 "Applied" 2 "Not used" 3 "Error Indicator";
VAL_ 298 LKA_MODE 1 "warning only" 2 "assist" 6 "off";
VAL_ 298 LKA_ICON 0 "hidden" 1 "grey" 2 "green" 3 "flashing green";
VAL_ 304 PARK_BUTTON 1 "Pressed" 2 "Not Pressed";
VAL_ 304 KNOB_POSITION 1 "R" 2 "N (on R side)" 3 "Centered" 4 "N (on D side)" 5 "D";
VAL_ 304 GEAR 1 "P" 2 "R" 3 "N" 4 "D";
VAL_ 352 AEB_SETTING 1 "off" 2 "warning only" 3 "active assist";
VAL_ 353 FCA_ICON 0 "HIDDEN" 1 "ORANGE" 2 "RED";
VAL_ 353 FCA_ALT_ICON 0 "HIDDEN" 1 "ORANGE" 3 "RED";
VAL_ 353 LKA_ICON 0 "HIDDEN" 1 "ORANGE" 3 "GRAY" 4 "GREEN";
VAL_ 353 HBA_ICON 0 "HIDDEN" 1 "GRAY" 2 "GREEN";
VAL_ 353 FCA_IMAGE 0 "HIDDEN" 2 "VISIBLE";
VAL_ 353 BCA_LEFT 0 "HIDDEN" 1 "VISIBLE" 2 "VISIBLE+ICON";
VAL_ 353 BCA_RIGHT 0 "HIDDEN" 1 "VISIBLE" 2 "VISIBLE+ICON";
VAL_ 353 LCA_LEFT_ARROW 0 "HIDDEN" 1 "VISIBLE";
VAL_ 353 LCA_RIGHT_ARROW 0 "HIDDEN" 1 "VISIBLE";
VAL_ 353 CENTERLINE 0 "HIDDEN" 1 "GREEN";
VAL_ 353 TARGET 0 "HIDDEN" 1 "BLUE" 3 "WHITE";
VAL_ 353 LANELINE_LEFT 0 "GRAY" 1 "HIDDEN" 2 "WHITE" 4 "ORANGE" 6 "GREEN";
VAL_ 353 LANELINE_RIGHT 0 "GRAY" 1 "HIDDEN" 2 "WHITE" 4 "ORANGE" 6 "GREEN";
VAL_ 353 LANE_HIGHLIGHT 0 "HIDDEN" 1 "GREEN" 2 "WHITE" 3 "BLUE" 4 "ORANGE" 5 "RED";
VAL_ 353 LANE_LEFT 0 "HIDDEN" 1 "GREEN";
VAL_ 353 LANE_RIGHT 0 "HIDDEN" 1 "GREEN";
VAL_ 353 LANE_ZOOM 0 "ZOOM" 1 "HIDDEN";
VAL_ 353 ALERTS_1 0 "HIDDEN" 1 "WARNING_ONLY_CAR_CENTER" 2 "WARNING_ONLY_CAR_LEFT" 3 "WARNING_ONLY_CAR_RIGHT" 4 "WARNING_ONLY_LEFT" 5 "WARNING_ONLY_RIGHT" 11 "EMERGENCY_BRAKING_CAR_CENTER" 12 "EMERGENCY_BRAKING_CAR_LEFT" 13 "EMERGENCY_BRAKING_CAR_RIGHT" 14 "EMERGENCY_BRAKING_LEFT" 15 "EMERGENCY_BRAKING_RIGHT" 21 "EMERGENCY_STEERING_CAR_LEFT" 22 "EMERGENCY_STEERING_CAR_RIGHT" 23 "EMERGENCY_STEERING_CAR_LEFT_AWAY" 24 "EMERGENCY_STEERING_CAR_RIGHT_AWAY" 25 "EMERGENCY_STEERING_REAR_LEFT" 26 "EMERGENCY_STEERING_REAR_RIGHT" 33 "DRIVE_CAREFULLY";
VAL_ 353 ALERTS_2 0 "HIDDEN" 1 "KEEP_HANDS_ON_STEERING_WHEEL" 2 "KEEP_HANDS_ON_STEERING_WHEEL_RED" 3 "LANE_FOLLOWING_ASSIST_DEACTIVATED" 4 "HIGHWAY_DRIVING_ASSIST_DEACTIVATED" 5 "CONSIDER_TAKING_A_BREAK" 6 "PRESS_OK_BUTTON_TO_ENABLE_LANE_CHANGE_ASSIST" 7 "COLLISION_RISK_VEHICLE_TAKING_EMERGENCY_CONTROL" 8 "TAKE_CONTROL_OF_THE_VEHICLE_IMMEDIATELY_VEHICLE_IS_STOPPING" 9 "TAKE_CONTROL_OF_THE_VEHICLE_IMMEDIATELY" 11 "HIGHWAY_DRIVING_PILOT_SYSTEM_DEACTIVATED_AUDIBLE" 12 "KEEP_YOUR_EYES_ON_THE_ROAD" 13 "HIGHWAY_DRIVING_PILOT_CONDITIONS_NOT_MET_AUDIBLE" 14 "COLLISION_RISK_VEHICLE_TAKING_EMERGENCY_CONTROL" 15 "SET_THE_WIPER_AND_LIGHT_CONTROLS_TO_AUTO" 16 "BE_PREPARED_TO_TAKE_CONTROL_OF_THE_VEHICLE_AT_ANY_TIME" 21 "TAKE_CONTROL_OF_THE_VEHICLE_IMMEDIATELY_VEHICLE_IS_STOPPING" 10 "TAKE_CONTROL_OF_THE_VEHICLE_IMMEDIATELY";
VAL_ 353 ALERTS_3 1 "AUTOMATICALLY_ADJUSTING_TO_THE_POSTED_SPEED_LIMIT" 2 "SET_SPEED_CHANGED" 3 "AUTOMATICALLY_ADJUSTING_TO_THE_POSTED_SPEED_LIMIT" 4 "SET_SPEED_CHANGED" 7 "DISTANCE_1" 8 "DISTANCE_2" 9 "DISTANCE_3" 10 "DISTANCE_4" 17 "DRIVE_CAREFULLY" 18 "CHECK_SURROUNDINGS" 19 "CONDITIONS_NOT_MET" 20 "LANES_NOT_DETECTED" 21 "CURVE_TOO_SHARP" 22 "LANE_TOO_NARROW" 23 "ROAD_TYPE_NOT_SUPPORTED" 24 "UNAVAILABLE_WITH_HAZARD_LIGHTS_ON" 25 "VEHICLE_SPEED_IS_TOO_LOW" 26 "KEEP_HANDS_ON_STEERING_WHEEL" 27 "LANE_TYPE_NOT_SUPPORTED" 28 "LANE_ASSIST_CANCELED_STEERING_INPUT_DETECTED" 0 "HIDDEN";
VAL_ 353 ALERTS_4 0 "HIDDEN" 1 "TAKE_FOOT_OFF_THE_ACCELERATOR_PEDAL" 2 "TAKE_FOOT_OFF_THE_BRAKE_PEDAL" 3 "UNAVAILABLE_WHILE_HIGHWAY_DRIVING_PILOT_SYSTEM_IS_ACTIVE" 4 "TO_EXIT_HDP_GRASP_THE_STEERING_WHEEL_THEN_PRESS_AND_HOLD_THE_HDP_BUTTON" 5 "ACCELERATOR_PEDAL_OPERATION_LIMITED_FOR_SAFETY" 6 "TURN_OFF_HAZARD_WARNING_LIGHTS_AND_TURN_SIGNAL" 7 "KEEP_THE_DRIVERS_SEAT_IN_A_SAFE_DRIVING_POSITION" 16 "SET_SPEED_CHANGED" 17 "ACTIVATING_WINDSHIELD_DEFOG_TO_MAINTAIN_THE_DRIVERS_VIEW" 18 "SET_THE_WIPER_AND_LIGHT_CONTROLS_TO_AUTO" 19 "VEHICLE_SPEED_REDUCED_FOR_SAFETY_MERGING_LANES_AHEAD" 20 "SPEED_REDUCED_FOR_SAFETY_CONSTRUCTION_ZONE_DETECTED" 21 "VEHICLE_SPEED_LIMITED_SENSOR_DETECTION_RANGE_LIMITED" 22 "PREPARE_TO_TAKE_CONTROL_UNSUPPORTED_ROAD_TYPE_AHEAD" 23 "PREPARE_TO_TAKE_CONTROL_ENTRANCE_AND_EXIT_RAMPS_AHEAD" 24 "PREPARE_TO_TAKE_CONTROL_TOLLGATE_AHEAD" 25 "PREPARE_TO_TAKE_CONTROL_ROAD_EVENT_AHEAD" 26 "CLEARING_PATH_FOR_EMERGENCY_VEHICLE" 27 "VEHICLE_IS_TOO_SLOW_COMPARED_TO_TRAFFIC_FLOW" 28 "AFTER_SUNSET_HDP_IS_AVAILABLE_IN_AN_INSIDE_LANE_BEHIND_A_LEADING_VEHICLE" 29 "VEHICLE_SPEED_LIMITED_MERGING_LANES_AHEAD" 30 "VEHICLE_SPEED_LIMITED_CONSTRUCTION_ZONE_DETECTED" 31 "VEHICLE_SPEED_TEMPORARILY_LIMITED_FOR_SAFETY" 32 "PRESS_AND_HOLD_THE_BUTTON_TO_ACTIVATE_HIGHWAY_DRIVING_PILOT" 40 "HIGHWAY_DRIVING_PILOT_SYSTEM_IS_AVAILABLE" 64 "RESTART_VEHICLE_AFTER_EMERGENCY_STOP" 65 "CONNECTED_SERVICES_UNAVAILABLE" 66 "AVAILABLE_AFTER_VEHICLE_SOFTWARE_IS_UPDATED" 67 "ROAD_TYPE_NOT_SUPPORTED" 68 "ONLY_AVAILABLE_WHILE_DRIVING_ON_HIGHWAY_LANES" 69 "UNAVAILABLE_WHILE_OTHER_WARNINGS_ARE_ACTIVE" 70 "CANNOT_ACTIVATE_AT_ENTRANCE_EXIT_RAMPS" 71 "LANE_UNSUPPORTED" 72 "NOT_AVAILABLE_IN_THIS_COUNTRY" 79 "CHECKING_THE_DETECTION_RANGE_OF_THE_SENSOR" 80 "SHIFT_TO_D" 81 "ENGINE_STOPPED_BY_AUTO_STOP" 82 "INCREASE_DISTANCE_FROM_VEHICLE_AHEAD" 83 "VEHICLE_SPEED_IS_TOO_HIGH" 84 "CENTER_VEHICLE_IN_THE_LANE" 85 "PARKING_ASSIST_IS_ACTIVE" 86 "ESC_ACTIVIATION_REQUIRED" 87 "UNFOLD_SIDE_VIEW_MIRRORS" 88 "UNAVAILABLE_IN_THE_OUTER_LANE_AFTER_SUNSET" 89 "VEHICLE_SPEED_LIMITED_AFTER_SUNSET_FOR_SAFETY" 90 "LEADING_VEHICLE_NOT_DETECTED" 104 "AGGRESSIVE_BRAKING_OR_STEERING_DETECTED" 110 "SENSOR_AUTO_CALIBRATION_IN_PROGRESS_THIS_MAY_TAKE_SEVERAL_MINUTES" 111 "HIGHWAY_DRIVING_PILOT_WILL_BE_AVAILABLE_SHORTLY" 112 "IF_STEERING_WHEEL_IS_USED_HDP_WILL_BE_DEACTIVATED" 120 "IMPACT_DETECTED" 128 "UNSUITABLE_USE_OF_ACCELERATOR_PEDAL_DETECTED" 129 "GEAR_SHIFTER_USE_DETECTED" 130 "UNSUITABLE_BRAKE_PEDAL_USE_DETECTED" 131 "VEHICLE_START_BUTTON_PRESSED" 132 "VEHICLE_HAS_BEEN_STOPPED_FOR_TOO_LONG" 141 "TRAFFIC_CONGESTION_HAS_CLEARED" 142 "ENTRANCE_AND_EXIT_RAMPS_AHEAD" 143 "UNSUPPORTED_LANE_AHEAD" 144 "UNSUPPORTED_ROAD_TYPE_AHEAD" 145 "LANE_DEPARTURE_DETECTED" 146 "MAXIMUM_SPEED_EXCEEDED" 147 "HIGHWAY_DRIVING_PILOT_LIMITED_ABNORMAL_VEHICLE_CONTROLLER_STATUS" 148 "WIPER_LIGHT_CONTROL_SETTINGS_ARE_UNSUITABLE_FOR_USE_WITH_HDP" 149 "WINDSHIELD_DEFOG_SYSTEM_STATUS_IS_UNSUITABLE_FOR_USE_WITH_HDP" 150 "HAZARD_WARNING_LIGHTS_OR_TURN_SIGNAL_OPERATION_DETECTED" 151 "PERFORMING_EVASIVE_STEERING_OBSTACLES_DETECTED_AHEAD" 152 "HIGHWAY_DRIVING_PILOT_LIMITED_SENSOR_DETECTION_RANGE_LIMITED" 160 "CHECK_HIGHWAY_DRIVING_PILOT_SYSTEM" 161 "SAFETY_FUNCTION_ACTIVATED" 176 "CAMERA_OBSCURED" 177 "RADAR_BLOCKED" 178 "LIDAR_BLOCKED" 179 "AIRBAG_WARNING_LIGHT_IS_ON" 180 "ATTACHED_TRAILED_DETECTED" 181 "HIGH_OUTSIDE_TEMPERATURE" 182 "LOW_OUTSIDE_TEMPERATURE" 190 "UNAVAILABLE_DUE_TO_THE_ROAD_EVENT_INFORMATION_RECEIVED" 191 "UNAVAILABLE_NEAR_TOLLGATES" 192 "DRIVERS_SEAT_IS_NOT_IN_A_SAFE_DRIVING_POSITION" 193 "VEHICLE_DRIVING_THE_WRONG_WAY_DETECTED_AHEAD" 194 "EMERGENCY_VEHICLE_DETECTED" 195 "OBSTACLE_DETECTED_AHEAD" 196 "SENSOR_BLOCKED_DUE_TO_RAIN_SNOW_OR_ROAD_DEBRIS" 197 "SLIPPERY_ROAD_SURFACE_DETECTED" 198 "CONSTRUCTION_ZONE_DETECTED_AHEAD" 199 "PEDESTRIAN_DETECTED_AHEAD" 200 "UNSUITABLE_DRIVERS_SEAT_POSITION_DETECTED" 201 "FOLDED_SIDE_VIEW_MIRRORS_DETECTED" 208 "VEHICLE_POSITION_NOT_DETECTED" 209 "LANE_NOT_DETECTED" 210 "DRIVER_NOT_DETECTED" 211 "KEEP_YOUR_EYES_ON_THE_ROAD" 212 "LEADING_VEHICLE_REQUIRED_AFTER_SUNSET" 213 "TBD" 240 "LOW_FUEL" 241 "LOW_TIRE_PRESSURE" 242 "DOOR_OPEN" 243 "TRUNK_OPEN" 244 "HOOD_OPEN" 245 "SEAT_BELT_NOT_FASTENED" 246 "PARKING_BRAKE_ACTIVATED" 247 "LOW_EV_BATTERY" 248 "HDP_DEACTIVATION_DELAYED_RISK_OF_COLLISION_DETECTED" 249 "LIFTGATE_OPENED";
VAL_ 353 ALERTS_5 0 "HIDDEN" 1 "DRIVERS_GRASP_NOT_DETECTED_DRIVING_SPEED_WILL_BE_LIMITED" 2 "WATCH_FOR_SURROUNDING_VEHICLES" 3 "SMART_CRUISE_CONTROL_DEACTIVATED" 4 "SMART_CRUISE_CONTROL_CONDITIONS_NOT_MET" 5 "USE_SWITCH_OR_PEDAL_TO_ACCELERATE" 6 "DRIVER_ASSISTNCE_SYSTEM_LIMITED_TRAILER_ATTACHED" 7 "DRIVER_ASSISTNCE_SYSTEM_LIMITED_DRIVER_FULL_FACE_NOT_VISIBLE" 11 "LEADING_VEHICLE_IS_DRIVING_AWAY" 12 "STOP_VEHICLE_THEN_TRY_AGAIN" 19 "ACTIVATING_HIGHWAY_DRIVING_PILOT_SYSTEM" 20 "CONTINUING_USE_OF_HIGHWAY_DRIVING_PILOT_WILL_RESULT_IN_DEVIATION_FROM_THE_NAVIGATION_ROUTE" 21 "HIGHWAY_DRIVING_PILOT_SYSTEM_DEACTIVATED_SILENT" 22 "HIGHWAY_DRIVING_PILOT_SYSTEM_NOT_APPLIED" 23 "HIGHWAY_DRIVING_PILOT_CONDITIONS_NOT_MET_SILENT";
VAL_ 353 MUTE 0 "NONE" 1 "MUTED";
VAL_ 353 SOUNDS_1 0 "NONE" 3 "FAST BEEP" 6 "CONSTANT BEEP";
VAL_ 353 SOUNDS_2 0 "NONE" 2 "SINGLE CHIME" 3 "CONSTANT CHIME" 6 "FAST BEEP";
VAL_ 353 SOUNDS_3 0 "NONE" 3 "SOFT CHIME" 5 "SINGLE CHIME";
VAL_ 353 SOUNDS_4 0 "NONE" 2 "DOUBLE CHIME";
VAL_ 353 SETSPEED_HUD 0 "HIDDEN" 1 "GRAY" 2 "GREEN" 3 "WHITE" 5 "CYAN";
VAL_ 353 DISTANCE_LEAD 0 "HIDDEN" 1 "GRAY" 2 "WHITE";
VAL_ 353 DISTANCE_CAR 0 "HIDDEN" 1 "GRAY" 2 "WHITE" 3 "CYAN A";
VAL_ 353 DISTANCE_SPACING 0 "HIDDEN" 1 "BLUE" 3 "WHITE" 5 "CYAN";
VAL_ 353 SETSPEED 0 "HIDDEN" 1 "GRAY" 2 "GREEN" 3 "WHITE" 6 "CYAN";
VAL_ 353 HDA_ICON 0 "HIDDEN" 1 "GRAY" 2 "GREEN" 3 "WHITE" 5 "CYAN HDP";
VAL_ 353 SLA_ICON 0 "HIDDEN" 1 "WHITE UP" 2 "WHITE DOWN" 3 "GREEN UP" 4 "GREEN DOWN";
VAL_ 353 NAV_ICON 0 "HIDDEN" 1 "GRAY" 2 "GREEN" 4 "WHITE";
VAL_ 353 LFA_ICON 0 "HIDDEN" 1 "GRAY" 2 "GREEN" 3 "WHITE" 5 "CYAN";
VAL_ 353 LCA_LEFT_ICON 0 "HIDDEN" 1 "GRAY" 2 "GREEN" 4 "WHITE";
VAL_ 353 LCA_RIGHT_ICON 0 "HIDDEN" 1 "GRAY" 2 "GREEN" 4 "WHITE";
VAL_ 353 BACKGROUND 0 "HIDDEN" 1 "BLUE" 3 "ORANGE" 4 "FLASHING ORANGE" 6 "FLASHING RED" 7 "GRAY";
VAL_ 353 DAW_ICON 0 "HIDDEN" 1 "ORANGE";
VAL_ 353 CAR_CIRCLE 0 "HIDDEN" 1 "GRAY" 2 "CYAN";
VAL_ 354 COUNTRY 0 "HIDDEN" 1 "SOUTH_KOREA" 4 "INTL" 5 "JAPAN" 6 "CANADA" 7 "USA" 8 "CHINA" 9 "INTL";
VAL_ 354 SPEEDLIMIT_FLASH 0 "HIDDEN" 1 "ERROR" 2 "NORMAL" 4 "RED";
VAL_ 354 SIGNS 0 "HIDDEN" 1 "PEDESTRIAN_CROSSING" 2 "SCHOOL_CROSSWALK" 8 "STOP" 9 "YIELD" 16 "DO_NOT_PASS" 19 "DO_NOT_ENTER" 24 "ROUNDABOUT" 26 "RIGHT_CURVE_AHEAD" 27 "LEFT_CURVE_AHEAD" 28 "SLIGHT_RIGHT_CURVE_AHEAD" 29 "SLIGHT_LEFT_CURVE_AHEAD";
VAL_ 354 SPEEDLIMIT_WEATHER 0 "HIDDEN" 1 "RAIN" 2 "SNOW" 3 "RAIN+SNOW" 4 "TRAILER";
VAL_ 354 VIBRATE 0 "NONE" 1 "VIBRATE";
VAL_ 354 LEAD 0 "HIDDEN" 1 "GRAY BOX" 2 "WHITE BOX" 3 "GRAY CAR" 4 "WHITE CAR" 5 "GRAY TRUCK" 6 "WHITE TRUCK" 7 "GRAY PERSON" 8 "WHITE PERSON" 9 "GRAY BICYCLE" 10 "WHITE BICYCLE" 11 "GRAY MOTORCYCLE" 12 "WHITE MOTORCYCLE" 13 "DARK CONE" 14 "ORANGE CONE";
VAL_ 354 LEAD_ALT 0 "HIDDEN" 1 "GRAY BOX" 2 "WHITE BOX" 3 "DIM CONE" 4 "ORANGE CONE";
VAL_ 354 LEAD_LEFT 0 "HIDDEN" 1 "GRAY BOX" 2 "WHITE BOX" 3 "GRAY CAR" 4 "WHITE CAR" 5 "GRAY TRUCK" 6 "WHITE TRUCK" 7 "GRAY PERSON" 8 "WHITE PERSON" 9 "GRAY BICYCLE" 10 "WHITE BICYCLE" 11 "GRAY MOTORCYCLE" 12 "WHITE MOTORCYCLE" 13 "DARK CONE" 14 "ORANGE CONE";
VAL_ 354 LEAD_RIGHT 0 "HIDDEN" 1 "GRAY BOX" 2 "WHITE BOX" 3 "GRAY CAR" 4 "WHITE CAR" 5 "GRAY TRUCK" 6 "WHITE TRUCK" 7 "GRAY PERSON" 8 "WHITE PERSON" 9 "GRAY BICYCLE" 10 "WHITE BICYCLE" 11 "GRAY MOTORCYCLE" 12 "WHITE MOTORCYCLE" 13 "DARK CONE" 14 "ORANGE CONE";
VAL_ 354 FAULT_FSS 0 "HIDDEN" 1 "CHECK_FORWARD_SAFETY_SYSTEM" 2 "FORWARD_SAFETY_SYSTEM_LIMITED_CAMERA_OBSCURED" 3 "FORWARD_SAFETY_SYSTEM_LIMITED_RADAR_BLOCKED";
VAL_ 354 FAULT_FCA 0 "HIDDEN" 1 "CHECK_FORWARD_SIDE_SAFETY_SYSTEM" 2 "FORWARD_SIDE_SAFETY_SYSTEM_LIMITED_CAMERA_OBSCURED" 3 "FORWARD_SIDE_SAFETY_SYSTEM_LIMITED_RADAR_BLOCKED";
VAL_ 354 FAULT_LSS 0 "HIDDEN" 1 "CHECK_LANE_SAFETY_SYSTEM" 2 "LANE_SAFETY_SYSTEM_DISABLED_CAMERA_OBSCURED";
VAL_ 354 FAULT_SLA 0 "HIDDEN" 1 "CHECK_SPEED_LIMIT_SYSTEM" 2 "SPEED_LIMIT_SYSTEM_DISABLED_CAMERA_OBSCURED";
VAL_ 354 FAULT_DAW 0 "HIDDEN" 1 "CHECK_INATTENTIVE_DRIVING_WARNING_SYSTEM" 2 "INATTENTIVE_DRIVING_WARNING_SYSTEM_DISABLED_CAMERA_OBSCURED";
VAL_ 354 FAULT_HBA 0 "HIDDEN" 1 "CHECK_HIGH_BEAM_ASSIST_SYSTEM";
VAL_ 354 FAULT_SCC 0 "HIDDEN" 1 "CHECK_SMART_CRUISE_CONTROL_SYSTEM" 2 "SMART_CRUISE_CONTROL_DISABLED_RADAR_BLOCKED";
VAL_ 354 FAULT_LFA 0 "HIDDEN" 1 "CHECK_LANE_FOLLOWING_SYSTEM_ASSIST_SYSTEM";
VAL_ 354 FAULT_HDA 0 "HIDDEN" 1 "CHECK_HIGHWAY_DRIVING_ASSIST_SYSTEM";
VAL_ 354 FAULT_LCA 0 "HIDDEN" 1 "CHECK_LANE_CHANGE_ASSIST_FUNCTION" 2 "LANE_CHANGE_ASSIST_FUNCTION_DISABLED_CAMERA_OBSCURED" 3 "LANE_CHANGE_ASSIST_FUNCTION_DISABLED_RADAR_BLOCKED";
VAL_ 354 FAULT_HDP 0 "HIDDEN" 1 "CHECK_HIGHWAY_DRIVING_PILOT_SYSTEM" 2 "HIGHWAY_DRIVING_PILOT_DISABLED_CAMERA_OBSCURED" 3 "HIGHWAY_DRIVING_PILOT_DISABLED_RADAR_BLOCKED" 4 "HIGHWAY_DRIVING_PILOT_DISABLED_LIDAR_BLOCKED";
VAL_ 354 FAULT_DAS 0 "HIDDEN" 1 "CHECK_DRIVER_ASSISTANCE_SYSTEM" 2 "DRIVER_ASSISTANCE_SYSTEM_LIMITED_CAMERA_OBSCURED" 3 "DRIVER_ASSISTANCE_SYSTEM_LIMITED_RADAR_BLOCKED" 4 "DRIVER_ASSISTANCE_SYSTEM_LIMITED_CAMERA_OBSCURED_AND_RADAR_BLOCKED";
VAL_ 354 FAULT_ESS 0 "HIDDEN" 1 "CHECK_EMERGENCY_STOPPING_FUNCTION" 2 "EMERGENCY_STOPPING_FUNCTION_DISABLED_CAMERA_OBSCURED" 3 "EMERGENCY_STOPPING_FUNCTION_DISABLED_RADAR_BLOCKED";
VAL_ 362 BLINKER_CONTROL 1 "hazards" 2 "hazards button backlight" 3 "left blinkers" 4 "right blinkers";
VAL_ 373 ACCEnable 0 "SCC ready" 1 "SCC temp fault" 2 "SCC permanent fault" 3 "SCC permanent fault, communication issue";
VAL_ 416 ACCMode 0 "off" 1 "enabled" 2 "driver_override" 3 "off_maybe_fault" 4 "cancelled";
VAL_ 426 CRUISE_BUTTONS 0 "none" 1 "res_accel" 2 "set_decel" 3 "gap_distance" 4 "pause_resume";
VAL_ 437 Info_LftLnQualSta 0 "Very Low Quality" 1 "Low Quality" 2 "High Quality" 3 "Very High Quality" 4 "Reserved" 5 "Reserved" 6 "Reserved" 7 "Error indicator";
VAL_ 437 Info_LftLnDptSta 0 "No Left Line Departure" 1 "Left Line Departure" 2 "Reserved" 3 "Error Indicator";
VAL_ 437 Info_RtLnQualSta 0 "Very Low Quality" 1 "Low Quality" 2 "High Quality" 3 "Very High Quality" 4 "Reserved" 5 "Reserved" 6 "Reserved" 7 "Error indicator";
VAL_ 437 Info_RtLnDptSta 0 "No Right Line Departure" 1 "Right Line Departure" 2 "Reserved" 3 "Error Indicator";
VAL_ 437 Info_RtLnCvtrVal 65534 "Reserved" 65535 "Error indicator";
VAL_ 463 CRUISE_BUTTONS 0 "none" 1 "res_accel" 2 "set_decel" 3 "gap_distance" 4 "pause_resume";
VAL_ 463 RIGHT_PADDLE 0 "Not Pulled" 1 "Pulled";
VAL_ 463 LEFT_PADDLE 0 "Not Pulled" 1 "Pulled";
VAL_ 506 ISLW_OptUsmSta 0 "None ISLW Option (Default)" 1 "System Disabled by USM" 2 "System Enable by USM" 3 "Invalid";
VAL_ 506 ISLW_SysSta 0 "Normal (Default)" 1 "System Fail" 2 "ISLW Temporary Unavailable" 3 "Reserved";
VAL_ 506 ISLW_NoPassingInfoDis 0 "None Display (Default)" 1 "LHD No Passing Zone Display" 2 "RHD No Passing Zone Display" 3 "Reserved" 4 "Reserved" 5 "Reserved" 6 "Reserved" 7 "Invalid";
VAL_ 506 ISLW_OvrlpSignDis 0 "None (Default)" 1 "Overlap Sign" 2 "Reserved" 3 "Error indicator";
VAL_ 506 ISLW_SpdCluMainDis 0 "No Recognition (Default)" 253 "Unlimited Speed" 254 "Reserved" 255 "Invalid";
VAL_ 506 ISLW_SpdNaviMainDis 0 "No Recognition (Default)" 253 "Unlimited Speed" 254 "Reserved" 255 "Invalid";
VAL_ 506 ISLW_SubCondinfoSta1 0 "None (Default)" 1 "Rain" 2 "Snow" 3 "Snow&Rain" 4 "Trailer" 5 "Reserved" 6 "Reserved" 7 "Reserved" 8 "Reserved" 9 "Reserved" 10 "Reserved" 11 "Reserved" 12 "Reserved" 13 "Reserved" 14 "Generic" 15 "Invalid";
VAL_ 506 ISLW_SubCondinfoSta2 0 "None (Default)" 1 "Rain" 2 "Snow" 3 "Snow&Rain" 4 "Trailer" 5 "Reserved" 6 "Reserved" 7 "Reserved" 8 "Reserved" 9 "Reserved" 10 "Reserved" 11 "Reserved" 12 "Reserved" 13 "Reserved" 14 "Generic" 15 "Invalid";
VAL_ 506 ISLW_SpdCluSubMainDis 0 "No Recognition (Default)" 253 "Unlimited Speed" 254 "Reserved" 255 "Invalid";
VAL_ 506 ISLW_SpdCluDisSubCond1 0 "No Recognition (Default)" 253 "LHD Conditional No Passing ZONE" 254 "RHD Conditional No Passing ZONE" 255 "Invalid";
VAL_ 506 ISLW_SpdCluDisSubCond2 0 "No Recognition (Default)" 253 "LHD Conditional No Passing ZONE" 254 "RHD Conditional No Passing ZONE" 255 "Invalid";
VAL_ 506 ISLW_SpdNaviSubMainDis 0 "No Recognition (Default)" 253 "Unlimited Speed" 254 "Reserved" 255 "Invalid";
VAL_ 506 ISLW_SpdNaviDisSubCond1 0 "No Recognition (Default)" 253 "LHD Conditional No Passing ZONE" 254 "RHD Conditional No Passing ZONE" 255 "Invalid";
VAL_ 506 ISLW_SpdNaviDisSubCond2 0 "No Recognition (Default)" 253 "LHD Conditional No Passing ZONE" 254 "RHD Conditional No Passing ZONE" 255 "Invalid";
VAL_ 506 ISLA_SpdwOffst 0 "No Recognition" 253 "Unlimited Speed" 254 "Reserved" 255 "Invalid";
VAL_ 506 ISLA_SwIgnoreReq 0 "Allow All Switch Inputs (default)" 1 "-(SET) Switch Input Ignore" 2 "+(SET) Switch Input Ignore" 3 "-(SET) & +(SET) Switch Inputs Ignore";
VAL_ 506 ISLA_SpdChgReq 0 "Default" 1 "Speed Change Request" 2 "Reserved" 3 "Reserved";
VAL_ 506 ISLA_SpdWrn 0 "No Warning" 1 "Warning" 2 "Reserved" 3 "Reserved";
VAL_ 506 ISLA_IcyWrn 0 "No Warning" 1 "Warning" 2 "Reserved" 3 "Reserved";
VAL_ 506 ISLA_SymFlashMod 0 "No Flasing" 1 "Flashing Sign" 2 "Flashing - Arrow Symbol" 3 "Flashing + Arrow Symbol" 4 "Flashing Auto Symbol" 5 "Reserved" 6 "Reserved" 7 "Reserved";
VAL_ 506 ISLA_Popup 0 "No Popup" 1 "MSLA Speed will Change" 2 "MSLA Speed has Changed" 3 "CC_SCC Speed will Change" 4 "CC_SCC Speed has Changed" 5 "Reserved" 6 "Reserved" 7 "Reserved";
VAL_ 506 ISLA_OptUsmSta 0 "None ISLA Option (속도 제한 메뉴 삭제)" 1 "Off" 2 "Warning" 3 "Assist" 4 "Reserved" 5 "Reserved" 6 "Reserved" 7 "Invalid (GRAY)";
VAL_ 506 ISLA_OffstUsmSta 0 "None Offset Function" 1 "-10kph or -5mph" 2 "-5kph or -3mph" 3 "0kph or 0mph" 4 "+5kph or +3mph" 5 "+10kph or +5mph" 6 "Reserved" 7 "Invalid (GRAY)";
VAL_ 506 ISLA_AutoUsmSta 0 "None Auto Function (Delete Menu)" 1 "Auto Off" 2 "Auto On" 3 "Invalid (GRAY)";
VAL_ 506 ISLA_Cntry 0 "Europe/Russia/Australia" 1 "Domestic" 2 "China" 3 "USA" 4 "Canada" 5 "Australia" 6 "Reserved" 7 "Reserved" 8 "Reserved" 9 "Reserved" 10 "Reserved" 11 "Reserved" 12 "Reserved" 13 "Reserved" 14 "Reserved" 15 "Initial Value (default)";
VAL_ 506 ISLA_AddtnlSign 0 "No Recognition (default)" 1 "School Crossing" 16 "Do Not Pass" 17 "Reserved" 18 "Reserved" 19 "Reserved" 20 "Reserved" 21 "Reserved" 22 "Reserved" 23 "Reserved" 24 "Exit" 25 "Roundabout" 26 "Right Curve" 27 "Left Curve" 28 "Winding Road" 29 "Reserved" 30 "Reserved" 31 "Reserved" 2 "Pedestrian Crossing" 3 "Bicycle Crossing" 4 "Reserved" 5 "Reserved" 6 "Reserved" 7 "Reserved" 8 "Stop" 9 "Yield" 10 "Stop Ahead" 11 "Yield Ahead" 12 "Road Construction Ahead" 13 "Lane Reduction" 14 "Reserved" 15 "Reserved";
VAL_ 506 ISLA_SchoolZone 0 "No School Zone" 1 "School Zone" 2 "Reserved" 3 "Reserved";
VAL_ 698 IFSref_FR_CMR_Sta 0 "None Option (Default)" 1 "Normal" 2 "Blockage Status" 3 "Error Indicator";
VAL_ 698 IFSref_VehNumVal 0 "No vehicle" 1 "Number of vehicles" 2 "Number of vehicles" 3 "Number of vehicles" 4 "Number of vehicles" 5 "Number of vehicles" 6 "Number of vehicles" 7 "Number of vehicles" 8 "Number of vehicles" 9 "Number of vehicles" 10 "Number of vehicles" 11 "Over than 10 vehicles" 12 "Reserved" 13 "Reserved" 14 "Default" 15 "Error indicator";
VAL_ 698 IFSref_ILLAmbtSta 0 "Bright" 1 "Dark" 2 "Not used" 3 "Error indicator";
VAL_ 698 IFSref_VehLftAngl1Val 501 "Not used" 502 "Not used" 503 "Not used" 504 "Not used" 505 "Not used" 506 "Not used" 507 "Not used" 508 "Not used" 509 "Not used" 510 "Default" 511 "Error indicator";
VAL_ 698 IFSref_VehRtAngl1Val 501 "Not used" 502 "Not used" 503 "Not used" 504 "Not used" 505 "Not used" 506 "Not used" 507 "Not used" 508 "Not used" 509 "Not used" 510 "Default" 511 "Error indicator";
VAL_ 698 IFSref_VehLftAngl2Val 501 "Not used" 502 "Not used" 503 "Not used" 504 "Not used" 505 "Not used" 506 "Not used" 507 "Not used" 508 "Not used" 509 "Not used" 510 "Default" 511 "Error indicator";
VAL_ 698 IFSref_VehDst1Val 0 "No Value" 1 "1~10m" 2 "11~20m" 3 "21~30m" 4 "31~40m" 5 "41~50m" 6 "51~60m" 7 "61~70m" 8 "71~80m" 9 "81~90m" 10 "91~100m" 11 "101~200m" 12 "201~300m" 13 "301~400m" 14 "401m~" 15 "Error indicator";
VAL_ 698 IFSref_VehRtAngl2Val 501 "Not used" 502 "Not used" 503 "Not used" 504 "Not used" 505 "Not used" 506 "Not used" 507 "Not used" 508 "Not used" 509 "Not used" 510 "Default" 511 "Error indicator";
VAL_ 698 IFSref_VehLftAngl3Val 501 "Not used" 502 "Not used" 503 "Not used" 504 "Not used" 505 "Not used" 506 "Not used" 507 "Not used" 508 "Not used" 509 "Not used" 510 "Default" 511 "Error indicator";
VAL_ 698 IFSref_VehRtAngl3Val 501 "Not used" 502 "Not used" 503 "Not used" 504 "Not used" 505 "Not used" 506 "Not used" 507 "Not used" 508 "Not used" 509 "Not used" 510 "Default" 511 "Error indicator";
VAL_ 698 IFSref_VehLftAngl4Val 501 "Not used" 502 "Not used" 503 "Not used" 504 "Not used" 505 "Not used" 506 "Not used" 507 "Not used" 508 "Not used" 509 "Not used" 510 "Default" 511 "Error indicator";
VAL_ 698 IFSref_VehRtAngl4Val 501 "Not used" 502 "Not used" 503 "Not used" 504 "Not used" 505 "Not used" 506 "Not used" 507 "Not used" 508 "Not used" 509 "Not used" 510 "Default" 511 "Error indicator";
VAL_ 698 IFSref_VehLftAngl5Val 501 "Not used" 502 "Not used" 503 "Not used" 504 "Not used" 505 "Not used" 506 "Not used" 507 "Not used" 508 "Not used" 509 "Not used" 510 "Default" 511 "Error indicator";
VAL_ 698 IFSref_VehRtAngl5Val 501 "Not used" 502 "Not used" 503 "Not used" 504 "Not used" 505 "Not used" 506 "Not used" 507 "Not used" 508 "Not used" 509 "Not used" 510 "Default" 511 "Error indicator";
VAL_ 698 IFSref_VehLftAngl6Val 501 "Not used" 502 "Not used" 503 "Not used" 504 "Not used" 505 "Not used" 506 "Not used" 507 "Not used" 508 "Not used" 509 "Not used" 510 "Default" 511 "Error indicator";
VAL_ 698 IFSref_VehRtAngl6Val 501 "Not used" 502 "Not used" 503 "Not used" 504 "Not used" 505 "Not used" 506 "Not used" 507 "Not used" 508 "Not used" 509 "Not used" 510 "Default" 511 "Error indicator";
VAL_ 698 IFSref_VehLftAngl7Val 501 "Not used" 502 "Not used" 503 "Not used" 504 "Not used" 505 "Not used" 506 "Not used" 507 "Not used" 508 "Not used" 509 "Not used" 510 "Default" 511 "Error indicator";
VAL_ 698 IFSref_VehRtAngl7Val 501 "Not used" 502 "Not used" 503 "Not used" 504 "Not used" 505 "Not used" 506 "Not used" 507 "Not used" 508 "Not used" 509 "Not used" 510 "Default" 511 "Error indicator";
VAL_ 698 IFSref_VehLftAngl8Val 501 "Not used" 502 "Not used" 503 "Not used" 504 "Not used" 505 "Not used" 506 "Not used" 507 "Not used" 508 "Not used" 509 "Not used" 510 "Default" 511 "Error indicator";
VAL_ 698 IFSref_VehRtAngl8Val 501 "Not used" 502 "Not used" 503 "Not used" 504 "Not used" 505 "Not used" 506 "Not used" 507 "Not used" 508 "Not used" 509 "Not used" 510 "Default" 511 "Error indicator";
VAL_ 698 IFSref_VehLftAngl9Val 501 "Not used" 502 "Not used" 503 "Not used" 504 "Not used" 505 "Not used" 506 "Not used" 507 "Not used" 508 "Not used" 509 "Not used" 510 "Default" 511 "Error indicator";
VAL_ 698 IFSref_VehRtAngl9Val 501 "Not used" 502 "Not used" 503 "Not used" 504 "Not used" 505 "Not used" 506 "Not used" 507 "Not used" 508 "Not used" 509 "Not used" 510 "Default" 511 "Error indicator";
VAL_ 698 IFSref_VehLftAngl10Val 501 "Not used" 502 "Not used" 503 "Not used" 504 "Not used" 505 "Not used" 506 "Not used" 507 "Not used" 508 "Not used" 509 "Not used" 510 "Default" 511 "Error indicator";
VAL_ 698 IFSref_VehRtAngl10Val 501 "Not used" 502 "Not used" 503 "Not used" 504 "Not used" 505 "Not used" 506 "Not used" 507 "Not used" 508 "Not used" 509 "Not used" 510 "Default" 511 "Error indicator";
VAL_ 698 IFSref_VehDst2Val 0 "No Value" 1 "1~10m" 2 "11~20m" 3 "21~30m" 4 "31~40m" 5 "41~50m" 6 "51~60m" 7 "61~70m" 8 "71~80m" 9 "81~90m" 10 "91~100m" 11 "101~200m" 12 "201~300m" 13 "301~400m" 14 "401m~" 15 "Error indicator";
VAL_ 698 IFSref_VehDst3Val 0 "No Value" 1 "1~10m" 2 "11~20m" 3 "21~30m" 4 "31~40m" 5 "41~50m" 6 "51~60m" 7 "61~70m" 8 "71~80m" 9 "81~90m" 10 "91~100m" 11 "101~200m" 12 "201~300m" 13 "301~400m" 14 "401m~" 15 "Error indicator";
VAL_ 698 IFSref_VehDst4Val 0 "No Value" 1 "1~10m" 2 "11~20m" 3 "21~30m" 4 "31~40m" 5 "41~50m" 6 "51~60m" 7 "61~70m" 8 "71~80m" 9 "81~90m" 10 "91~100m" 11 "101~200m" 12 "201~300m" 13 "301~400m" 14 "401m~" 15 "Error indicator";
VAL_ 698 IFSref_VehDst5Val 0 "No Value" 1 "1~10m" 2 "11~20m" 3 "21~30m" 4 "31~40m" 5 "41~50m" 6 "51~60m" 7 "61~70m" 8 "71~80m" 9 "81~90m" 10 "91~100m" 11 "101~200m" 12 "201~300m" 13 "301~400m" 14 "401m~" 15 "Error indicator";
VAL_ 698 IFSref_VehDst6Val 0 "No Value" 1 "1~10m" 2 "11~20m" 3 "21~30m" 4 "31~40m" 5 "41~50m" 6 "51~60m" 7 "61~70m" 8 "71~80m" 9 "81~90m" 10 "91~100m" 11 "101~200m" 12 "201~300m" 13 "301~400m" 14 "401m~" 15 "Error indicator";
VAL_ 698 IFSref_VehDst7Val 0 "No Value" 1 "1~10m" 2 "11~20m" 3 "21~30m" 4 "31~40m" 5 "41~50m" 6 "51~60m" 7 "61~70m" 8 "71~80m" 9 "81~90m" 10 "91~100m" 11 "101~200m" 12 "201~300m" 13 "301~400m" 14 "401m~" 15 "Error indicator";
VAL_ 698 IFSref_VehDst8Val 0 "No Value" 1 "1~10m" 2 "11~20m" 3 "21~30m" 4 "31~40m" 5 "41~50m" 6 "51~60m" 7 "61~70m" 8 "71~80m" 9 "81~90m" 10 "91~100m" 11 "101~200m" 12 "201~300m" 13 "301~400m" 14 "401m~" 15 "Error indicator";
VAL_ 698 IFSref_VehDst9Val 0 "No Value" 1 "1~10m" 2 "11~20m" 3 "21~30m" 4 "31~40m" 5 "41~50m" 6 "51~60m" 7 "61~70m" 8 "71~80m" 9 "81~90m" 10 "91~100m" 11 "101~200m" 12 "201~300m" 13 "301~400m" 14 "401m~" 15 "Error indicator";
VAL_ 698 IFSref_VehDst10Val 0 "No Value" 1 "1~10m" 2 "11~20m" 3 "21~30m" 4 "31~40m" 5 "41~50m" 6 "51~60m" 7 "61~70m" 8 "71~80m" 9 "81~90m" 10 "91~100m" 11 "101~200m" 12 "201~300m" 13 "301~400m" 14 "401m~" 15 "Error indicator";
VAL_ 736 MSLA_STATUS 0 "disabled" 1 "active" 2 "paused";
VAL_ 866 LEFT_LANE_LINE 0 "Not Detected" 1 "Low Confidence" 2 "Medium Confidence" 3 "High Confidence";
VAL_ 866 RIGHT_LANE_LINE 0 "Not Detected" 1 "Low Confidence" 2 "Medium Confidence" 3 "High Confidence";
VAL_ 1041 DRIVER_DOOR 0 "Closed" 1 "Opened";
VAL_ 1041 PASSENGER_DOOR 0 "Closed" 1 "Opened";
VAL_ 1041 PASSENGER_SEATBELT 0 "Unlatched" 1 "Latched";
VAL_ 1041 DRIVER_SEATBELT 0 "Unlatched" 1 "Latched";
VAL_ 1041 DRIVER_REAR_DOOR 0 "Closed" 1 "Opened";
VAL_ 1041 PASSENGER_REAR_DOOR 0 "Closed" 1 "Opened";
VAL_ 1144 DRIVE_MODE2 3 "Set Sport" 1 "Set Normal" 2 "Set Eco";
VAL_ 1240 DISTANCE_UNIT 1 "Miles" 0 "Kilometers";
