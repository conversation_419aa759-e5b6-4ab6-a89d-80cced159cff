BO_ 304 GAS_PEDAL_2: 8 PCM
 SG_ ENGINE_TORQUE_ESTIMATE : 7|16@0- (1,0) [-1000|1000] "Nm" EON
 SG_ ENGINE_TORQUE_REQUEST : 23|16@0- (1,0) [-1000|1000] "Nm" EON
 SG_ CAR_GAS : 39|8@0+ (1,0) [0|255] "" EON
 SG_ COUNTER : 61|2@0+ (1,0) [0|3] "" EON
 SG_ CHECKSUM : 59|4@0+ (1,0) [0|15] "" EON

BO_ 316 GAS_PEDAL: 8 PCM
 SG_ CAR_GAS : 39|8@0+ (1,0) [0|255] "" EON
 SG_ COUNTER : 61|2@0+ (1,0) [0|3] "" EON
 SG_ CHECKSUM : 59|4@0+ (1,0) [0|15] "" EON

BO_ 344 ENGINE_DATA: 8 PCM
 SG_ XMISSION_SPEED : 7|16@0+ (0.01,0) [0|250] "kph" EON
 SG_ ENGINE_RPM : 23|16@0+ (1,0) [0|15000] "rpm" EON
 SG_ XMISSION_SPEED2 : 39|16@0+ (0.01,0) [0|250] "kph" EON
 SG_ ODOMETER : 55|8@0+ (10,0) [0|2550] "m" XXX
 SG_ COUNTER : 61|2@0+ (1,0) [0|3] "" EON
 SG_ CHECKSUM : 59|4@0+ (1,0) [0|15] "" EON

BO_ 380 POWERTRAIN_DATA: 8 PCM
 SG_ PEDAL_GAS : 7|8@0+ (1,0) [0|255] "" EON
 SG_ ENGINE_RPM : 23|16@0+ (1,0) [0|15000] "rpm" EON
 SG_ GAS_PRESSED : 39|1@0+ (1,0) [0|1] "" EON
 SG_ ACC_STATUS : 38|1@0+ (1,0) [0|1] "" EON
 SG_ BOH_17C : 37|5@0+ (1,0) [0|1] "" EON
 SG_ BRAKE_SWITCH : 32|1@0+ (1,0) [0|1] "" EON
 SG_ BOH2_17C : 47|10@0+ (1,0) [0|1] "" EON
 SG_ BRAKE_PRESSED : 53|1@0+ (1,0) [0|1] "" EON
 SG_ BOH3_17C : 52|5@0+ (1,0) [0|1] "" EON
 SG_ COUNTER : 61|2@0+ (1,0) [0|3] "" EON
 SG_ CHECKSUM : 59|4@0+ (1,0) [0|15] "" EON

BO_ 420 VSA_STATUS: 8 VSA
 SG_ USER_BRAKE : 7|16@0+ (0.015625,-1.609375) [0|1000] "" EON
 SG_ COMPUTER_BRAKING : 23|1@0+ (1,0) [0|1] "" EON
 SG_ ESP_DISABLED : 28|1@0+ (1,0) [0|1] "" EON
 SG_ BRAKE_HOLD_RELATED : 52|1@0+ (1,0) [0|1] "" XXX
 SG_ BRAKE_HOLD_ACTIVE : 46|1@0+ (1,0) [0|1] "" EON
 SG_ BRAKE_HOLD_ENABLED : 45|1@0+ (1,0) [0|1] "" EON
 SG_ COUNTER : 61|2@0+ (1,0) [0|3] "" EON
 SG_ CHECKSUM : 59|4@0+ (1,0) [0|15] "" EON

BO_ 427 STEER_MOTOR_TORQUE: 3 EPS
 SG_ CONFIG_VALID : 7|1@0+ (1,0) [0|1] "" EON
 SG_ MOTOR_TORQUE : 1|10@0+ (1,0) [0|256] "" EON
 SG_ OUTPUT_DISABLED : 22|1@0+ (1,0) [0|1] "" EON
 SG_ COUNTER : 21|2@0+ (1,0) [0|3] "" EON
 SG_ CHECKSUM : 19|4@0+ (1,0) [0|15] "" EON

BO_ 464 WHEEL_SPEEDS: 8 VSA
 SG_ WHEEL_SPEED_FL : 7|15@0+ (0.01,0) [0|250] "kph" EON
 SG_ WHEEL_SPEED_FR : 8|15@0+ (0.01,0) [0|250] "kph" EON
 SG_ WHEEL_SPEED_RL : 25|15@0+ (0.01,0) [0|250] "kph" EON
 SG_ WHEEL_SPEED_RR : 42|15@0+ (0.01,0) [0|250] "kph" EON
 SG_ CHECKSUM : 59|4@0+ (1,0) [0|3] "" EON

BO_ 490 VEHICLE_DYNAMICS: 8 VSA
 SG_ LAT_ACCEL : 7|16@0- (0.0015,0) [-20|20] "m/s2" EON
 SG_ LONG_ACCEL : 23|16@0- (0.0015,0) [-20|20] "m/s2" EON
 SG_ COUNTER : 61|2@0+ (1,0) [0|3] "" EON
 SG_ CHECKSUM : 59|4@0+ (1,0) [0|3] "" EON

BO_ 597 ROUGH_WHEEL_SPEED: 8 VSA
 SG_ WHEEL_SPEED_FL : 7|8@0+ (1,0) [0|255] "kph" EON
 SG_ WHEEL_SPEED_FR : 15|8@0+ (1,0) [0|255] "kph" EON
 SG_ WHEEL_SPEED_RL : 23|8@0+ (1,0) [0|255] "kph" EON
 SG_ WHEEL_SPEED_RR : 31|8@0+ (1,0) [0|255] "kph" EON
 SG_ SET_TO_X55 : 39|8@0+ (1,0) [0|255] "" EON
 SG_ SET_TO_X55_2 : 47|8@0+ (1,0) [0|255] "" EON
 SG_ LONG_COUNTER : 55|8@0+ (1,0) [0|255] "" EON
 SG_ CHECKSUM : 59|4@0+ (1,0) [0|15] "" EON
 SG_ COUNTER : 61|2@0+ (1,0) [0|3] "" EON

BO_ 773 SEATBELT_STATUS: 7 BDY
 SG_ SEATBELT_DRIVER_LAMP : 7|1@0+ (1,0) [0|1] "" EON
 SG_ SEATBELT_PASS_UNLATCHED : 10|1@0+ (1,0) [0|1] "" EON
 SG_ SEATBELT_PASS_LATCHED : 11|1@0+ (1,0) [0|1] "" EON
 SG_ SEATBELT_DRIVER_UNLATCHED : 12|1@0+ (1,0) [0|1] "" EON
 SG_ SEATBELT_DRIVER_LATCHED : 13|1@0+ (1,0) [0|1] "" EON
 SG_ PASS_AIRBAG_OFF : 14|1@0+ (1,0) [0|1] "" EON
 SG_ PASS_AIRBAG_ON : 15|1@0+ (1,0) [0|1] "" EON
 SG_ COUNTER : 53|2@0+ (1,0) [0|3] "" EON
 SG_ CHECKSUM : 51|4@0+ (1,0) [0|3] "" EON

BO_ 777 CAR_SPEED: 8 PCM
 SG_ ROUGH_CAR_SPEED : 23|8@0+ (1,0) [0|255] "mph" XXX
 SG_ CAR_SPEED : 7|16@0+ (0.01,0) [0|65535] "kph" XXX
 SG_ ROUGH_CAR_SPEED_3 : 39|16@0+ (0.01,0) [0|65535] "kph" XXX
 SG_ ROUGH_CAR_SPEED_2 : 31|8@0+ (1,0) [0|255] "mph" XXX
 SG_ LOCK_STATUS : 55|2@0+ (1,0) [0|255] "" XXX
 SG_ COUNTER : 61|2@0+ (1,0) [0|3] "" XXX
 SG_ CHECKSUM : 59|4@0+ (1,0) [0|15] "" XXX
 SG_ IMPERIAL_UNIT : 63|1@0+ (1,0) [0|1] "" XXX

BO_ 780 ACC_HUD: 8 ADAS
 SG_ PCM_SPEED : 7|16@0+ (0.01,0) [0|250] "kph" BDY
 SG_ PCM_GAS : 23|8@0+ (1,0) [0|127] "" BDY
 SG_ CRUISE_SPEED : 31|8@0+ (1,0) [0|255] "kph" BDY
 SG_ DTC_MODE : 39|1@0+ (1,0) [0|1] "" BDY
 SG_ BRAKE_SYSTEM_ICON : 38|1@0+ (1,0) [0|1] "" BDY
 SG_ ACC_PROBLEM : 37|1@0+ (1,0) [0|1] "" BDY
 SG_ FCM_OFF : 35|1@0+ (1,0) [0|1] "" BDY
 SG_ FCM_OFF_2 : 36|1@0+ (1,0) [0|1] "" BDY
 SG_ FCM_PROBLEM : 34|1@0+ (1,0) [0|1] "" BDY
 SG_ RADAR_OBSTRUCTED : 33|1@0+ (1,0) [0|1] "" BDY
 SG_ ENABLE_MINI_CAR : 32|1@0+ (1,0) [0|1] "" BDY
 SG_ HUD_DISTANCE : 47|2@0+ (1,0) [0|3] "" BDY
 SG_ HUD_LEAD : 45|2@0+ (1,0) [0|3] "" BDY
 SG_ BOH_3 : 43|1@0+ (1,0) [0|3] "" BDY
 SG_ BOH_4 : 42|1@0+ (1,0) [0|3] "" BDY
 SG_ BOH_5 : 41|1@0+ (1,0) [0|3] "" BDY
 SG_ CRUISE_CONTROL_LABEL : 40|1@0+ (1,0) [0|3] "" BDY
 SG_ SET_ME_X01_2 : 55|1@0+ (1,0) [0|1] "" BDY
 SG_ IMPERIAL_UNIT : 54|1@0+ (1,0) [0|1] "" BDY
 SG_ ACC_ON : 52|1@0+ (1,0) [0|1] "" BDY
 SG_ CHIME : 51|3@0+ (1,0) [0|1] "" BDY
 SG_ SET_ME_X01 : 48|1@0+ (1,0) [0|1] "" BDY
 SG_ ICONS : 63|2@0+ (1,0) [0|1] "" BDY
 SG_ COUNTER : 61|2@0+ (1,0) [0|3] "" BDY
 SG_ CHECKSUM : 59|4@0+ (1,0) [0|3] "" BDY

BO_ 804 CRUISE: 8 PCM
 SG_ HUD_SPEED_KPH : 7|8@0+ (1,0) [0|255] "kph" EON
 SG_ HUD_SPEED_MPH : 15|8@0+ (1,0) [0|255] "mph" EON
 SG_ TRIP_FUEL_CONSUMED : 23|16@0+ (1,0) [0|255] "" EON
 SG_ CRUISE_SPEED_PCM : 39|8@0+ (1,0) [0|255] "" EON
 SG_ BOH2 : 47|8@0- (1,0) [0|255] "" EON
 SG_ BOH3 : 55|8@0+ (1,0) [0|255] "" EON
 SG_ COUNTER : 61|2@0+ (1,0) [0|3] "" EON
 SG_ CHECKSUM : 59|4@0+ (1,0) [0|15] "" EON

BO_ 884 STALK_STATUS: 8 XXX
 SG_ DASHBOARD_ALERT : 39|8@0+ (1,0) [0|255] "" EON
 SG_ AUTO_HEADLIGHTS : 46|1@0+ (1,0) [0|1] "" EON
 SG_ HIGH_BEAM_HOLD : 47|1@0+ (1,0) [0|1] "" EON
 SG_ HIGH_BEAM_FLASH : 45|1@0+ (1,0) [0|1] "" EON
 SG_ HEADLIGHTS_ON : 54|1@0+ (1,0) [0|1] "" EON
 SG_ WIPER_SWITCH : 53|2@0+ (1,0) [0|3] "" XXX
 SG_ COUNTER : 61|2@0+ (1,0) [0|3] "" EON
 SG_ CHECKSUM : 59|4@0+ (1,0) [0|15] "" EON

BO_ 891 STALK_STATUS_2: 8 XXX
 SG_ WIPERS : 17|2@0+ (1,0) [0|3] "" EON
 SG_ LOW_BEAMS : 35|1@0+ (1,0) [0|1] "" XXX
 SG_ HIGH_BEAMS : 34|1@0+ (1,0) [0|1] "" XXX
 SG_ PARK_LIGHTS : 36|1@0+ (1,0) [0|1] "" XXX
 SG_ COUNTER : 61|2@0+ (1,0) [0|3] "" EON
 SG_ CHECKSUM : 59|4@0+ (1,0) [0|15] "" EON

BO_ 1029 DOORS_STATUS: 8 BDY
 SG_ DOOR_OPEN_FL : 37|1@0+ (1,0) [0|1] "" EON
 SG_ DOOR_OPEN_FR : 38|1@0+ (1,0) [0|1] "" EON
 SG_ DOOR_OPEN_RL : 39|1@0+ (1,0) [0|1] "" EON
 SG_ DOOR_OPEN_RR : 40|1@0+ (1,0) [0|1] "" EON
 SG_ TRUNK_OPEN : 41|1@0+ (1,0) [0|1] "" EON
 SG_ COUNTER : 61|2@0+ (1,0) [0|3] "" EON
 SG_ CHECKSUM : 59|4@0+ (1,0) [0|15] "" EON

BO_ 1302 ODOMETER: 8 XXX
 SG_ ODOMETER : 7|24@0+ (1,0) [0|16777215] "km" EON
 SG_ COUNTER : 61|2@0+ (1,0) [0|3] "" EON
 SG_ CHECKSUM : 59|4@0+ (1,0) [0|3] "" EON

CM_ SG_ 304 "Seems to be platform-agnostic";
CM_ SG_ 316 "Should exist on Nidec";
CM_ SG_ 420 BRAKE_HOLD_RELATED "On when Brake Hold engaged";
CM_ SG_ 490 LONG_ACCEL "wheel speed derivative, noisy and zero snapping";
CM_ SG_ 773 PASS_AIRBAG_ON "Might just be indicator light";
CM_ SG_ 773 PASS_AIRBAG_OFF "Might just be indicator light";
CM_ SG_ 780 CRUISE_SPEED "255 = no speed";
CM_ SG_ 780 PCM_SPEED "Used by Nidec";
CM_ SG_ 780 PCM_GAS "Used by Nidec";
CM_ SG_ 804 CRUISE_SPEED_PCM "255 = no speed";

VAL_ 780 CRUISE_SPEED 255 "no_speed" 252 "stopped";
VAL_ 780 HUD_LEAD 3 "acc_off" 2 "solid_car" 1 "dashed_car" 0 "no_car";
VAL_ 884 DASHBOARD_ALERT 0 "none" 51 "acc_problem" 55 "cmbs_problem" 75 "key_not_detected" 79 "fasten_seatbelt" 111 "lkas_problem" 131 "brake_system_problem" 132 "brake_hold_problem" 139 "tbd" 161 "door_open";
VAL_ 891 WIPERS 4 "High" 2 "Low" 0 "Off";
