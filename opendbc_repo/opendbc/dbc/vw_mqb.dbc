VERSION ""


NS_ :
    NS_DESC_
    CM_
    BA_DEF_
    BA_
    VAL_
    CAT_DEF_
    CAT_
    FILTER
    BA_DEF_DEF_
    EV_DATA_
    ENVVAR_DATA_
    SGTYPE_
    SGTYPE_VAL_
    BA_DEF_SGTYPE_
    BA_SGTYPE_
    SIG_TYPE_REF_
    VAL_TABLE_
    SIG_GROUP_
    SIG_VALTYPE_
    SIGTYPE_VALTYPE_
    BO_TX_BU_
    BA_DEF_REL_
    BA_REL_
    BA_DEF_DEF_REL_
    BU_SG_REL_
    BU_EV_REL_
    BU_BO_REL_
    SG_MUL_VAL_

BS_:

BU_: Airbag_MQB BAP_Tester_MQB BMS_MQB Datenlogger_MQB Gateway_MQB Getriebe_DQ_Hybrid_MQB Getriebe_DQ_MQB LEH_MQB Motor_Diesel_MQB Motor_Hybrid_MQB Motor_Otto_MQB SAK_MQB Waehlhebel_MQB Vector__XXX l c i XXX


BO_ 290 ACC_06: 8 XXX
 SG_ CHECKSUM : 0|8@1+ (1,0) [0|255] ""  XXX
 SG_ COUNTER : 8|4@1+ (1,0) [0|15] ""  XXX
 SG_ ACC_limitierte_Anfahrdyn : 12|1@1+ (1,0) [0|1] ""  XXX
 SG_ ACC_nachtr_Stopp_Anf : 13|1@1+ (1,0) [0|1] ""  XXX
 SG_ ACC_DynErhoehung : 14|1@1+ (1.0,0.0) [0.0|1] ""  XXX
 SG_ ACC_Freilaufstrategie_TSK : 15|1@1+ (1.0,0.0) [0.0|1] ""  XXX
 SG_ ACC_zul_Regelabw_unten : 16|6@1+ (0.024,0) [0|1.512] "Unit_MeterPerSeconSquar"  XXX
 SG_ ACC_StartStopp_Info : 22|2@1+ (1,0) [0|3] ""  XXX
 SG_ ACC_Sollbeschleunigung_02 : 24|11@1+ (0.005,-7.22) [-7.22|3.005] "Unit_MeterPerSeconSquar"  XXX
 SG_ ACC_zul_Regelabw_oben : 35|5@1+ (0.0625,0) [0|1.9375] "Unit_MeterPerSeconSquar"  XXX
 SG_ ACC_neg_Sollbeschl_Grad_02 : 40|8@1+ (0.05,0) [0|12.75] "Unit_MeterPerCubicSecon"  XXX
 SG_ ACC_pos_Sollbeschl_Grad_02 : 48|8@1+ (0.05,0) [0|12.75] "Unit_MeterPerCubicSecon"  XXX
 SG_ ACC_Anfahren : 56|1@1+ (1,0) [0|1] ""  XXX
 SG_ ACC_Anhalten : 57|1@1+ (1,0) [0|1] ""  XXX
 SG_ ACC_Typ : 58|2@1+ (1,0) [0|3] ""  XXX
 SG_ ACC_Status_ACC : 60|3@1+ (1,0) [0|7] ""  XXX
 SG_ ACC_Minimale_Bremsung : 63|1@1+ (1,0) [0|1] ""  XXX

BO_ 279 ACC_10: 8 XXX
 SG_ CHECKSUM : 0|8@1+ (1,0) [0|255] ""  XXX
 SG_ COUNTER : 8|4@1+ (1,0) [0|15] ""  XXX
 SG_ AWV1_Anf_Prefill : 16|1@1+ (1,0) [0|1] ""  XXX
 SG_ ANB_CM_Info : 17|1@1+ (1,0) [0|1] ""  XXX
 SG_ AWV2_Freigabe : 18|1@1+ (1,0) [0|1] ""  XXX
 SG_ AWV1_HBA_Param : 19|2@1+ (1,0) [0|3] ""  XXX
 SG_ AWV2_Ruckprofil : 21|3@1+ (1,0) [0|7] ""  XXX
 SG_ AWV2_Priowarnung : 24|1@1+ (1,0) [0|1] ""  XXX
 SG_ ANB_CM_Anforderung : 25|1@1+ (1,0) [0|1] ""  XXX
 SG_ ANB_Info_Teilbremsung : 26|1@1+ (1,0) [0|1] ""  XXX
 SG_ ANB_Notfallblinken : 27|1@1+ (1,0) [0|1] ""  XXX
 SG_ ANB_Teilbremsung_Freigabe : 28|1@1+ (1,0) [0|1] ""  XXX
 SG_ ANB_Zielbrems_Teilbrems_Verz_Anf : 29|10@1+ (0.024,-20.016) [-20.016|4.536] "Unit_MeterPerSeconSquar"  XXX
 SG_ ANB_Zielbremsung_Freigabe : 39|1@1+ (1,0) [0|1] ""  XXX
 SG_ AWV_Vorstufe : 40|1@1+ (1,0) [0|1] ""  XXX
 SG_ AWV_Halten : 41|1@1+ (1,0) [0|1] ""  XXX
 SG_ AWV_CityANB_Auspraegung : 42|1@1+ (1,0) [0|1] ""  XXX
 SG_ PCF_Freigabe : 43|1@1+ (1,0) [0|1] ""  XXX
 SG_ AWV1_ECD_Anlauf : 44|1@1+ (1,0) [0|1] ""  XXX
 SG_ AWV_AWA_VZ_Anf_Lenkmomoffset : 46|1@1+ (1.0,0.0) [0.0|1] ""  XXX
 SG_ AWV_AWA_Anf_Lenkmomoffset : 47|9@1+ (0.01,0) [0.00|5.11] "Unit_NewtoMeter"  XXX
 SG_ PCF_Time_to_collision : 56|8@1+ (0.01,0) [0|2.5] "Unit_Secon"  XXX

BO_ 304 PLA_01: 8 XXX
 SG_ CHECKSUM : 0|8@1+ (1,0) [0|255] ""  XXX
 SG_ COUNTER : 8|4@1+ (1,0) [0|15] ""  XXX
 SG_ PLA_Status_PLA_ESP : 12|4@1+ (1.0,0.0) [0.0|15] ""  XXX
 SG_ PLA_LW_Soll : 16|13@1+ (0.1,0) [0.0|819.1] "Unit_DegreOfArc"  XXX
 SG_ PLA_VZ_LW_Soll : 31|1@1+ (1.0,0.0) [0.0|1] ""  XXX
 SG_ PLA_Status_PLA_EPS : 32|4@1+ (1.0,0.0) [0.0|15] ""  XXX
 SG_ PLA_Bremsmoment : 36|13@1+ (4,0) [0|32760] "Unit_NewtoMeter"  XXX
 SG_ PLA_Bremsverzoegerung : 36|7@1+ (0.1,0) [0.0|12.0] "Unit_MeterPerSeconSquar"  XXX
 SG_ PLA_Anf_Bremsverzoegerung : 43|1@1+ (1.0,0.0) [0.0|1] ""  XXX
 SG_ PLA_BremsMom_Verzoeg : 50|1@1+ (1.0,0.0) [0.0|1] ""  XXX
 SG_ PLA_Anhalten : 51|1@1+ (1.0,0.0) [0.0|1] ""  XXX
 SG_ PLA_Anhalteweg : 52|11@1+ (0.01,0) [0.01|20.45] "Unit_Meter"  XXX
 SG_ PLA_01_Signal_red_cyclic : 63|1@1+ (1.0,0.0) [0.0|1] ""  XXX

BO_ 679 ACC_13: 8 XXX
 SG_ ACC_Regelgeschw : 12|10@1+ (0.32,0) [0|327.04] "Unit_KiloMeterPerHour"  XXX
 SG_ ACC_Einheit_maxSetzgeschw : 22|1@1+ (1,0) [0|1] ""  XXX
 SG_ ACC_maxSetzgeschw : 23|9@1+ (1,0) [0|510] ""  XXX
 SG_ ACC_minRegelgeschw : 32|8@1+ (0.32,0) [0|81.28] "Unit_KiloMeterPerHour"  XXX
 SG_ ACC_maxRegelgeschw : 40|8@1+ (0.32,0) [0|81.28] "Unit_KiloMeterPerHour"  XXX
 SG_ ACC_Tempolimitassistent : 48|2@1+ (1,0) [0|3] ""  XXX
 SG_ ACC_Kurvenassistent : 52|3@1+ (1,0) [0|7] ""  XXX
 SG_ ACC_RUV : 56|2@1+ (1,0) [0|3] ""  XXX
 SG_ ACC_Tachokranz : 58|1@1+ (1,0) [0|1] ""  XXX
 SG_ ACC_Typ_Tachokranz_unten : 59|1@1+ (1,0) [0|1] ""  XXX
 SG_ ACC_ENG_Texte : 60|2@1+ (1,0) [0|3] ""  XXX
 SG_ ACC_ADAPTIVE : 63|2@0+ (1,0) [0|3] "" XXX

BO_ 681 ACC_15: 8 XXX
 SG_ AWV_Warnung : 16|3@1+ (1,0) [0|7] ""  XXX
 SG_ AWV_Texte : 24|3@1+ (1,0) [0|7] ""  XXX
 SG_ AWV_Status_Anzeige : 32|2@1+ (1,0) [0|3] ""  XXX
 SG_ AWV_Einstellung_System_FSG : 34|1@1+ (1,0) [0|1] ""  XXX
 SG_ AWV_Einstellung_Warnung_FSG : 36|3@1+ (1,0) [0|7] ""  XXX
 SG_ AWV_Warnlevel : 58|6@1+ (1,0) [0|63] ""  XXX

BO_ 64 Airbag_01: 8 Airbag_MQB
 SG_ CHECKSUM : 0|8@1+ (1,0) [0|255] "" BMS_MQB,Gateway_MQB,LEH_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ COUNTER : 8|4@1+ (1,0) [0|15] "" BMS_MQB,Gateway_MQB,LEH_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ AB_RGS_Anst : 12|4@1+ (1,0) [0|15] "" Gateway_MQB
 SG_ AB_Front_Crash : 16|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ AB_Heck_Crash : 17|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ AB_SF_Crash : 18|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ AB_SB_Crash : 19|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ AB_Rollover_Crash : 20|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ AB_Crash_Int : 21|3@1+ (1,0) [0|7] "" BMS_MQB,Gateway_MQB,LEH_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ AB_Lampe : 24|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ AB_Deaktiviert : 25|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ AB_VB_deaktiviert : 26|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ AB_Systemfehler : 27|1@1+ (1,0) [0|1] "" BMS_MQB,Gateway_MQB,LEH_MQB
 SG_ AB_Diagnose : 28|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ AB_Stellgliedtest : 29|1@1+ (1,0) [0|1] "" BMS_MQB,Gateway_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ AB_Erh_Auf_VB : 30|2@1+ (1,0) [0|3] "" Gateway_MQB
 SG_ AB_Gurtwarn_VF : 32|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ AB_Gurtwarn_VB : 33|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ AB_Anzeige_Fussg : 34|2@1+ (1,0) [0|3] "" Gateway_MQB
 SG_ AB_Texte_AKS : 36|2@1+ (1,0) [0|3] "" Gateway_MQB
 SG_ AB_PAO_Leuchte_Anf : 38|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ AB_MKB_gueltig : 39|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ AB_MKB_Anforderung : 40|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ AB_Versorgungsspannung : 41|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ AB_Deaktivierung_HV : 42|3@1+ (1.0,0.0) [0.0|7] ""  BMS,Gateway_MQB,LEH_MQB,Motor_Hybrid_MQB
 SG_ AB_EDR_Trigger : 45|2@1+ (1.0,0.0) [0.0|3] ""  Gateway_MQB
 SG_ AB_Gurtwarn_HFS : 47|1@1+ (1.0,0.0) [0.0|1] ""  Gateway_MQB
 SG_ AB_Gurtwarn_HBFS : 48|1@1+ (1.0,0.0) [0.0|1] ""  Gateway_MQB
 SG_ SC_Masterzeit_Offset : 53|2@1+ (5.08,0) [0.00|15.24] "Unit_Secon"  Gateway_MQB
 SG_ SC_Masterzeit : 57|7@1+ (0.04,0) [0.00|5.04] "Unit_Secon"  Gateway_MQB

BO_ 1312 Airbag_02: 8 Airbag_MQB
 SG_ AB_Belegung_VB : 26|2@1+ (1,0) [0|3] "" Gateway_MQB
 SG_ AB_Gurtschloss_FA : 40|2@1+ (1,0) [0|3] "" Gateway_MQB,Motor_Diesel_MQB,Motor_Otto_MQB
 SG_ AB_Gurtschloss_BF : 42|2@1+ (1,0) [0|3] "" Gateway_MQB
 SG_ AB_Gurtschloss_Reihe2_FA : 44|2@1+ (1,0) [0|3] "" Gateway_MQB
 SG_ AB_Gurtschloss_Reihe2_MI : 46|2@1+ (1,0) [0|3] "" Gateway_MQB
 SG_ AB_Gurtschloss_Reihe2_BF : 48|2@1+ (1,0) [0|3] "" Gateway_MQB
 SG_ AB_Gurtschloss_Reihe3_FA : 50|2@1+ (1,0) [0|3] "" Gateway_MQB
 SG_ AB_Gurtschloss_Reihe3_MI : 52|2@1+ (1,0) [0|3] "" Gateway_MQB
 SG_ AB_Gurtschloss_Reihe3_BF : 54|2@1+ (1,0) [0|3] "" Gateway_MQB
 SG_ AB_Sitzpos_Sens_FA : 56|2@1+ (1,0) [0|3] "" Gateway_MQB
 SG_ AB_Sitzpos_Sens_BF : 58|2@1+ (1,0) [0|3] "" Gateway_MQB

BO_ 65 Airbag_03: 8 Airbag_MQB
 SG_ Airbag_03_CRC : 0|8@1+ (1,0) [0|255] "" Gateway_MQB
 SG_ Airbag_03_BZ : 8|4@1+ (1,0) [0|15] "" Gateway_MQB
 SG_ AB_MKB_Safing : 63|1@1+ (1,0) [0|1] "" Gateway_MQB

BO_ 1633 Anhaenger_01: 8 Gateway_MQB
 SG_ AAG_BZ : 0|4@1+ (1,0) [0|15] "" Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ AAG_Bremsl_durch_ECD : 5|1@1+ (1,0) [0|1] "" Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ AAG_Anhaenger_abgesteckt : 6|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ AAG_NSL_aktiv : 7|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ AAG_Anhaenger_erkannt : 8|1@1+ (1,0) [0|1] "" Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ AAG_Blinker_H_aktiv : 9|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ AAG_Blinker_HL_def : 10|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ AAG_Blinker_HR_def : 11|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ AAG_Bremslicht_H_def : 12|1@1+ (1,0) [0|1] "" Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ AAG_Schlusslicht_HL_def : 13|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ AAG_Schlusslicht_HR_def : 14|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ AAG_AVS_Fehler_02 : 18|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ AAG_AVS_Stati : 20|4@1+ (1,0) [0|15] "" Vector__XXX

BO_ 1626 BCM_01: 8 Gateway_MQB
 SG_ BCM_Bremsbelag_Sensor : 12|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ BCM_Bremsfluessigkeit_Sensor : 13|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ BCM1_Licht_Warn : 14|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ BCM_Waschwasser_Sensor : 15|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ BCM_Kuehlmittel_Sensor : 16|1@1+ (1,0) [0|1] "" Motor_Diesel_MQB,Motor_Otto_MQB
 SG_ BCM1_Kl_15_HW_erkannt : 17|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ BCM_Eis_Offroad_Taste : 18|1@1+ (1,0) [0|1] "" Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,Motor_Diesel_MQB,Motor_Otto_MQB
 SG_ ZZH_Endlage_oben : 19|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ ZZH_Endlage_unten : 20|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ ZZH_Endlage_unplausibel : 21|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ BCM2_EZS_gedrueckt : 22|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ BCM2_SST_gedrueckt : 23|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ BCM_Hybrid_StartStopp_Taste : 24|1@1+ (1,0) [0|1] "" Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ BCM1_Warnblink_Taster : 25|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ BCM1_Valet_Parking_Taster : 26|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ BCM_Remotestart_Betrieb : 27|1@1+ (1,0) [0|1] "" Motor_Diesel_MQB,Motor_Otto_MQB
 SG_ BCM1_HSK_Taster : 28|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ BCM1_Heckrollo_Taster : 29|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ BCM1_Rueckfahrlicht_Schalter : 30|1@1+ (1,0) [0|1] "" Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ BCM1_MH_Schalter : 31|1@1+ (1,0) [0|1] "" Motor_Diesel_MQB,Motor_Otto_MQB
 SG_ BCM1_MH_WIV_Schalter : 32|1@1+ (1,0) [0|1] "" Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ BCM_Eco_Charisma_Taste : 33|1@1+ (1,0) [0|1] "" Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB
 SG_ BCM_Thermomanagement : 34|2@1+ (1,0) [0|3] "" Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ BCM_Thermomanagement_Fehler : 36|1@1+ (1,0) [0|1] "" Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ BCM_Thermomanagement_gueltig : 37|1@1+ (1,0) [0|1] "" Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ BCM1_Lichtwarn_Texte : 38|2@1+ (1,0) [0|3] "" Vector__XXX

BO_ 869 BEM_05: 8 Gateway_MQB
 SG_ BEM_P_Generator : 16|8@1+ (50,0) [0|12700] "Unit_Watt" Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ BEM_n_LLA : 24|2@1+ (1,0) [0|3] "" Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ BEM_01_Abschaltstufen : 26|3@1+ (1,0) [0|7] "" Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ BEM_Anf_KL : 29|1@1+ (1,0) [0|1] "" Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ BEM_StartStopp_Info : 30|2@1+ (1,0) [0|3] "" Motor_Diesel_MQB,Motor_Otto_MQB
 SG_ BEM_DFM : 32|5@1+ (3.225,0.025) [0.025|100] "" Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ BEM_EMLIN_ungueltig : 37|1@1+ (1,0) [0|1] "" Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ BEM_Batt_Ab : 38|1@1+ (1,0) [0|1] "" Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ BEM_Segel_Info : 48|2@1+ (1,0) [0|3] "" Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ BEM_HYB_DC_uSollLV : 50|6@1+ (0.1,10.6) [10.6|16] "Unit_Volt" LEH_MQB
 SG_ BEM_HYB_DC_uMinLV : 56|8@1+ (0.1,0) [0|25.3] "Unit_Volt" LEH_MQB

BO_ 1628 BMS_Hybrid_01: 8 BMS_MQB
 SG_ BMS_HYB_ASV_hinten_Status : 13|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ BMS_HYB_ASV_vorne_Status : 14|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ BMS_HYB_KD_Fehler : 15|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ BMS_HYB_BattFanSpd : 16|4@1+ (10,0) [0|100] "Unit_PerCent" Gateway_MQB
 SG_ BMS_HYB_VentilationReq : 20|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ BMS_HYB_Spuelbetrieb_Status : 21|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ BMS_HYB_Kuehlung_Anf : 22|2@1+ (1,0) [0|3] "" Gateway_MQB
 SG_ BMS_HYB_Temp_vor_Verd : 24|8@1+ (0.5,-40) [-40|86.5] "Unit_DegreCelsi" Gateway_MQB
 SG_ BMS_HYB_Temp_nach_Verd : 32|8@1+ (0.5,-40) [-40|86.5] "Unit_DegreCelsi" Gateway_MQB
 SG_ BMS_Temperatur : 40|8@1+ (0.5,-40) [-40|86.5] "Unit_DegreCelsi" Gateway_MQB
 SG_ BMS_Temperatur_Ansaugluft : 48|8@1+ (0.5,-40) [-40|86.5] "Unit_DegreCelsi" Gateway_MQB
 SG_ BMS_IstSpannung_HV : 56|8@1+ (1,100) [100|350] "Unit_Volt" Gateway_MQB

BO_ 901 Charisma_01: 8 Gateway_MQB
 SG_ CHA_Ziel_FahrPr_ALR : 0|4@1+ (1,0) [0|15] "" Vector__XXX
 SG_ CHA_Ziel_FahrPr_ESP : 4|4@1+ (1,0) [0|15] "" Vector__XXX
 SG_ CHA_Ziel_FahrPr_FL : 8|4@1+ (1,0) [0|15] "" Vector__XXX
 SG_ CHA_Fahrer_Umschaltung : 14|1@1+ (1,0) [0|1] "" Airbag_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB,SAK_MQB
 SG_ CHA_Ziel_FahrPr_MO : 16|4@1+ (1,0) [0|15] "" Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ CHA_Ziel_FahrPr_GE : 20|4@1+ (1,0) [0|15] "" Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB
 SG_ CHA_Ziel_FahrPr_ST : 24|4@1+ (1,0) [0|15] "" Vector__XXX
 SG_ CHA_Ziel_FahrPr_SCU : 28|4@1+ (1,0) [0|15] "" Vector__XXX
 SG_ CHA_Ziel_FahrPr_DR : 32|4@1+ (1,0) [0|15] "" Vector__XXX
 SG_ CHA_Ziel_FahrPr_QS : 36|4@1+ (1,0) [0|15] "" Vector__XXX
 SG_ CHA_Ziel_FahrPr_AFS : 40|4@1+ (1,0) [0|15] "" Vector__XXX
 SG_ CHA_Ziel_FahrPr_RGS : 44|4@1+ (1,0) [0|15] "" Airbag_MQB
 SG_ CHA_Ziel_FahrPr_EPS : 48|4@1+ (1,0) [0|15] "" Vector__XXX
 SG_ CHA_Ziel_FahrPr_ACC : 52|4@1+ (1,0) [0|15] "" Vector__XXX
 SG_ CHA_Ziel_FahrPr_SAK : 56|4@1+ (1,0) [0|15] "" SAK_MQB
 SG_ CHA_Ziel_FahrPr_MStSt : 60|4@1+ (1,0) [0|15] "" Vector__XXX

BO_ 945 DC_Hybrid_01: 8 LEH_MQB
 SG_ DC_HYB_iAktLV : 12|10@1+ (1,-511) [-511|510] "Unit_Amper" Gateway_MQB
 SG_ DC_HYB_iAktReserveLV : 22|10@1+ (1,-511) [-511|510] "Unit_Amper" Gateway_MQB
 SG_ DC_HYB_uAktLV : 32|8@1+ (0.1,0) [0|25.3] "Unit_Volt" Gateway_MQB
 SG_ DC_HYB_LangsRegelung : 40|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ DC_HYB_Abregelung_Temperatur : 41|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ DC_HYB_Fehler_RedLeistung : 42|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ DC_HYB_Fehler_intern : 43|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ DC_HYB_Fehler_Spannung : 44|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ DC_HYB_Auslastungsgrad : 56|8@1+ (0.4,0) [0|100] "Unit_PerCent" Gateway_MQB

BO_ 1714 Diagnose_01: 8 Gateway_MQB
 SG_ DGN_Verlernzaehler : 0|8@1+ (1,0) [0|254] "" Airbag_MQB,BMS_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,LEH_MQB,SAK_MQB
 SG_ KBI_Kilometerstand : 8|20@1+ (1,0) [0|1048573] "Unit_KiloMeter" Airbag_MQB,BMS_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,LEH_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB,SAK_MQB
 SG_ UH_Jahr : 28|7@1+ (1,2000) [2000|2127] "Unit_Year" Airbag_MQB,BMS_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,LEH_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB,SAK_MQB
 SG_ UH_Monat : 35|4@1+ (1,0) [1|12] "Unit_Month" Airbag_MQB,BMS_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,LEH_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB,SAK_MQB
 SG_ UH_Tag : 39|5@1+ (1,0) [1|31] "Unit_Day" Airbag_MQB,BMS_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,LEH_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB,SAK_MQB
 SG_ UH_Stunde : 44|5@1+ (1,0) [0|23] "Unit_Hours" Airbag_MQB,BMS_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,LEH_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB,SAK_MQB
 SG_ UH_Minute : 49|6@1+ (1,0) [0|59] "Unit_Minut" Airbag_MQB,BMS_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,LEH_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB,SAK_MQB
 SG_ UH_Sekunde : 55|6@1+ (1,0) [0|59] "Unit_Secon" Airbag_MQB,BMS_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,LEH_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB,SAK_MQB
 SG_ Kombi_02_alt : 62|1@1+ (1,0) [0|1] "" Airbag_MQB,BMS_MQB,LEH_MQB
 SG_ Uhrzeit_01_alt : 63|1@1+ (1,0) [0|1] "" Airbag_MQB,BMS_MQB,LEH_MQB

BO_ 1520 Dimmung_01: 8 Gateway_MQB
 SG_ DI_KL_58xd : 0|8@1+ (1,0) [0|253] "" Airbag_MQB
 SG_ DI_KL_58xs : 8|7@1+ (1,0) [0|100] "Unit_PerCent" Vector__XXX
 SG_ DI_Display_Nachtdesign : 15|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ DI_KL_58xt : 16|7@1+ (1,0) [0|100] "Unit_PerCent" Vector__XXX
 SG_ DI_Fotosensor : 24|16@1+ (1,0) [0|65535] "" Vector__XXX

BO_ 1603 Einheiten_01: 8 Gateway_MQB
 SG_ KBI_Einheit_Datum : 0|2@1+ (1,0) [0|3] "" Vector__XXX
 SG_ KBI_Einheit_Druck : 2|2@1+ (1,0) [0|3] "" Vector__XXX
 SG_ KBI_Einheit_Streckenanz : 4|1@1+ (1,0) [0|1] "" Motor_Diesel_MQB
 SG_ KBI_MFA_v_Einheit_02 : 5|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ KBI_Einheit_Temp : 6|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ KBI_Einheit_Uhrzeit : 7|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ KBI_Einheit_Verbrauch : 8|2@1+ (1,0) [0|3] "" Vector__XXX
 SG_ KBI_Einheit_Volumen : 10|2@1+ (1,0) [0|3] "" Vector__XXX
 SG_ KBI_Einheit_Sprache : 16|8@1+ (1,0) [0|255] "" Vector__XXX

BO_ 260 EPB_01: 8 Gateway_MQB
 SG_ EPB_01_CRC : 0|8@1+ (1,0) [0|255] "" Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ EPB_01_BZ : 8|4@1+ (1,0) [0|15] "" Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ EPB_QBit_Laengsbeschleunigung : 12|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ EPB_QBit_Pedalweg_Kuppl : 13|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ EPB_BCM2_Motor_Wakeup : 14|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ EPB_Freig_Verzoeg_Anf : 15|1@1+ (1,0) [0|1] "" Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ EPB_Verzoeg_Anf : 16|8@1+ (0.048,-7.968) [-7.968|4.224] "Unit_MeterPerSeconSquar" Vector__XXX
 SG_ EPB_Laengsbeschleunigung : 24|8@1+ (1,-128) [-128|126] "Unit_PerCentOfForceOfGravi" Vector__XXX
 SG_ EPB_Pedalweg_Kuppl : 32|8@1+ (0.4,0) [8|92] "Unit_PerCent" Vector__XXX
 SG_ EPB_Anfahrwunsch_erkannt : 48|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ EPB_DAA_Randbed_erf : 49|1@1+ (1,0) [0|1] "" Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB
 SG_ EPB_Fehlerstatus : 50|2@1+ (1,0) [0|3] "" Vector__XXX
 SG_ EPB_Schalterposition : 52|2@1+ (1,0) [0|3] "" Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ EPB_QBit_Schalterpos : 54|1@1+ (1,0) [0|1] "" Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ EPB_Konsistenz_ACC : 55|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ EPB_Spannkraft : 56|5@1+ (1,0) [0|29] "Unit_KiloNewto" Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB
 SG_ EPB_Status : 61|2@1+ (1,0) [0|3] "" Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB

BO_ 257 ESP_02: 8 Gateway_MQB
 SG_ CHECKSUM : 0|8@1+ (1,0) [0|255] "" Airbag_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ COUNTER : 8|4@1+ (1,0) [0|15] "" Airbag_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ ESP_QBit_Gierrate : 12|1@1+ (1,0) [0|1] "" Airbag_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB
 SG_ ESP_QBit_Laengsbeschl : 13|1@1+ (1,0) [0|1] "" Airbag_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ ESP_QBit_Querb : 14|1@1+ (1,0) [0|1] "" Airbag_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ ESP_Stillstandsflag : 15|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ ESP_Querbeschleunigung : 16|8@1+ (0.01,-1.27) [-1.27|1.27] "Unit_ForceOfGravi" Airbag_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ ESP_Laengsbeschl : 24|10@1+ (0.03125,-16) [-16|15.90625] "Unit_MeterPerSeconSquar" Airbag_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ ESP_Verteil_Wankmom : 34|5@1+ (0.1,-1) [-1|1] "" Vector__XXX
 SG_ ESP_QBit_Anf_Vert_Wank : 39|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ ESP_Gierrate : 40|14@1+ (0.01,0) [0|163.82] "Unit_DegreOfArcPerSecon" Airbag_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB
 SG_ ESP_VZ_Gierrate : 54|1@1+ (1,0) [0|1] "" Airbag_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB
 SG_ ESP_Notbremsanzeige : 55|1@1+ (1,0) [0|1] "" Airbag_MQB
 SG_ ESP_SpannungsAnf : 56|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ ESP_PLA_Abbruch : 57|3@1+ (1,0) [0|7] "" Vector__XXX
 SG_ ESP_Status_ESP_PLA : 60|4@1+ (1,0) [0|15] "" Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB

BO_ 262 ESP_05: 8 Gateway_MQB
 SG_ CHECKSUM : 0|8@1+ (1,0) [0|255] "" Airbag_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ COUNTER : 8|4@1+ (1,0) [0|15] "" Airbag_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ ESP_QBit_Bremsdruck : 12|1@1+ (1,0) [0|1] "" Airbag_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ ESP_QBit_Fahrer_bremst : 13|1@1+ (1,0) [0|1] "" Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ ESP_Schwelle_Unterdruck : 14|2@1+ (1,0) [0|3] "" Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ ESP_Bremsdruck : 16|10@1+ (0.3,-30) [-30|276.6] "Unit_Bar" Airbag_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ ESP_Fahrer_bremst : 26|1@1+ (1,0) [0|1] "" Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ ESP_Verz_TSK_aktiv : 27|1@1+ (1,0) [0|1] "" Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ ESP_Lenkeingriff_ADS : 28|1@1+ (1,0) [0|1] "" Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ ESP_Konsistenz_TSK : 29|1@1+ (1,0) [0|1] "" Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ ESP_Bremsruck_AWV2 : 30|1@1+ (1,0) [0|1] "" Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ ESP_Konsistenz_AWV2 : 31|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ ECD_Fehler : 32|1@1+ (1,0) [0|1] "" Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ ECD_nicht_verfuegbar : 33|1@1+ (1,0) [0|1] "" Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ ESP_Status_Bremsentemp : 34|1@1+ (1,0) [0|1] "" Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ ESP_Autohold_Standby : 35|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ ESP_HDC_Standby : 36|1@1+ (1,0) [0|1] "" Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB
 SG_ ESP_HBA_aktiv : 37|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ ESP_Prefill_ausgeloest : 38|1@1+ (1,0) [0|1] "" Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ ESP_Rueckwaertsfahrt_erkannt : 39|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ ESP_Status_Anfahrhilfe : 40|1@1+ (1,0) [0|1] "" Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ ESP_HDC_aktiv : 41|1@1+ (1,0) [0|1] "" Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB
 SG_ ESP_StartStopp_Info : 42|2@1+ (1,0) [0|3] "" Motor_Diesel_MQB,Motor_Otto_MQB
 SG_ ESP_Eingr_HL : 44|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ ESP_Eingr_HR : 45|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ ESP_Eingr_VL : 46|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ ESP_Eingr_VR : 47|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ ESP_BKV_Unterdruck : 48|8@1+ (4,0) [0|1012] "Unit_MilliBar" Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ ESP_Autohold_aktiv : 56|1@1+ (1,0) [0|1] "" Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ ESP_FStatus_Anfahrhilfe : 57|1@1+ (1,0) [0|1] "" Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB
 SG_ ESP_Verz_EPB_aktiv : 58|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ ECD_Bremslicht : 59|1@1+ (1,0) [0|1] "" Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ ESP_Verzoeg_EPB_verf : 60|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ ESP_Status_Bremsdruck : 61|1@1+ (1,0) [0|1] "" Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ ESP_Anforderung_EPB : 62|2@1+ (1,0) [0|3] "" Vector__XXX

BO_ 914 ESP_07: 8 Gateway_MQB
 SG_ ESP_07_CRC : 0|8@1+ (1,0) [0|255] "" Airbag_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ ESP_07_BZ : 8|4@1+ (1,0) [0|15] "" Airbag_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ ESP_ACC_LDE : 12|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ ESP_Quattro_Antrieb : 13|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ ESP_Codierung_ADS : 14|2@1+ (1,0) [0|3] "" Vector__XXX
 SG_ ESP_RTA_HL : 16|8@1+ (0.048828125,-6.20117) [-6.20117|6.152345625] "Unit_PerCent" Vector__XXX
 SG_ ESP_RTA_HR : 24|8@1+ (0.048828125,-6.20117) [-6.20117|6.152345625] "Unit_PerCent" Vector__XXX
 SG_ ESP_RTA_VR : 32|8@1+ (0.048828125,-6.20117) [-6.20117|6.152345625] "Unit_PerCent" Vector__XXX
 SG_ OBD_Fehler_Radsensor_HL : 40|4@1+ (1,0) [0|15] "" Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ OBD_Fehler_Radsensor_HR : 44|4@1+ (1,0) [0|15] "" Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ OBD_Fehler_Radsensor_VL : 48|4@1+ (1,0) [0|15] "" Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ OBD_Fehler_Radsensor_VR : 52|4@1+ (1,0) [0|15] "" Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ ESP_Qualifizierung_Antriebsart : 56|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ ESP_Offroad_Modus : 57|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ ESP_MKB_ausloesbar : 58|1@1+ (1,0) [0|1] "" Airbag_MQB
 SG_ ESP_MKB_Status : 59|1@1+ (1,0) [0|1] "" Airbag_MQB
 SG_ ESP_CM_Variante : 60|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ ESP_OBD_Status : 61|1@1+ (1,0) [0|1] "" Vector__XXX

BO_ 278 ESP_10: 8 Gateway_MQB
 SG_ CHECKSUM : 0|8@1+ (1,0) [0|255] "" Airbag_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ COUNTER : 8|4@1+ (1,0) [0|15] "" Airbag_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ ESP_QBit_Wegimpuls_VL : 12|1@1+ (1,0) [0|1] "" Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ ESP_QBit_Wegimpuls_VR : 13|1@1+ (1,0) [0|1] "" Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ ESP_QBit_Wegimpuls_HL : 14|1@1+ (1,0) [0|1] "" Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ ESP_QBit_Wegimpuls_HR : 15|1@1+ (1,0) [0|1] "" Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ ESP_Wegimpuls_VL : 16|10@1+ (1,0) [0|1000] "" Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ ESP_Wegimpuls_VR : 26|10@1+ (1,0) [0|1000] "" Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ ESP_Wegimpuls_HL : 36|10@1+ (1,0) [0|1000] "" Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ ESP_Wegimpuls_HR : 46|10@1+ (1,0) [0|1000] "" Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ ESP_VL_Fahrtrichtung : 56|2@1+ (1,0) [0|3] "" Airbag_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ ESP_VR_Fahrtrichtung : 58|2@1+ (1,0) [0|3] "" Airbag_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ ESP_HL_Fahrtrichtung : 60|2@1+ (1,0) [0|3] "" Airbag_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ ESP_HR_Fahrtrichtung : 62|2@1+ (1,0) [0|3] "" Airbag_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB

BO_ 178 ESP_19: 8 Gateway_MQB
 SG_ ESP_HL_Radgeschw_02 : 0|16@1+ (0.0075,0) [0|491.49] "Unit_KiloMeterPerHour" Airbag_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ ESP_HR_Radgeschw_02 : 16|16@1+ (0.0075,0) [0|491.49] "Unit_KiloMeterPerHour" Airbag_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ ESP_VL_Radgeschw_02 : 32|16@1+ (0.0075,0) [0|491.49] "Unit_KiloMeterPerHour" Airbag_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ ESP_VR_Radgeschw_02 : 48|16@1+ (0.0075,0) [0|491.49] "Unit_KiloMeterPerHour" Airbag_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB

BO_ 1629 ESP_20: 8 Gateway_MQB
 SG_ CHECKSUM : 0|8@1+ (1,0) [0|255] ""  XXX
 SG_ COUNTER : 8|4@1+ (1,0) [0|15] ""  XXX
 SG_ BR_Systemart : 12|2@1+ (1,0) [0|3] ""  XXX
 SG_ ESP_SpannungsAnf_02 : 14|2@1+ (1,0) [0|3] ""  XXX
 SG_ ESP_Zaehnezahl : 16|8@1+ (1,0) [0|255] ""  XXX
 SG_ ESP_Charisma_FahrPr : 24|4@1+ (1,0) [0|15] ""  XXX
 SG_ ESP_Charisma_Status : 28|2@1+ (1,0) [0|3] ""  XXX
 SG_ ESP_Wiederstart_Anz_01 : 30|1@1+ (1,0) [0|1] ""  XXX
 SG_ ESP_Wiederstart_Anz_02 : 31|1@1+ (1,0) [0|1] ""  XXX
 SG_ ESP_Wiederstart_Anz_03 : 32|1@1+ (1,0) [0|1] ""  XXX
 SG_ ESP_Wiederstart_Anz_04 : 33|1@1+ (1,0) [0|1] ""  XXX
 SG_ ESP_Stoppverbot_Anz_01 : 34|1@1+ (1,0) [0|1] ""  XXX
 SG_ ESP_Stoppverbot_Anz_02 : 35|1@1+ (1,0) [0|1] ""  XXX
 SG_ ESP_Stoppverbot_Anz_03 : 36|1@1+ (1,0) [0|1] ""  XXX
 SG_ ESP_Stoppverbot_Anz_04 : 37|1@1+ (1,0) [0|1] ""  XXX
 SG_ ESP_Stoppverbot_Anz_05 : 38|1@1+ (1,0) [0|1] ""  XXX
 SG_ ESP_Stoppverbot_Anz_06 : 39|1@1+ (1,0) [0|1] ""  XXX
 SG_ ESP_Stoppverbot_Anz_07 : 40|1@1+ (1,0) [0|1] ""  XXX
 SG_ ESP_Stoppverbot_Anz_Std : 41|1@1+ (1,0) [0|1] ""  XXX
 SG_ ESP_Dachrelingsensor : 42|2@1+ (1,0) [0|3] ""  XXX
 SG_ ESP_Stoppverbot_Anz_08 : 44|1@1+ (1,0) [0|1] ""  XXX
 SG_ HDC_Charisma_FahrPr : 45|4@1+ (1,0) [0|15] ""  XXX
 SG_ HDC_Charisma_Status : 49|2@1+ (1,0) [0|3] ""  XXX
 SG_ BR_QBit_Reifenumfang : 51|1@1+ (1,0) [0|1] ""  XXX
 SG_ BR_Reifenumfang : 52|12@1+ (1,0) [0|4095] "Unit_MilliMeter"  XXX

BO_ 253 ESP_21: 8 Gateway_MQB
 SG_ CHECKSUM : 0|8@1+ (1,0) [0|255] ""  XXX
 SG_ COUNTER : 8|4@1+ (1,0) [0|15] ""  XXX
 SG_ BR_Eingriffsmoment : 12|10@1+ (1,-509) [-509|509] ""  XXX
 SG_ ESP_PLA_Bremseingriff : 22|1@1+ (1.0,0.0) [0.0|1] ""  XXX
 SG_ ESP_Diagnose : 23|1@1+ (1.0,0.0) [0.0|1] ""  XXX
 SG_ ESC_Reku_Freigabe : 24|1@1+ (1.0,0.0) [0.0|1] ""  XXX
 SG_ ESC_v_Signal_Qualifier_High_Low : 25|3@1+ (1.0,0.0) [0.0|7] ""  XXX
 SG_ ESP_Vorsteuerung : 28|1@1+ (1.0,0.0) [0.0|1] ""  XXX
 SG_ ESP_AWV3_Brems_aktiv : 29|1@1+ (1.0,0.0) [0.0|1] ""  XXX
 SG_ OBD_Schlechtweg : 30|1@1+ (1.0,0.0) [0.0|1] ""  XXX
 SG_ OBD_QBit_Schlechtweg : 31|1@1+ (1.0,0.0) [0.0|1] ""  XXX
 SG_ ESP_v_Signal : 32|16@1+ (0.01,0) [0.00|655.32] "Unit_KiloMeterPerHour"  XXX
 SG_ ASR_Tastung_passiv : 48|1@1+ (1.0,0.0) [0.0|1] ""  XXX
 SG_ ESP_Tastung_passiv : 49|1@1+ (1.0,0.0) [0.0|1] ""  XXX
 SG_ ESP_Systemstatus : 50|1@1+ (1.0,0.0) [0.0|1] ""  XXX
 SG_ ASR_Schalteingriff : 51|2@1+ (1.0,0.0) [0.0|3] ""  XXX
 SG_ ESP_Haltebestaetigung : 53|1@1+ (1.0,0.0) [0.0|1] ""  XXX
 SG_ ESP_MKB_Abbruch_Geschw : 54|1@1+ (1.0,0.0) [0.0|1] ""  XXX
 SG_ ESP_QBit_v_Signal : 55|1@1+ (1.0,0.0) [0.0|1] ""  XXX
 SG_ ABS_Bremsung : 56|1@1+ (1.0,0.0) [0.0|1] ""  XXX
 SG_ ASR_Anf : 57|1@1+ (1.0,0.0) [0.0|1] ""  XXX
 SG_ MSR_Anf : 58|1@1+ (1.0,0.0) [0.0|1] ""  XXX
 SG_ EBV_Eingriff : 59|1@1+ (1.0,0.0) [0.0|1] ""  XXX
 SG_ EDS_Eingriff : 60|1@1+ (1.0,0.0) [0.0|1] ""  XXX
 SG_ ESP_Eingriff : 61|1@1+ (1.0,0.0) [0.0|1] ""  XXX
 SG_ ESP_ASP : 62|1@1+ (1.0,0.0) [0.0|1] ""  XXX
 SG_ ESP_Anhaltevorgang_ACC_aktiv : 63|1@1+ (1.0,0.0) [0.0|1] ""  XXX

BO_ 987 Gateway_72: 8 Gateway_MQB
 SG_ BCM_01_alt : 0|1@1+ (1,0) [0|1] "" Airbag_MQB
 SG_ SMLS_01_alt : 1|1@1+ (1,0) [0|1] "" Motor_Diesel_MQB,Motor_Otto_MQB
 SG_ ZV_02_alt : 2|1@1+ (1,0) [0|1] "" Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ Wischer_01_alt : 3|1@1+ (1,0) [0|1] "" Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ Anhaenger_01_alt : 4|1@1+ (1,0) [0|1] "" Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB
 SG_ Klima_Sensor_02_alt : 5|1@1+ (1,0) [0|1] "" Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ VSG_01_alt : 6|1@1+ (1,0) [0|1] "" Airbag_MQB
 SG_ Klima_01_alt : 7|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ WFS_01_alt : 8|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Licht_Anf_01_alt : 9|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ ZV_HFS_offen : 20|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ ZV_HBFS_offen : 21|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ VS_VD_offen_ver : 22|1@1+ (1,0) [0|1] "" Airbag_MQB
 SG_ VS_VD_zu_ver : 23|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ ZV_BT_offen : 24|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ BCM1_Rueckfahrlicht_Schalter : 25|1@1+ (1,0) [0|1] "" Airbag_MQB
 SG_ ZV_FT_offen : 26|1@1+ (1,0) [0|1] "" Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ Wischer_vorne_aktiv : 27|1@1+ (1,0) [0|1] "" Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ AAG_Anhaenger_erkannt : 28|1@1+ (1,0) [0|1] "" Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB
 SG_ BCM1_MH_Schalter : 29|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ ZV_HD_offen : 30|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Waschen_vorne_aktiv : 31|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ KL_Thermomanagement : 32|2@1+ (1,0) [0|3] "" Vector__XXX
 SG_ WFS_Schluessel_Fahrberecht : 34|4@1+ (1,0) [0|15] "" Vector__XXX
 SG_ BCM1_RFahrlicht_Fzg_Anf : 38|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ BCM1_RFahrlicht_Ahg_Anf : 39|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ BH_Fernlicht : 49|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ BH_Blinker_li : 50|1@1+ (1,0) [0|1] "" Motor_Diesel_MQB,Motor_Otto_MQB
 SG_ BH_Blinker_re : 51|1@1+ (1,0) [0|1] "" Motor_Diesel_MQB,Motor_Otto_MQB
 SG_ BCM1_OBD_FStatus_ATemp : 52|4@1+ (1,0) [0|15] "" Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ BCM1_Aussen_Temp_ungef : 56|8@1+ (0.5,-50) [-50|76] "Unit_DegreCelsi" Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB

BO_ 989 Gateway_74: 8 Gateway_MQB
 SG_ LH_EPS_01_alt : 0|1@1+ (1,0) [0|1] "" Motor_Diesel_MQB
 SG_ Kessy_04_alt : 1|1@1+ (1,0) [0|1] "" Motor_Diesel_MQB,Motor_Otto_MQB
 SG_ LIN_2_alt : 2|1@1+ (1,0) [0|1] "" Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB
 SG_ MFG_01_alt : 3|1@1+ (1,0) [0|1] "" Motor_Diesel_MQB,Motor_Otto_MQB
 SG_ GW_74_va_14 : 4|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Klima_02_alt : 5|1@1+ (1,0) [0|1] "" BMS_MQB
 SG_ Parkhilfe_01_alt : 6|1@1+ (1,0) [0|1] "" Motor_Diesel_MQB,Motor_Otto_MQB
 SG_ ELV_01_alt : 7|1@1+ (1,0) [0|1] "" Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ KY_StartStopp_Info : 16|2@1+ (1,0) [0|3] "" Motor_Diesel_MQB,Motor_Otto_MQB
 SG_ PH_StartStopp_Info : 18|2@1+ (1,0) [0|3] "" Motor_Diesel_MQB,Motor_Otto_MQB
 SG_ EPS_Lenkerposition : 20|2@1+ (1,0) [0|3] "" Motor_Diesel_MQB
 SG_ ELV_Anf_Klemme_50 : 22|1@1+ (1,0) [0|1] "" Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ MF_StartStopp_Info : 25|2@1+ (1,0) [0|3] "" Motor_Diesel_MQB,Motor_Otto_MQB
 SG_ KL_Geblaesespannung_Soll : 40|8@1+ (0.05,0.5) [2|13] "Unit_Volt" BMS_MQB
 SG_ KL_Umluftklappe_Status : 48|4@1+ (1,0) [0|15] "" Vector__XXX
 SG_ MFL_Tip_Down : 56|1@1+ (1,0) [0|1] "" Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB
 SG_ MFL_Tip_Up : 57|1@1+ (1,0) [0|1] "" Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB
 SG_ LS_Tiptronic_Fehler : 58|1@1+ (1,0) [0|1] "" Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB

BO_ 296 Getriebe_06: 3 Getriebe_DQ_Hybrid_MQB
 SG_ GE_WH_Sperre : 0|1@1+ (1,0) [0|1] "" Waehlhebel_MQB
 SG_ GE_Ausleuchtungsmode : 1|1@1+ (1,0) [0|1] "" Waehlhebel_MQB
 SG_ GE_Test_Freigabe : 2|1@1+ (1,0) [0|1] "" Waehlhebel_MQB
 SG_ GE_Ist_Fahrstufe : 4|4@1+ (1,0) [0|15] "" Waehlhebel_MQB
 SG_ GE_Testparameter_1 : 8|8@1+ (1,0) [0|255] "" Waehlhebel_MQB
 SG_ GE_Testparameter_2 : 16|8@1+ (1,0) [0|255] "" Waehlhebel_MQB

BO_ 173 Getriebe_11: 8 Getriebe_DQ_Hybrid_MQB
 SG_ CHECKSUM : 0|8@1+ (1,0) [0|255] ""  XXX
 SG_ COUNTER_DISABLED : 8|4@1+ (1,0) [0|15] ""  XXX
 SG_ GE_MMom_Soll_02 : 12|10@1+ (1,-509) [-509|509] ""  XXX
 SG_ GE_MMom_Vorhalt_02 : 22|10@1+ (1,-509) [-509|509] ""  XXX
 SG_ GE_Uefkt : 32|10@1+ (0.1,0) [0|102.2] ""  XXX
 SG_ GE_Fahrstufe : 42|4@1+ (1,0) [0|15] ""  XXX
 SG_ GE_reserv_Fahrstufe : 46|1@1+ (1,0) [0|1] ""  XXX
 SG_ GE_Schaltablauf : 47|2@1+ (1,0) [0|3] ""  XXX
 SG_ GE_Uefkt_unplausibel : 49|1@1+ (1,0) [0|1] ""  XXX
 SG_ GE_MMom_Status_02 : 50|3@1+ (1,0) [0|7] ""  XXX
 SG_ GE_Status_Kraftschluss : 53|3@1+ (1,0) [0|7] ""  XXX
 SG_ GE_MMom_Status : 56|2@1+ (1,0) [0|3] ""  XXX
 SG_ GE_Freig_MMom_Vorhalt : 58|1@1+ (1,0) [0|1] ""  XXX
 SG_ GE_Verbot_Ausblendung : 59|1@1+ (1,0) [0|1] ""  XXX
 SG_ GE_Zielgang : 60|4@1+ (1,0) [0|15] ""  XXX

BO_ 174 Getriebe_12: 8 Getriebe_DQ_Hybrid_MQB
 SG_ Getriebe_12_CRC : 0|8@1+ (1,0) [0|255] "" Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ Getriebe_12_BZ : 8|4@1+ (1,0) [0|15] "" Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ GE_Drehzahlmesser_Daempfung : 12|1@1+ (1,0) [0|1] "" Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ GE_Schubabschalt_Unt : 13|1@1+ (1,0) [0|1] "" Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ GE_Freigabe_Synchro : 14|1@1+ (1,0) [0|1] "" Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ GE_Synchro_Wunschdrehz : 15|9@1+ (25,0) [0|12750] "Unit_MinutInver" Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ GE_Synchro_Zeit : 24|8@1+ (20,0) [0|5080] "Unit_MilliSecon" Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ GE_Mom_Begr_Gradient : 32|8@1+ (10,0) [0|2540] "Unit_NewtoMeterPerSecon" Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ GE_Anheb_Solldrehz_Leerlauf : 40|8@1+ (10,0) [0|2540] "Unit_MinutInver" Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ GE_Aufnahmemoment : 48|10@1+ (1,-509) [-509|509] "Unit_NewtoMeter" Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ GE_Anf_Zylabsch : 58|2@1+ (1,0) [0|3] "" Vector__XXX
 SG_ GE_HYB_DZ_Eingriff : 62|2@1+ (1,0) [0|3] "" Motor_Hybrid_MQB

BO_ 301 Getriebe_13: 8 Getriebe_DQ_Hybrid_MQB
 SG_ Getriebe_13_CRC : 0|8@1+ (1,0) [0|255] "" Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ Getriebe_13_BZ : 8|4@1+ (1,0) [0|15] "" Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ GE_StartStopp_Info : 12|2@1+ (1,0) [0|3] "" Motor_Diesel_MQB,Motor_Otto_MQB
 SG_ GE_Langfr_Schutzmom_02 : 14|9@1+ (1,0) [0|509] "Unit_NewtoMeter" Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ GE_Eingangsdrehz : 48|14@1+ (1,0) [0|16381] "Unit_MinutInver" Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ GE_Notlauf : 62|1@1+ (1,0) [0|1] "" Gateway_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ GE_Freig_Langfr_Schutzmom : 63|1@1+ (1,0) [0|1] "" Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB

BO_ 968 Getriebe_14: 8 Getriebe_DQ_Hybrid_MQB
 SG_ GE_OBD_AbsperrVent : 12|4@1+ (1,0) [0|15] "" Gateway_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ GE_amax_moeglich : 16|9@1+ (0.024,-2.016) [-2.016|10.224] "Unit_MeterPerSeconSquar" Gateway_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ GE_Charisma_FahrPr : 25|4@1+ (1,0) [0|15] "" Gateway_MQB
 SG_ GE_Charisma_Status : 29|2@1+ (1,0) [0|3] "" Gateway_MQB
 SG_ GE_Verlustmoment : 32|8@1+ (1,0) [0|254] "" Gateway_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ GE_Freigabe_Verfallsinfo_WFS : 49|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ GE_Codierung_MSG : 50|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ GE_LaunchControl : 51|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ GE_Heizwunsch : 52|2@1+ (1,0) [0|3] "" Gateway_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ GE_OBD_Status : 54|1@1+ (1,0) [0|1] "" Gateway_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ GE_LFR_Adaption : 55|1@1+ (1,0) [0|1] "" Gateway_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ GE_Sumpftemperatur : 56|8@1+ (1,-58) [-58|196] "Unit_DegreCelsi" Gateway_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB

BO_ 158 Getriebe_Hybrid_01: 8 Getriebe_DQ_Hybrid_MQB
 SG_ Getriebe_Hybrid_01_CRC : 0|8@1+ (1,0) [0|255] "" Motor_Hybrid_MQB
 SG_ Getriebe_Hybrid_01_BZ : 8|4@1+ (1,0) [0|15] "" Motor_Hybrid_MQB
 SG_ GE_HYB_Fehlerstatus : 12|2@1+ (1,0) [0|3] "" Motor_Hybrid_MQB
 SG_ GE_HYB_Freigabe_K0 : 16|1@1+ (1,0) [0|1] "" Motor_Hybrid_MQB
 SG_ GE_HYB_Freigabe_LL_Reg : 17|1@1+ (1,0) [0|1] "" Motor_Hybrid_MQB
 SG_ GE_HYB_Freig_sSchl_K0 : 18|1@1+ (1,0) [0|1] "" Motor_Hybrid_MQB
 SG_ GE_HYB_Freig_VM_EM_Stop : 19|1@1+ (1,0) [0|1] "" Motor_Hybrid_MQB
 SG_ GE_HYB_Wiederstart : 20|1@1+ (1,0) [0|1] "" Motor_Hybrid_MQB
 SG_ GE_HYB_Filt_MomAufbau : 21|3@1+ (1,0) [0|7] "" Motor_Hybrid_MQB
 SG_ GE_HYB_nK0 : 24|8@1+ (25,0) [0|6350] "Unit_MinutInver" Vector__XXX
 SG_ GE_HYB_MomEingriff_EM : 32|6@1+ (0.5,0) [0|31.5] "Unit_NewtoMeter" LEH_MQB
 SG_ GE_HYB_VZ_MomEingriff_EM : 38|1@1+ (1,0) [0|1] "" LEH_MQB
 SG_ GE_HYB_Sportfaktor : 56|4@1+ (1,0) [0|15] "" Motor_Hybrid_MQB
 SG_ GE_HYB_VM_akt_halten : 61|1@1+ (1,0) [0|1] "" Motor_Hybrid_MQB
 SG_ GE_HYB_StartAnf : 62|1@1+ (1,0) [0|1] "" Motor_Hybrid_MQB
 SG_ GE_HYB_VM_Startkontr : 63|1@1+ (1,0) [0|1] "" Motor_Hybrid_MQB

BO_ 299 GRA_ACC_01: 8 Gateway_MQB
 SG_ CHECKSUM : 0|8@1+ (1,0) [0|255] "" Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ COUNTER : 8|4@1+ (1,0) [0|15] "" Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ GRA_Hauptschalter : 12|1@1+ (1,0) [0|1] "" Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ GRA_Abbrechen : 13|1@1+ (1,0) [0|1] "" Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ GRA_Typ_Hauptschalter : 14|1@1+ (1,0) [0|1] "" Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ GRA_Limiter : 15|1@1+ (1,0) [0|1] "" Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ GRA_Tip_Setzen : 16|1@1+ (1,0) [0|1] "" Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ GRA_Tip_Hoch : 17|1@1+ (1,0) [0|1] "" Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ GRA_Tip_Runter : 18|1@1+ (1,0) [0|1] "" Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ GRA_Tip_Wiederaufnahme : 19|1@1+ (1,0) [0|1] "" Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ GRA_Verstellung_Zeitluecke : 20|2@1+ (1,0) [0|3] "" Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ GRA_Codierung : 22|2@1+ (1,0) [0|3] "" Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ GRA_Fehler : 24|1@1+ (1,0) [0|1] "" Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ GRA_Typ468 : 25|2@1+ (1,0) [0|3] "" Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ GRA_Tip_Stufe_2 : 27|1@1+ (1,0) [0|1] "" Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ GRA_ButtonTypeInfo : 28|2@1+ (1,0) [0|1] "" Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB

BO_ 960 Klemmen_Status_01: 4 Gateway_MQB
 SG_ CHECKSUM : 0|8@1+ (1,0) [0|255] "" Airbag_MQB,BMS_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ COUNTER : 8|4@1+ (1,0) [0|15] "" Airbag_MQB,BMS_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ ZAS_Kl_S : 16|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ ZAS_Kl_15 : 17|1@1+ (1,0) [0|1] "" Airbag_MQB,BMS_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ ZAS_Kl_X : 18|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ ZAS_Kl_50 : 19|1@1+ (1,0) [0|1] "" Vector__XXX

BO_ 949 Klima_11: 8 Gateway_MQB
 SG_ KL_Drehz_Anh : 0|1@1+ (1,0) [0|1] "" Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ KL_Vorwarn_Komp_ein : 1|1@1+ (1,0) [0|1] "" Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ KL_AC_Schalter : 2|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ KL_Komp_Moment_alt : 3|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ KL_Zonen : 4|2@1+ (1,0) [0|3] "" Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ KL_Vorwarn_Zuheizer_ein : 6|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ KL_Zustand : 7|1@1+ (1,0) [0|1] "" Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ KL_Comp_rev_rq : 8|8@1+ (50,0) [0|8600] "Unit_MinutInver" Vector__XXX
 SG_ KL_Charisma_FahrPr : 16|4@1+ (1,0) [0|15] "" Vector__XXX
 SG_ KL_Charisma_Status : 20|2@1+ (1,0) [0|3] "" Vector__XXX
 SG_ KL_Comp_enable : 23|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ KL_Last_Kompr : 24|8@1+ (0.25,0) [0|63.5] "Unit_NewtoMeter" Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ KL_Spannungs_Anf : 32|2@1+ (1,0) [0|3] "" Vector__XXX
 SG_ KL_Thermomanagement : 34|2@1+ (1,0) [0|3] "" Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ KL_StartStopp_Info : 36|2@1+ (1,0) [0|3] "" Motor_Diesel_MQB,Motor_Otto_MQB
 SG_ KL_Anf_KL : 40|8@1+ (0.4,0) [0|101.6] "Unit_PerCent" Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ KL_el_Zuheizer_Stufe : 48|3@1+ (1,0) [0|7] "" Motor_Diesel_MQB

BO_ 1625 Klimakomp_01: 8 Gateway_MQB
 SG_ EKL_KD_Fehler : 15|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ EKL_Comp_SCI_com_stat : 16|2@1+ (1,0) [0|3] "" Vector__XXX
 SG_ EKL_Comp_output_stat : 18|2@1+ (1,0) [0|3] "" Vector__XXX
 SG_ EKL_Comp_main_stat : 20|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ EKL_Comp_ovld_stat : 21|3@1+ (1,0) [0|7] "" Vector__XXX
 SG_ EKL_Comp_Inv_stat : 24|2@1+ (1,0) [0|3] "" Vector__XXX
 SG_ EKL_Comp_photo_temp_stat : 30|2@1+ (1,0) [0|3] "" Vector__XXX
 SG_ EKL_Comp_photo_temp : 32|8@1+ (1,0) [0|254] "Unit_DegreCelsi" Vector__XXX
 SG_ EKL_Comp_current : 40|8@1+ (0.1,0) [0|25.4] "Unit_Amper" Motor_Hybrid_MQB
 SG_ EKL_Comp_rev_stat : 48|8@1+ (50,0) [0|8600] "Unit_MinutInver" Vector__XXX

BO_ 2549088277 KN_Airbag_01: 8 Airbag_MQB
 SG_ Airbag_01_KompSchutz : 0|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Airbag_01_Nachlauftyp : 4|4@1+ (1,0) [0|15] "" Gateway_MQB
 SG_ AB_KD_Fehler : 63|1@1+ (1,0) [0|1] "" Gateway_MQB

BO_ 2549088380 KN_EMotor_01: 8 LEH_MQB
 SG_ EMotor_KompSchutz : 0|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ EMotor_Nachlauftyp : 4|4@1+ (1,0) [0|15] "" Gateway_MQB
 SG_ EM_HYB_KD_Fehler : 63|1@1+ (1,0) [0|1] "" Gateway_MQB

BO_ 2549088375 KN_Getriebe_01: 8 Getriebe_DQ_Hybrid_MQB
 SG_ Getriebe_KompSchutz : 0|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Getriebe_Nachlauftyp : 4|4@1+ (1,0) [0|15] "" Gateway_MQB
 SG_ GE_KD_Fehler : 63|1@1+ (1,0) [0|1] "" Gateway_MQB

BO_ 2549088379 KN_Hybrid_01: 8 BMS_MQB
 SG_ Hybrid_KompSchutz : 0|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Hybrid_Nachlauftyp : 4|4@1+ (1,0) [0|15] "" Gateway_MQB
 SG_ BMS_HYB_KD_Fehler : 63|1@1+ (1,0) [0|1] "" Gateway_MQB

BO_ 2549088374 KN_MO_01: 8 Motor_Diesel_MQB
 SG_ Motor_KompSchutz : 0|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Motor_Nachlauftyp : 4|4@1+ (1,0) [0|15] "" Gateway_MQB
 SG_ MO_KD_Fehler : 63|1@1+ (1,0) [0|1] "" Gateway_MQB

BO_ 2549088284 KN_SAK: 8 SAK_MQB
 SG_ SAK_KompSchutz : 0|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ SAK_Nachlauftyp : 4|4@1+ (1,0) [0|15] "" Gateway_MQB
 SG_ SAK_KD_Fehler : 63|1@1+ (1,0) [0|1] "" Gateway_MQB

BO_ 779 Kombi_01: 8 Gateway_MQB
 SG_ KBI_ABS_Lampe : 0|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ KBI_ESP_Lampe : 1|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ KBI_BKL_Lampe : 2|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ KBI_Airbag_Lampe : 3|1@1+ (1,0) [0|1] "" Airbag_MQB
 SG_ KBI_SILA_gueltig : 4|1@1+ (1,0) [0|1] "" Airbag_MQB
 SG_ KBI_Lenkung_Lampe : 5|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ KBI_Vorglueh_System_Lampe : 6|1@1+ (1,0) [0|1] "" Motor_Diesel_MQB
 SG_ KBI_NV_in_Anzeige : 7|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Kombi_01_BZ : 8|4@1+ (1,0) [0|15] "" Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ KBI_Anzeigestatus_ACC : 12|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ KBI_Anzeigestatus_GRA : 13|2@1+ (1,0) [0|3] "" Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ KBI_Oeldruck_Schalter : 15|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ KBI_Tankwarnung : 16|1@1+ (1,0) [0|1] "" Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ KBI_MFA_v_Einheit_01 : 17|1@1+ (1,0) [0|1] "" Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ KBI_im_Stellgliedtest : 18|1@1+ (1,0) [0|1] "" Airbag_MQB
 SG_ KBI_Anzeigefehler_LDW : 19|2@1+ (1,0) [0|3] "" Vector__XXX
 SG_ KBI_Variante_USA : 21|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ KBI_Oeldruckwarnung : 22|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ KBI_Handbremse : 23|1@1+ (1,0) [0|1] "" Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB
 SG_ KBI_V_Digital : 24|9@1+ (1,0) [0|511] "" Vector__XXX
 SG_ KBI_PLA_in_Anzeige : 33|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ KBI_Anzeigefehler_NV : 34|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ KBI_Anzeigestatus_LIM : 35|2@1+ (1,0) [0|3] "" Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ KBI_angez_Geschw : 48|10@1+ (0.32,0) [0|325.12] "Unit_KiloMeterPerHour" Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ KBI_Einheit_Tacho : 58|1@1+ (1,0) [0|1] "" Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ KBI_Konsistenz_ACC : 59|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ KBI_Fehler_Anzeige_ACC : 60|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ KBI_Anzeigefehler_SWA : 61|2@1+ (1,0) [0|3] "" Vector__XXX

BO_ 1719 Kombi_02: 8 Gateway_MQB
 SG_ KBI_Kilometerstand : 0|20@1+ (1,0) [0|1048573] "Unit_KiloMeter" Vector__XXX
 SG_ KBI_Standzeit_02 : 20|17@1+ (1,0) [0|131068] "Unit_Secon" Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ KBI_Inhalt_Tank : 40|7@1+ (1,0) [0|125] "Unit_Liter" Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ KBI_FStatus_Tank : 47|1@1+ (1,0) [0|1] "" Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ KBI_QBit_Aussen_Temp_gef : 55|1@1+ (1,0) [0|1] "" Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ KBI_Aussen_Temp_gef : 56|8@1+ (0.5,-50) [-50|75] "Unit_DegreCelsi" Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB

BO_ 982 Licht_hinten_01: 8 Gateway_MQB
 SG_ Licht_hinten_01_BZ : 0|4@1+ (1,0) [0|15] "" Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ BCM2_Bremsl_durch_ECD : 5|1@1+ (1,0) [0|1] "" Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ LH_Aussenlicht_def : 7|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ LH_Standlicht_H_aktiv : 8|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ LH_Parklicht_HL_aktiv : 9|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ LH_Parklicht_HR_aktiv : 10|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ LH_Bremslicht_H_aktiv : 11|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ LH_Nebelschluss_aktiv : 12|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ LH_Rueckfahrlicht_aktiv : 13|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ LH_Blinker_HL_akt : 14|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ LH_Blinker_HR_akt : 15|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ LH_Blinker_li_def : 16|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ LH_Bremsl_li_def : 17|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ LH_Schlusslicht_li_def : 18|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ LH_Rueckf_li_def : 19|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ LH_Nebel_li_def : 20|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ LH_Schluss_Brems_Nebel_li_def : 21|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ LH_Schluss_Brems_Nebel_re_def : 22|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ LH_Schluss_Brems_li_def : 24|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ LH_Schluss_Nebel_li_def : 25|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ LH_SL_BRL_BLK_li_def : 26|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ LH_Brems_Blk_li_def : 27|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ LH_Blinker_re_def : 32|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ LH_Bremsl_re_def : 33|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ LH_Schlusslicht_re_def : 34|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ LH_Rueckf_re_def : 35|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ LH_Nebel_re_def : 36|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ LH_Schluss_Brems_re_def : 40|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ LH_Schluss_Nebel_re_def : 41|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ LH_SL_BRL_BLK_re_def : 42|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ LH_Brems_Blk_re_def : 43|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ LH_Kennzl_def : 48|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ LH_3_Bremsl_def : 49|1@1+ (1,0) [0|1] "" Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ LH_Nebel_mi_def : 50|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ LH_Rueckf_mi_def : 51|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ LH_Bremsl_li_ges_def : 54|1@1+ (1,0) [0|1] "" Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ LH_Bremsl_re_ges_def : 55|1@1+ (1,0) [0|1] "" Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB

BO_ 134 LWI_01: 8 Gateway_MQB
 SG_ CHECKSUM : 0|8@1+ (1,0) [0|255] "" Airbag_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ COUNTER : 8|4@1+ (1,0) [0|15] "" Airbag_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ LWI_Sensorstatus : 12|1@1+ (1,0) [0|1] "" Airbag_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ LWI_QBit_Sub_Daten : 13|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ LWI_QBit_Lenkradwinkel : 15|1@1+ (1,0) [0|1] "" Airbag_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ LWI_Lenkradwinkel : 16|13@1+ (0.1,0) [0|800] "Unit_DegreOfArc" Airbag_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ LWI_VZ_Lenkradwinkel : 29|1@1+ (1,0) [0|1] "" Airbag_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ LWI_VZ_Lenkradw_Geschw : 30|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ LWI_Lenkradw_Geschw : 31|9@1+ (5,0) [0|2500] "Unit_DegreOfArcPerSecon" Vector__XXX
 SG_ LWI_Sub_Daten : 40|16@1+ (1,0) [0|65535] "" Vector__XXX

BO_ 263 Motor_04: 8 Motor_Diesel_MQB
 SG_ MO_Istgang : 8|4@1+ (1,0) [0|15] "" Gateway_MQB
 SG_ MO_Sollgang : 12|4@1+ (1,0) [0|15] "" Gateway_MQB
 SG_ MO_Oeldruck : 16|8@1+ (0.04,0) [0|10] "Unit_Bar" Gateway_MQB
 SG_ MO_Anzeigedrehz : 24|12@1+ (3,0) [0|12282] "Unit_MinutInver" Gateway_MQB
 SG_ MO_Schaltempf_verfbar : 38|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ MO_Ladedruck : 39|9@1+ (0.01,0) [0|5.1] "Unit_Bar" Gateway_MQB
 SG_ MO_KVS : 48|15@1+ (1,0) [0|32767] "Unit_MicroLiter" Gateway_MQB
 SG_ MO_KVS_Ueberlauf : 63|1@1+ (1,0) [0|1] "" Gateway_MQB

BO_ 1600 Motor_07: 8 Motor_Diesel_MQB
 SG_ MO_QBit_Ansaugluft_Temp : 0|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ MO_QBit_Oel_Temp : 1|1@1+ (1,0) [0|1] "" Gateway_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB
 SG_ MO_QBit_Kuehlmittel_Temp : 2|1@1+ (1,0) [0|1] "" Gateway_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,LEH_MQB
 SG_ MO_Stellgliedtest_Soundaktuator : 3|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ MO_HYB_Fehler_HV_Netz : 4|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ MO_aktives_Getriebeheizen : 5|1@1+ (1,0) [0|1] "" Gateway_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB
 SG_ MO_Absperrventil_oeffnen : 6|2@1+ (1,0) [0|3] "" Gateway_MQB
 SG_ MO_Ansaugluft_Temp : 8|8@1+ (0.75,-48) [-48|141.75] "Unit_DegreCelsi" Gateway_MQB
 SG_ MO_Oel_Temp : 16|8@1+ (1,-60) [-60|192] "Unit_DegreCelsi" Gateway_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB
 SG_ MO_Kuehlmittel_Temp : 24|8@1+ (0.75,-48) [-48|141.75] "Unit_DegreCelsi" Gateway_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,LEH_MQB
 SG_ MO_Hoeheninfo : 32|8@1+ (0.00781,0) [0|1.98374] "" Gateway_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB
 SG_ MO_Kennfeldk : 40|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ MO_Versionsinfo : 41|6@1+ (1,0) [0|63] "" Gateway_MQB
 SG_ MO_Getriebe_kuehlen : 47|1@1+ (1,0) [0|1] "" Gateway_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB
 SG_ MO_Mom_Traegheit_02 : 48|5@1+ (0.01,0) [0|0.31] "Unit_KiloGramMeterSquar" Gateway_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB
 SG_ MO_Heizungspumpenansteuerung : 53|4@1+ (10,0) [0|100] "Unit_PerCent" Gateway_MQB
 SG_ MO_SpannungsAnf : 57|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ MO_Nachlaufzeit_Heizungspumpe : 58|6@1+ (15,0) [0|945] "Unit_Secon" Gateway_MQB

BO_ 1607 Motor_09: 8 Motor_Diesel_MQB
 SG_ MO_ITM_Kuehlmittel_Temp : 0|8@1+ (0.75,-48) [-45.75|143.25] "Unit_DegreCelsi" Gateway_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB
 SG_ MO_E85_Sensor : 8|4@1+ (10,0) [0|100] "Unit_PerCent" Gateway_MQB
 SG_ SCR_Anz_Motorstarts : 12|4@1+ (1,0) [0|8] "" Gateway_MQB
 SG_ SCR_Reichweite : 16|15@1+ (1,0) [0|32766] "" Gateway_MQB
 SG_ SCR_Warnstufe_1 : 32|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ SCR_Warnstufe_2 : 33|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ SCR_Text : 34|3@1+ (1,0) [0|7] "" Gateway_MQB
 SG_ SCR_Akustik : 37|2@1+ (1,0) [0|3] "" Gateway_MQB
 SG_ MO_Kraftstofffilter_Wasser : 40|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ SCR_Systemfehler : 41|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ SCR_Inducement_Strategie : 42|2@1+ (1,0) [0|3] "" Gateway_MQB
 SG_ MO_CO2_Faktor : 44|12@1+ (1,0) [1|4094] "Unit_GramPerLiter" Gateway_MQB

BO_ 167 Motor_11: 8 Motor_Diesel_MQB
 SG_ Motor_11_CRC : 0|8@1+ (1,0) [0|255] "" Gateway_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB
 SG_ Motor_11_BZ : 8|4@1+ (1,0) [0|15] "" Gateway_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB
 SG_ MO_Mom_Soll_Roh : 12|10@1+ (1,-509) [-509|509] "Unit_NewtoMeter" Gateway_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB
 SG_ MO_Mom_Ist_Summe : 22|10@1+ (1,-509) [-509|509] "Unit_NewtoMeter" Gateway_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,SAK_MQB
 SG_ MO_Mom_Traegheit_Summe : 32|10@1+ (1,-509) [-509|509] "Unit_NewtoMeter" Gateway_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB
 SG_ MO_Mom_Soll_gefiltert : 42|10@1+ (1,-509) [-509|509] "Unit_NewtoMeter" Gateway_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB
 SG_ MO_Mom_Schub : 52|9@1+ (1,-509) [-509|0] "Unit_NewtoMeter" Gateway_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB
 SG_ MO_Status_Normalbetrieb_01 : 61|1@1+ (1,0) [0|1] "" Gateway_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB
 SG_ MO_erste_Ungenauschwelle : 62|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ MO_QBit_Motormomente : 63|1@1+ (1,0) [0|1] "" Gateway_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB

BO_ 168 Motor_12: 8 Motor_Diesel_MQB
 SG_ Motor_12_CRC : 0|8@1+ (1,0) [0|255] "" Gateway_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,LEH_MQB
 SG_ Motor_12_BZ : 8|4@1+ (1,0) [0|15] "" BMS_MQB,Gateway_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,LEH_MQB
 SG_ MO_Mom_neg_verfuegbar : 12|9@1+ (1,-509) [-509|0] "Unit_NewtoMeter" Gateway_MQB
 SG_ MO_Mom_Begr_stat : 21|9@1+ (1,0) [0|509] "Unit_NewtoMeter" Gateway_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB
 SG_ MO_Mom_Begr_dyn : 30|10@1+ (1,-509) [-509|509] "Unit_NewtoMeter" Gateway_MQB
 SG_ MO_Momentenintegral_02 : 40|7@1+ (1,0) [0|100] "Unit_PerCent" Gateway_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB
 SG_ MO_QBit_Drehzahl_01 : 47|1@1+ (1,0) [0|1] "" BMS_MQB,Gateway_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,LEH_MQB
 SG_ MO_Drehzahl_01 : 48|16@1+ (0.25,0) [0|16383] "Unit_MinutInver" BMS_MQB,Gateway_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,LEH_MQB,SAK_MQB

BO_ 958 Motor_14: 8 Motor_Diesel_MQB
 SG_ MO_StartStopp_Status : 12|2@1+ (1,0) [0|3] "" Gateway_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB
 SG_ MO_StartStopp_Wiederstart : 14|1@1+ (1,0) [0|1] "" Gateway_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB
 SG_ MO_StartStopp_Motorstopp : 15|1@1+ (1,0) [0|1] "" Gateway_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB
 SG_ MO_Freig_Reku : 16|2@1+ (1,0) [0|3] "" Gateway_MQB
 SG_ MO_Kl_75 : 18|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ MO_Kl_50 : 19|1@1+ (1,0) [0|1] "" Gateway_MQB,LEH_MQB
 SG_ MO_Gangposition : 20|4@1+ (1,0) [0|15] "" Gateway_MQB
 SG_ MO_StartStopp_Fahrerwunsch : 24|2@1+ (1,0) [0|3] "" Gateway_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB
 SG_ MO_HYB_Fahrbereitschaft : 26|1@1+ (1,0) [0|1] "" BMS_MQB,Gateway_MQB
 SG_ MO_Ext_E_Fahrt_aktiv : 27|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ MO_Fahrer_bremst : 28|1@1+ (1,0) [0|1] "" Gateway_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB
 SG_ MO_QBit_Fahrer_bremst : 29|1@1+ (1,0) [0|1] "" Gateway_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB
 SG_ MO_BLS : 30|1@1+ (1,0) [0|1] "" Gateway_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB
 SG_ MO_Konsistenz_Bremsped : 31|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ MO_Timeout_ESP : 32|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ MO_Klima_Eingr : 33|2@1+ (1,0) [0|3] "" Gateway_MQB
 SG_ MO_Aussp_Anlass : 35|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ MO_Freig_Anlass : 36|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ MO_Kuppl_schalter : 37|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ MO_Interlock : 38|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ MO_Motor_laeuft : 39|1@1+ (1,0) [0|1] "" Gateway_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB
 SG_ MO_Kickdown : 40|1@1+ (1,0) [0|1] "" Gateway_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB
 SG_ MO_Status_Zylabschalt_01 : 41|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ MO_EKlKomLeiRed : 42|2@1+ (1,0) [0|3] "" Gateway_MQB
 SG_ MO_Handshake_STH : 44|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ MO_BKV_Unterdruckwarnung : 45|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ MO_Freigabe_Segeln : 46|1@1+ (1,0) [0|1] "" Gateway_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB
 SG_ MO_PTC_Status : 47|3@1+ (1,0) [0|7] "" Gateway_MQB
 SG_ MO_QBit_Gangposition : 50|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ MO_Signalquelle_Gangposition : 51|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ MO_Remotestart_Betrieb : 52|1@1+ (1,0) [0|1] "" Gateway_MQB

BO_ 1631 Motor_16: 8 Motor_Diesel_MQB
 SG_ TSK_QBit_Steigung : 12|1@1+ (1,0) [0|1] "" Gateway_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB
 SG_ TSK_QBit_Fahrzeugmasse : 13|1@1+ (1,0) [0|1] "" Gateway_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB
 SG_ MO_SpannungsAnf_02 : 14|2@1+ (1,0) [0|3] "" Gateway_MQB
 SG_ MO_DPF_reg : 16|1@1+ (1,0) [0|1] "" Gateway_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB
 SG_ MO_Heizstrom_EKAT : 17|7@1+ (1,0) [0|126] "Unit_Amper" Gateway_MQB
 SG_ MO_Heizstrom_SCR : 24|6@1+ (1,0) [0|62] "Unit_Amper" Gateway_MQB
 SG_ TSK_Fahrzeugmasse_02 : 48|8@1+ (32,0) [0|8128] "Unit_KiloGram" Gateway_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB
 SG_ TSK_Steigung : 56|8@1+ (0.8,-101.6) [-101.6|101.6] "Unit_PerCent" Gateway_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB

BO_ 812 Motor_17: 8 Motor_Diesel_MQB
 SG_ MO_Prio_MAX_Wunschdrehzahl : 12|1@1+ (1,0) [0|1] "" Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB
 SG_ MO_Prio_MIN_Wunschdrehzahl : 13|1@1+ (1,0) [0|1] "" Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB
 SG_ MO_Luftpfad_aktiv : 14|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ MO_v_Begrenz_Aktivierbar : 15|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ MO_Drehzahlbeeinflussung : 16|8@1+ (0.39,0) [0|99.45] "Unit_PerCent" Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB
 SG_ MO_MIN_Wunschdrehzahl : 24|8@1+ (25,0) [0|6350] "Unit_MinutInver" Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB
 SG_ MO_MAX_Wunschdrehzahl : 32|9@1+ (25,0) [0|12750] "Unit_MinutInver" Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB
 SG_ MO_Charisma_FahrPr : 41|4@1+ (1,0) [0|15] "" Gateway_MQB
 SG_ MO_Charisma_Status : 45|2@1+ (1,0) [0|3] "" Gateway_MQB

BO_ 1648 Motor_18: 8 Motor_Diesel_MQB
 SG_ MO_Hybrid_StartStopp_LED : 43|2@1+ (1,0) [0|3] "" Gateway_MQB
 SG_ MO_Eis_Offroad_LED : 45|2@1+ (1,0) [0|3] "" Gateway_MQB
 SG_ MO_Anzahl_Abgesch_Zyl : 47|3@1+ (1,0) [0|7] "" Gateway_MQB
 SG_ MO_Zylabsch_Texte : 50|2@1+ (1,0) [0|3] "" Gateway_MQB
 SG_ MO_E85_BS_Texte : 52|3@1+ (1,0) [0|7] "" Gateway_MQB
 SG_ MO_Drehzahl_Warnung : 55|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ MO_obere_Drehzahlgrenze : 56|8@1+ (50,0) [50|12750] "Unit_MinutInver" Gateway_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB

BO_ 289 Motor_20: 8 Motor_Diesel_MQB
 SG_ CHECKSUM : 0|8@1+ (1,0) [0|255] "" XXX
 SG_ COUNTER : 8|4@1+ (1,0) [0|255] "" XXX
 SG_ MO_Fahrpedalrohwert_01 : 12|8@1+ (0.4,0) [0.0|101.6] "Unit_PerCent"  XXX
 SG_ MO_QBit_Fahrpedalwerte_01 : 20|1@1+ (1.0,0.0) [0.0|1] ""  XXX
 SG_ MO_Fahrpedalgradient : 21|8@1+ (25,0) [0|6350] "Unit_PerCentPerSecon"  XXX
 SG_ MO_Sig_Fahrpedalgradient : 29|1@1+ (1.0,0.0) [0.0|1] ""  XXX
 SG_ MO_rel_Saugrohrdruck : 30|6@1+ (18,0) [0|1116] "Unit_MilliBar"  XXX
 SG_ MO_rel_Saugrohrdruck_gem_err : 36|1@1+ (1.0,0.0) [0.0|1] ""  XXX
 SG_ MO_Moment_im_Leerlauf : 37|1@1+ (1.0,0.0) [0.0|1] ""  XXX
 SG_ MO_Schubabschaltung : 38|1@1+ (1.0,0.0) [0.0|1] ""  XXX
 SG_ MO_StartStopp_StoppVorbereitung : 39|1@1+ (1.0,0.0) [0.0|1] ""  XXX
 SG_ MO_Solldrehz_Leerlauf : 40|8@1+ (10,0) [0|2540] "Unit_MinutInver"  XXX
 SG_ MO_Entkopplung_Sollschlupf : 48|7@1+ (20,0) [0|2480] "Unit_MinutInver"  XXX
 SG_ MO_temporaere_Fahrerabwesenheit : 55|1@1+ (1.0,0.0) [0.0|1] ""  XXX
 SG_ TSK_a_Soll_gradientenbegrenzt : 57|7@1+ (0.1,-7.2) [-7.2|5.4] "Unit_MeterPerSeconSquar"  XXX

BO_ 967 Motor_26: 8 Motor_Diesel_MQB
 SG_ MO_HYB_Status_HV_Ladung : 8|3@1+ (1,0) [0|7] "" Gateway_MQB
 SG_ WIV_Anzeige_aktiv : 12|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ WIV_Oelmin_Warn : 13|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ WIV_Sensorfehler : 14|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ WIV_Schieflage : 15|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ WIV_Oelstand : 16|4@1+ (12.5,0) [0|100] "Unit_PerCent" Gateway_MQB
 SG_ MO_Zustand_HWP : 20|2@1+ (1,0) [0|3] "" Gateway_MQB
 SG_ WIV_Oelsystem_aktiv : 24|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ WIV_nicht_betriebswarm : 25|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ WIV_Ueberfuell_Warn : 26|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ WIV_laufender_Motor : 27|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ MO_HYB_Text_1 : 28|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ MO_HYB_Text_2 : 29|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ MO_HYB_Text_3 : 30|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ MO_HYB_Text_4 : 31|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ MO_Text_Motorstart : 32|4@1+ (1,0) [0|15] "" Gateway_MQB
 SG_ MO_HYB_Text_5 : 36|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ MO_HYB_Text_6 : 37|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ MO_HYB_Text_7 : 38|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ MO_Text_Partikelfil_Reg : 41|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ WIV_Oelmenge : 43|5@1+ (125,0) [0|3875] "Unit_MilliLiter" Gateway_MQB
 SG_ MO_Systemlampe : 48|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ MO_OBD2_Lampe : 49|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ MO_Heissleuchte : 50|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ MO_Partikel_Lampe : 51|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ MO_Winterfahrprog : 52|1@1+ (1,0) [0|1] "" Gateway_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB
 SG_ WIV_Oelstand_nicht_vorhanden : 53|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ WIV_nachfuellanzeige_ein : 54|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ WIV_Ueberfuell_deaktiv : 55|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ WIV_Unterfuell_Warn : 56|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ MO_Tankdeckel_Lampe : 57|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ MO_Text_Tankdeckelwarn : 58|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ WIV_Oeldr_Warn_Motor : 60|1@1+ (1,0) [0|1] "" Gateway_MQB

BO_ 1601 Motor_Code_01: 8 Motor_Diesel_MQB
 SG_ Motor_Code_01_CRC : 0|8@1+ (1,0) [0|255] "" Gateway_MQB
 SG_ Motor_Code_01_BZ : 8|4@1+ (1,0) [0|15] "" Gateway_MQB
 SG_ MO_Faktor_Momente_02 : 12|2@1+ (1,0) [0|3] "" Gateway_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB
 SG_ MO_Hybridfahrzeug : 14|2@1+ (1,0) [0|3] "" Gateway_MQB
 SG_ MO_Code : 16|8@1+ (1,0) [0|255] "" Gateway_MQB,SAK_MQB
 SG_ MO_Getriebe_Code : 24|6@1+ (1,0) [0|63] "" Gateway_MQB
 SG_ MO_StartStopp_Codiert : 30|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ MO_Anzahl_Zyl : 32|4@1+ (1,0) [0|15] "" Gateway_MQB
 SG_ MO_Kraftstoffart : 36|4@1+ (1,0) [0|15] "" Gateway_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB
 SG_ MO_Hubraum : 40|7@1+ (0.1,0) [0|12.7] "Unit_Liter" Gateway_MQB
 SG_ MO_Ansaugsystem : 47|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ MO_Leistung : 48|9@1+ (1,0) [0|511] "Unit_KiloWatt" Gateway_MQB
 SG_ MO_Abgastyp_EOBD : 57|1@1+ (1,0) [0|1] "" BMS_MQB,Gateway_MQB,LEH_MQB
 SG_ MO_Abgastyp_OBD : 58|1@1+ (1,0) [0|1] "" BMS_MQB,Gateway_MQB,LEH_MQB
 SG_ MO_DPF_verbaut : 59|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ TSK_Codierung : 60|3@1+ (1,0) [0|7] "" Gateway_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB
 SG_ MO_Einspritzart : 63|1@1+ (1,0) [0|1] "" Gateway_MQB

BO_ 157 Motor_Hybrid_01: 8 Motor_Hybrid_MQB
 SG_ Motor_Hybrid_01_CRC : 0|8@1+ (1,0) [0|255] "" Getriebe_DQ_Hybrid_MQB
 SG_ Motor_Hybrid_01_BZ : 8|4@1+ (1,0) [0|15] "" BMS_MQB,Getriebe_DQ_Hybrid_MQB
 SG_ MO_HYB_IstStatusK0 : 12|2@1+ (1,0) [0|3] "" Getriebe_DQ_Hybrid_MQB
 SG_ MO_HYB_max_ind_VM_Mom : 16|10@1+ (1,0) [0|1021] "Unit_NewtoMeter" Getriebe_DQ_Hybrid_MQB
 SG_ MO_HYB_Zielzustand : 26|3@1+ (1,0) [0|7] "" BMS_MQB,Getriebe_DQ_Hybrid_MQB
 SG_ MO_HYB_Startmodus : 29|3@1+ (1,0) [0|7] "" Vector__XXX
 SG_ MO_HYB_Startmodus_PQ3x : 32|1@1+ (1,0) [0|1] "" Getriebe_DQ_Hybrid_MQB
 SG_ MO_HYB_Stoppmodus : 33|1@1+ (1,0) [0|1] "" Getriebe_DQ_Hybrid_MQB
 SG_ MO_HYB_VM_Mom_oE : 40|10@1+ (1,-100) [-100|922] "Unit_NewtoMeter" Getriebe_DQ_Hybrid_MQB
 SG_ MO_HYB_VM_aktiv : 50|1@1+ (1,0) [0|1] "" Getriebe_DQ_Hybrid_MQB
 SG_ MO_HYB_Schaltverhinderung : 51|1@1+ (1,0) [0|1] "" Getriebe_DQ_Hybrid_MQB

BO_ 811 Motor_Hybrid_02: 8 Motor_Hybrid_MQB
 SG_ MO_HYB_E_Faktor : 12|4@1+ (1,0) [0|15] "" Getriebe_DQ_Hybrid_MQB
 SG_ MO_HYB_Drehzahl_VM : 16|16@1+ (0.25,0) [0|16256] "Unit_MinutInver" Getriebe_DQ_Hybrid_MQB
 SG_ MO_HYB_LowSpeedModus : 32|1@1+ (1,0) [0|1] "" Getriebe_DQ_Hybrid_MQB

BO_ 2600468501 NMH_Airbag_01: 8 Airbag_MQB
 SG_ NM_Airbag_01_SNI : 0|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ NM_Airbag_01_NM_State : 16|6@1+ (1,0) [0|63] "" BMS_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,LEH_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ NM_Airbag_01_Car_Wakeup : 22|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ NM_Airbag_01_Wakeup : 24|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ NM_Airbag_01_NM_aktiv_KL15 : 32|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ NM_Airbag_01_NM_aktiv_Diagnose : 33|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ NM_Airbag_01_NM_aktiv_Tmin : 34|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ NM_Airbag_01_UDS_CC : 63|1@1+ (1,0) [0|1] "" Vector__XXX

BO_ 2600468604 NMH_EMotor_01: 8 LEH_MQB
 SG_ NM_EMotor_01_SNI : 0|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ NM_EMotor_01_NM_State : 16|6@1+ (1,0) [0|63] "" BMS_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,LEH_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ NM_EMotor_01_Car_Wakeup : 22|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ NM_EMotor_01_Wakeup : 24|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ NM_EMotor_01_NM_aktiv_KL15 : 32|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ NM_EMotor_01_NM_aktiv_Diagnose : 33|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ NM_EMotor_01_NM_aktiv_Tmin : 34|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ NM_EMotor_01_NL_Daten_EEPROM : 48|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ NM_EMotor_01_UDS_CC : 63|1@1+ (1,0) [0|1] "" Vector__XXX

BO_ 2600468496 NMH_Gateway: 8 Gateway_MQB
 SG_ NM_Gateway_SNI : 0|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ NM_Gateway_NM_State : 16|6@1+ (1,0) [0|63] "" BMS_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,LEH_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ NM_Gateway_Car_Wakeup : 22|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ NM_Gateway_Wakeup : 24|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ NM_Gateway_NM_aktiv_KL15 : 32|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ NM_Gateway_NM_aktiv_Diagnose : 33|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ NM_Gateway_NM_aktiv_Tmin : 34|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ NM_Gateway_ACAN_Aktivitaet : 35|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ NM_Gateway_FCAN_Aktivitaet : 36|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ NM_Gateway_KCAN_Aktivitaet : 37|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ NM_Gateway_ICAN_Aktivitaet : 38|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ NM_Gateway_DiagCAN_Aktivitaet : 39|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ NM_Gateway_ECAN_Aktivitaet : 40|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ NM_Gateway_Energie_LIN_Aktivi000 : 41|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ NM_Gateway_Bedien_LIN_Aktivitaet : 42|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ NM_Gateway_EM_Aktivitaet : 43|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ NM_Gateway_NL_EM : 48|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ NM_Gateway_NL_Shutdown : 49|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ NM_Gateway_NL_Spg_Messung : 50|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ NM_Gateway_NL_Wakeup_Monitor : 51|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ NM_Gateway_UDS_CC : 63|1@1+ (1,0) [0|1] "" Vector__XXX

BO_ 2600468599 NMH_Getriebe_01: 8 Getriebe_DQ_Hybrid_MQB
 SG_ NM_Getriebe_01_SNI : 0|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ NM_Getriebe_01_NM_State : 16|6@1+ (1,0) [0|63] "" BMS_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,LEH_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ NM_Getriebe_01_Car_Wakeup : 22|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ NM_Getriebe_01_Wakeup : 24|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ NM_Getriebe_01_NM_aktiv_KL15 : 32|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ NM_Getriebe_01_NM_aktiv_Diagnose : 33|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ NM_Getriebe_01_NM_aktiv_Tmin : 34|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ NM_Getriebe_01_NM_aktiv_v_gr_0 : 35|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ NM_Getriebe_01_NM_aktiv_Pos_Erk : 36|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ NM_Getriebe_01_NM_aktiv_Umg_Bed : 37|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ NM_Getriebe_01_NL_Daten_EEPROM : 48|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ NM_Getriebe_01_UDS_CC : 63|1@1+ (1,0) [0|1] "" Vector__XXX

BO_ 2600468603 NMH_Hybrid_01: 8 BMS_MQB
 SG_ NM_Hybrid_01_SNI : 0|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ NM_Hybrid_01_NM_State : 16|6@1+ (1,0) [0|63] "" BMS_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,LEH_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ NM_Hybrid_01_Car_Wakeup : 22|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ NM_Hybrid_01_Wakeup : 24|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ NM_Hybrid_01_NM_aktiv_KL15 : 32|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ NM_Hybrid_01_NM_aktiv_Diagnose : 33|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ NM_Hybrid_01_NM_aktiv_Tmin : 34|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ NM_Hybrid_01_NL_Daten_EEPROM : 48|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ NM_Hybrid_01_NL_Luefter : 49|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ NM_Hybrid_01_UDS_CC : 63|1@1+ (1,0) [0|1] "" Vector__XXX

BO_ 2600468598 NMH_MO_01: 8 Motor_Diesel_MQB
 SG_ NM_MO_01_SNI : 0|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ NM_MO_01_NM_State : 16|6@1+ (1,0) [0|63] "" BMS_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,LEH_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ NM_MO_01_Car_Wakeup : 22|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ NM_MO_01_Wakeup : 24|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ NM_MO_01_NM_aktiv_KL15 : 32|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ NM_MO_01_NM_aktiv_Diagnose : 33|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ NM_MO_01_NM_aktiv_Tmin : 34|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ NM_MO_01_NM_aktiv_HV_Abschaltung : 35|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ NM_MO_01_NM_aktiv_EKP_Vorlauf : 36|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ NM_MO_01_NM_aktiv_STH_Betrieb : 37|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ NM_MO_01_NL_Kuehlerluefter : 48|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ NM_MO_01_NL_Diagnose : 49|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ NM_MO_01_NL_WFS : 50|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ NM_MO_01_NL_EEPROM : 51|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ NM_MO_01_NL_Sonstige : 52|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ NM_MO_01_UDS_CC : 63|1@1+ (1,0) [0|1] "" Vector__XXX

BO_ 913 OBD_01: 8 Motor_Diesel_MQB
 SG_ OBD_Calc_Load_Val : 0|8@1+ (0.39215686275,0) [0|100] "Unit_PerCent" BMS_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,LEH_MQB
 SG_ OBD_Eng_Cool_Temp : 8|8@1+ (1,-40) [-40|215] "Unit_DegreCelsi" BMS_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,LEH_MQB
 SG_ OBD_Abs_Throttle_Pos : 16|8@1+ (0.39215686275,0) [0|100] "Unit_PerCent" BMS_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,LEH_MQB
 SG_ OBD_Abs_Load_Val : 24|16@1+ (0.39215686275,0) [0|25700] "Unit_PerCent" BMS_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,LEH_MQB
 SG_ OBD_Abs_Pedal_Pos : 40|8@1+ (0.39215686275,0) [0|100] "Unit_PerCent" BMS_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,LEH_MQB
 SG_ OBD_Kaltstart_Denominator : 59|1@1+ (1,0) [0|1] "" BMS_MQB,LEH_MQB
 SG_ OBD_Minimum_Trip : 60|1@1+ (1,0) [0|1] "" BMS_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,LEH_MQB
 SG_ OBD_Driving_Cycle : 61|1@1+ (1,0) [0|1] "" BMS_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,LEH_MQB
 SG_ OBD_Warm_Up_Cycle : 62|1@1+ (1,0) [0|1] "" BMS_MQB,Gateway_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,LEH_MQB
 SG_ OBD_Normed_Trip : 63|1@1+ (1,0) [0|1] "" BMS_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,LEH_MQB

BO_ 1630 OBD_Tankgeber_01: 8 Gateway_MQB
 SG_ OBD_TG_F_Status_1 : 0|4@1+ (1,0) [0|15] "" Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ OBD_TG_F_Status_2 : 4|4@1+ (1,0) [0|15] "" Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ OBD_TG_F_Status_3 : 8|4@1+ (1,0) [0|15] "" Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ OBD_TG_F_Status_4 : 12|4@1+ (1,0) [0|15] "" Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ OBD_TG_Sens_Rohwert_1 : 16|12@1+ (0.5,0) [0|2047.5] "Unit_Ohm" Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ OBD_TG_Sens_Rohwert_2 : 28|12@1+ (0.5,0) [0|2047.5] "Unit_Ohm" Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ OBD_TG_Sens_Rohwert_3 : 40|12@1+ (0.5,0) [0|2047.5] "Unit_Ohm" Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ OBD_TG_Sens_Rohwert_4 : 52|12@1+ (0.5,0) [0|2047.5] "Unit_Ohm" Motor_Hybrid_MQB,Motor_Otto_MQB

BO_ 1437 Remotestart_FFB: 8 Gateway_MQB
 SG_ RSF_Tastencode_1 : 0|8@1+ (1,0) [0|255] "" Motor_Diesel_MQB,Motor_Otto_MQB
 SG_ RSF_Tastencode_2 : 8|8@1+ (1,0) [0|255] "" Motor_Diesel_MQB,Motor_Otto_MQB
 SG_ RSF_Tastencode_Maske : 16|8@1+ (1,0) [0|255] "" Motor_Diesel_MQB,Motor_Otto_MQB

BO_ 984 RGS_VL_01: 8 Airbag_MQB
 SG_ RGS_VL_Texte : 12|2@1+ (1,0) [0|3] "" Gateway_MQB
 SG_ RGS_VL_Charisma_FahrPr : 14|4@1+ (1,0) [0|15] "" Gateway_MQB
 SG_ RGS_VL_Charisma_Status : 18|2@1+ (1,0) [0|3] "" Gateway_MQB
 SG_ RGS_VL_aktiv : 21|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ RGS_VL_PC_Aktuator_Sitz : 25|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ RGS_VL_PC_Aktuator_Schiebedach : 26|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ RGS_VL_PC_Aktuator_Fenster : 27|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ RGS_VL_PC_Aktuator_Warnblinken : 28|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ RGS_VL_Precrash_Basis : 32|8@1+ (1,0) [0|255] "" Gateway_MQB
 SG_ RGS_VL_Precrash_Front : 40|8@1+ (1,0) [0|255] "" Gateway_MQB
 SG_ RGS_VL_Precrash_Rear : 48|8@1+ (1,0) [0|255] "" Gateway_MQB

BO_ 1528 SAK_01: 8 SAK_MQB
 SG_ SAK_Charisma_FahrPr : 16|4@1+ (1,0) [0|15] "" Gateway_MQB
 SG_ SAK_Charisma_Status : 20|2@1+ (1,0) [0|3] "" Gateway_MQB

BO_ 1313 STH_01: 8 Gateway_MQB
 SG_ STH_Funk_ein : 0|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ STH_Funk_aus : 1|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ STH_Zusatzheizung : 2|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ STH_LED : 3|1@1+ (1,0) [0|1] "" Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ STH_Pumpe_ein : 4|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ STH_Geblaese : 5|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ STH_EKP_Anst : 6|1@1+ (1,0) [0|1] "" Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ STH_Start_folgt : 7|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ STH_Ventiloeffnungszeit : 8|6@1+ (1,0) [0|63] "Unit_Minut" Vector__XXX
 SG_ STH_Ventil_Status : 14|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ STH_Waermeeintrag : 16|6@1+ (1,0) [0|63] "" Vector__XXX
 SG_ STH_KVS : 24|13@1+ (1,0) [0|8191] "Unit_MilliLiter" Vector__XXX
 SG_ STH_Fehlerstatus : 37|3@1+ (1,0) [0|7] "" Vector__XXX
 SG_ STH_Heizleistung : 40|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ STH_Wassertemp : 48|8@1+ (0.75,-40) [-40|142.25] "Unit_DegreCelsi" Vector__XXX
 SG_ STH_Motorvorwaermung : 59|1@1+ (1,0) [0|1] "" Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ STH_Servicemode : 60|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ STH_war_aktiv : 61|1@1+ (1,0) [0|1] "" Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ STH_KVS_Ueberlauf : 62|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ STH_KD_Fehler : 63|1@1+ (1,0) [0|1] "" Vector__XXX

BO_ 1172 STS_01: 8 Gateway_MQB
 SG_ STS_01_CRC : 0|8@1+ (1,0) [0|255] "" Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ STS_01_BZ : 8|4@1+ (1,0) [0|15] "" Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ STS_Car_not_under_theft : 12|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ STS_Car_under_theft : 13|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ STS_Anlassersperre : 15|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ STS_Typencodierung : 16|5@1+ (1,0) [0|31] "" Vector__XXX
 SG_ STS_LIN_aktiv : 23|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ STS_Standlicht : 24|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ STS_Fahrlicht : 25|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ STS_Alarm_still : 26|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ STS_Texte : 27|4@1+ (1,0) [0|15] "" Vector__XXX
 SG_ STS_Laderelais : 38|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ STS_Summer : 48|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ STS_Alarm_Blinker : 50|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ STS_Notstart : 51|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ STS_Signalhorn : 55|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ STS_Leerlaufschaltung : 56|1@1+ (1,0) [0|1] "" Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB

BO_ 1413 Systeminfo_01: 8 Gateway_MQB
 SG_ SI_Sammel_SG_Fehler : 0|6@1+ (1,0) [0|60] "" Vector__XXX
 SG_ SI_Rollenmode : 6|2@1+ (1,0) [0|3] "" Vector__XXX
 SG_ SI_QRS_Mode : 8|1@1+ (1,0) [0|1] "" Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ SI_T_Mode : 9|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ SI_NWDF : 10|1@1+ (1,0) [0|1] "" SAK_MQB
 SG_ SI_NWDF_gueltig : 11|1@1+ (1,0) [0|1] "" SAK_MQB
 SG_ SI_Sammelfehler : 12|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ GW_KD_Fehler : 13|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ SI_BUS_01 : 16|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ SI_BUS_02 : 17|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ SI_BUS_03 : 18|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ SI_BUS_04 : 19|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ SI_BUS_05 : 20|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ SI_BUS_06 : 21|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ SI_BUS_07 : 22|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ SI_BUS_08 : 23|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ SI_BUS_09 : 24|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ SI_BUS_10 : 25|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ SI_BUS_11 : 26|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ SI_BUS_12 : 27|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ SI_BUS_13 : 28|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ SI_BUS_14 : 29|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ SI_BUS_15 : 30|1@1+ (1,0) [0|1] "" Vector__XXX

BO_ 288 TSK_06: 8 Motor_Diesel_MQB
 SG_ CHECKSUM : 0|8@1+ (1,0) [0|255] "" Gateway_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB
 SG_ COUNTER : 8|4@1+ (1,0) [0|15] "" Gateway_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB
 SG_ TSK_Radbremsmom : 12|12@1+ (8,0) [0|32760] "Unit_NewtoMeter" Gateway_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB
 SG_ TSK_Status : 24|3@1+ (1,0) [0|7] "" Gateway_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB
 SG_ TSK_v_Begrenzung_aktiv : 27|1@1+ (1,0) [0|1] "" Gateway_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB
 SG_ TSK_Standby_Anf_ESP : 28|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ TSK_Freig_WU : 29|1@1+ (1.0,0.0) [0.0|1] ""  Gateway_MQB
 SG_ TSK_Freig_Verzoeg_Anf : 30|1@1+ (1,0) [0|1] "" Gateway_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB
 SG_ TSK_Limiter_ausgewaehlt : 31|1@1+ (1,0) [0|1] "" Gateway_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB
 SG_ TSK_Wunsch_Uebersetz : 32|10@1+ (0.0245,0) [0.0245|25.0635] ""  Gateway_MQB
 SG_ TSK_Hauptschalter_GRA_ACC : 42|2@1+ (1.0,0.0) [0.0|3] ""  Gateway_MQB
 SG_ TSK_SRBM_Anf_ASIL : 44|3@1+ (1.0,0.0) [0.0|7] ""  Gateway_MQB
 SG_ TSK_ax_Getriebe_02 : 48|9@1+ (0.024,-2.016) [-2.016|10.224] "Unit_MeterPerSeconSquar" Gateway_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB
 SG_ TSK_Zwangszusch_ESP : 57|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ TSK_zul_Regelabw : 58|6@1+ (0.024,0) [0|1.512] "Unit_MeterPerSeconSquar" Gateway_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB

BO_ 798 TSK_07: 8 Motor_Diesel_MQB
 SG_ TSK_07_CRC : 0|8@1+ (1,0) [0|255] ""  Gateway_MQB,Getriebe_AQ
 SG_ TSK_07_BZ : 8|4@1+ (1,0) [0|15] ""  Gateway_MQB,Getriebe_AQ
 SG_ TSK_Wunschgeschw : 12|10@1+ (0.32,0) [0.00|326.72] "Unit_KiloMeterPerHour"  Gateway_MQB,Getriebe_AQ
 SG_ TSK_Texte : 40|5@1+ (1.0,0.0) [0.0|31] ""  Gateway_MQB
 SG_ TSK_Akustik : 45|3@1+ (1.0,0.0) [0.0|7] ""  Gateway_MQB
 SG_ TSK_Texte_Primaeranz : 48|5@1+ (1.0,0.0) [0.0|31] ""  Gateway_MQB
 SG_ TSK_Limiter_Fahrerinfo : 53|2@1+ (1.0,0.0) [0.0|3] ""  Gateway_MQB
 SG_ TSK_Limiter_Anzeige : 55|1@1+ (1.0,0.0) [0.0|1] ""  Gateway_MQB
 SG_ TSK_Fahrzeugstatus_GRA : 56|1@1+ (1.0,0.0) [0.0|1] ""  Gateway_MQB
 SG_ TSK_Fahrzeugstatus_Limiter : 57|1@1+ (1.0,0.0) [0.0|1] ""  Gateway_MQB
 SG_ MO_Motorlaufwarnung : 58|1@1+ (1.0,0.0) [0.0|1] ""  Gateway_MQB
 SG_ TSK_Status_Anzeige : 61|3@1+ (1.0,0.0) [0.0|7] ""  Gateway_MQB

BO_ 346 TSK_08: 8 Motor_Diesel_MQB
 SG_ TSK_08_CRC : 0|8@1+ (1,0) [0|255] ""  Frontradar
 SG_ TSK_08_BZ : 8|4@1+ (1,0) [0|15] ""  Frontradar
 SG_ MO_Anforderung_HMS : 12|3@1+ (1,0) [0|7] "" Vector__XXX
 SG_ TSK_Status_EA : 32|3@1+ (1,0) [0|7] "" Vector__XXX
 SG_ TSK_vMax_Fahrerassistenz : 40|9@1+ (1,0) [0|510] ""  Frontradar
 SG_ TSK_Einheit_vMax_Fahrerassistenz : 49|1@1+ (1,0) [0|1] ""  Frontradar
 SG_ TSK_Status_PLA : 50|3@1+ (1,0) [0|7] "" Vector__XXX
 SG_ TSK_aktives_System : 53|3@1+ (1,0) [0|7] "" Vector__XXX
 SG_ TSK_erhoehter_Fahrwiderstand : 56|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ TSK_Anf_Antriebsmoment : 57|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ TSK_Status_ARA : 58|3@1+ (1,0) [0|7] "" Vector__XXX
 SG_ TSK_Status_IPA : 61|3@1+ (1,0) [0|7] "" Vector__XXX

BO_ 1716 VIN_01: 8 Gateway_MQB
 SG_ VIN_01_MUX M : 0|2@1+ (1,0) [0|3] ""  Airbag_MQB
 SG_ KS_Geheimnis_1 m0 : 8|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ VIN_4 m1 : 8|8@1+ (1,0) [0|255] ""  Airbag_MQB
 SG_ VIN_11 m2 : 8|8@1+ (1,0) [0|255] ""  Airbag_MQB
 SG_ KS_Geheimnis_2 m0 : 16|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ VIN_5 m1 : 16|8@1+ (1,0) [0|255] ""  Airbag_MQB
 SG_ VIN_12 m2 : 16|8@1+ (1,0) [0|255] ""  Airbag_MQB
 SG_ KS_Geheimnis_3 m0 : 24|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ VIN_6 m1 : 24|8@1+ (1,0) [0|255] ""  Airbag_MQB
 SG_ VIN_13 m2 : 24|8@1+ (1,0) [0|255] ""  Airbag_MQB
 SG_ KS_Geheimnis_4 m0 : 32|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ VIN_7 m1 : 32|8@1+ (1,0) [0|255] ""  Airbag_MQB
 SG_ VIN_14 m2 : 32|8@1+ (1,0) [0|255] ""  Airbag_MQB
 SG_ VIN_1 m0 : 40|8@1+ (1,0) [0|255] ""  Airbag_MQB
 SG_ VIN_8 m1 : 40|8@1+ (1,0) [0|255] ""  Airbag_MQB
 SG_ VIN_15 m2 : 40|8@1+ (1,0) [0|255] ""  Airbag_MQB
 SG_ VIN_2 m0 : 48|8@1+ (1,0) [0|255] ""  Airbag_MQB
 SG_ VIN_9 m1 : 48|8@1+ (1,0) [0|255] ""  Airbag_MQB
 SG_ VIN_16 m2 : 48|8@1+ (1,0) [0|255] ""  Airbag_MQB
 SG_ VIN_3 m0 : 56|8@1+ (1,0) [0|255] ""  Airbag_MQB
 SG_ VIN_10 m1 : 56|8@1+ (1,0) [0|255] ""  Airbag_MQB
 SG_ VIN_17 m2 : 56|8@1+ (1,0) [0|255] ""  Airbag_MQB

BO_ 175 Waehlhebel_03: 4 Waehlhebel_MQB
 SG_ WH_Status_Sperre : 0|3@1+ (1,0) [0|7] "" Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB
 SG_ WH_Initialisierung : 3|1@1+ (1,0) [0|1] "" Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB
 SG_ WH_SensorPos_roh : 4|4@1+ (1,0) [0|15] "" Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB
 SG_ WH_03_BZ : 8|4@1+ (1,0) [0|15] "" Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB
 SG_ WH_SensorPos_roh_inv : 12|4@1+ (1,0) [0|15] "" Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB
 SG_ WH_Testergebnis : 16|8@1+ (1,0) [0|255] "" Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB
 SG_ WH_Test_Aktiv : 24|1@1+ (1,0) [0|1] "" Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB
 SG_ WH_Status : 25|7@1+ (1,0) [0|127] "" Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB

BO_ 916 WBA_03: 8 Getriebe_DQ_Hybrid_MQB
 SG_ WBA_03_CRC : 0|8@1+ (1,0) [0|255] "" Gateway_MQB
 SG_ WBA_03_BZ : 8|4@1+ (1,0) [0|15] "" Gateway_MQB
 SG_ WBA_Fahrstufe_02 : 12|4@1+ (1,0) [0|15] "" Gateway_MQB
 SG_ WBA_ZielFahrstufe : 16|4@1+ (1,0) [0|15] "" Gateway_MQB
 SG_ WBA_GE_Warnung_02 : 20|4@1+ (1,0) [0|15] "" Gateway_MQB
 SG_ WBA_eing_Gang_02 : 24|4@1+ (1,0) [0|15] "" Gateway_MQB,Motor_Diesel_MQB,Motor_Otto_MQB
 SG_ WBA_GE_Texte : 28|3@1+ (1,0) [0|7] "" Gateway_MQB
 SG_ WBA_Segeln_aktiv : 31|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ WBA_Schaltschema : 32|5@1+ (1,0) [0|31] "" Gateway_MQB

BO_ 1602 WIV_01: 8 Motor_Diesel_MQB
 SG_ WIV_Verschleissindex : 0|16@1+ (2e-8,0) [0|0.00131068] "" Gateway_MQB
 SG_ WIV_Russindex : 16|16@1+ (2e-8,0) [0|0.00131068] "" Gateway_MQB
 SG_ WIV_t_min : 32|6@1+ (1,0) [0|63] "Unit_Month" Gateway_MQB
 SG_ WIV_t_max : 40|6@1+ (1,0) [0|63] "Unit_Month" Gateway_MQB
 SG_ WIV_W_min : 48|7@1+ (1000,0) [0|127000] "Unit_KiloMeter" Gateway_MQB
 SG_ WIV_W_max : 56|7@1+ (1000,0) [0|127000] "Unit_KiloMeter" Gateway_MQB

BO_ 294 HCA_01: 8 Frontsensorik
 SG_ CHECKSUM : 0|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ COUNTER : 8|4@1+ (1,0) [0|15] "" Vector__XXX
 SG_ HCA_01_Vib_Freq : 12|4@1+ (1,15) [15|30] "Unit_Hertz" Vector__XXX
 SG_ HCA_01_LM_Offset : 16|9@1+ (1,0) [0|511] "Unit_centiNewtoMeter" Vector__XXX
 SG_ EA_ACC_Sollstatus : 25|2@1+ (1,0) [0|3] ""  Frontradar
 SG_ EA_Ruckprofil : 27|3@1+ (1,0) [0|7] "" Vector__XXX
 SG_ HCA_01_Sendestatus : 30|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ HCA_01_LM_OffSign : 31|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ HCA_01_Status_HCA : 32|4@1+ (1,0) [0|15] "" Vector__XXX
 SG_ HCA_01_Vib_Amp : 36|4@1+ (0.2,0) [0|3] "Unit_NewtoMeter" Vector__XXX
 SG_ EA_Ruckfreigabe : 40|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ EA_ACC_Wunschgeschwindigkeit : 41|10@1+ (0.32,0) [0|327.04] "Unit_KiloMeterPerHour"  Frontradar

BO_ 810 LH_EPS_01: 8 XXX
 SG_ CHECKSUM : 0|8@1+ (1,0) [0|255] "" XXX
 SG_ COUNTER : 8|4@1+ (1,0) [0|15] "" XXX
 SG_ EPS_SpannungsAnf : 12|2@1+ (1.0,0.0) [0.0|3] "" XXX
 SG_ EPS_Endanschlag : 14|2@1+ (1.0,0.0) [0.0|3] "" XXX
 SG_ EPS_Akustiksignal : 16|1@1+ (1.0,0.0) [0.0|1] "" XXX
 SG_ EPS_Fehlerlampe : 17|1@1+ (1.0,0.0) [0.0|1] "" XXX
 SG_ EPS_Warnungen : 19|3@1+ (1.0,0.0) [0.0|7] "" XXX
 SG_ EPS_PLA_Abbruch : 22|4@1+ (1,0) [0|15] "" XXX
 SG_ EPS_PLA_Fehler : 26|4@1+ (1,0) [0|15] "" XXX
 SG_ EPS_PLA_Status : 30|4@1+ (1.0,0.0) [0.0|15] "" XXX
 SG_ EPS_Charisma_FahrPr : 34|4@1+ (1.0,0.0) [0.0|15] "" XXX
 SG_ EPS_Charisma_Status : 38|2@1+ (1.0,0.0) [0.0|3] "" XXX
 SG_ EPS_Lenkerposition : 41|2@1+ (1.0,0.0) [0.0|3] "" XXX
 SG_ EPS_Anf_KL : 43|1@1+ (1.0,0.0) [0.0|1] "" XXX
 SG_ EPS_ARA_Status : 44|4@1+ (1.0,0.0) [0.0|15] "" XXX

BO_ 159 LH_EPS_03: 8 XXX
 SG_ CHECKSUM : 0|8@1+ (1,0) [0|255] ""  XXX
 SG_ COUNTER : 8|4@1+ (1,0) [0|15] ""  XXX
 SG_ EPS_DSR_Status : 12|4@1+ (1,0) [0|15] ""  XXX
 SG_ EPS_Berechneter_LW : 16|12@1+ (0.15,0) [0|613.95] "Unit_DegreOfArc"  XXX
 SG_ EPS_BLW_QBit : 30|1@1+ (1,0) [0|1] ""  XXX
 SG_ EPS_VZ_BLW : 31|1@1+ (1,0) [0|1] ""  XXX
 SG_ EPS_HCA_Status : 32|4@1+ (1,0) [0|15] ""  XXX
 SG_ EPS_Lenkmoment : 40|10@1+ (1,0) [0|8] "Unit_centiNewtoMeter"  XXX
 SG_ EPS_Lenkmoment_QBit : 54|1@1+ (1,0) [0|1] ""  XXX
 SG_ EPS_VZ_Lenkmoment : 55|1@1+ (1,0) [0|1] ""  XXX
 SG_ EPS_Lenkungstyp : 60|4@1+ (1,0) [0|15] ""  XXX

BO_ 286 ESP_08: 8 Gateway_MQB
 SG_ ESP_08_CRC : 0|8@1+ (1,0) [0|255] ""  XXX
 SG_ ESP_08_BZ : 8|4@1+ (1,0) [0|15] ""  XXX
 SG_ ESP_ANB_CM_Rueckk_Umsetz : 12|1@1+ (1.0,0.0) [0.0|1] ""  XXX
 SG_ ESP_Konsistenz_ACC_Botschaft : 13|1@1+ (1.0,0.0) [0.0|1] ""  XXX
 SG_ ESP_Stillstandsphase_erschoepft : 14|1@1+ (1.0,0.0) [0.0|1] ""  XXX
 SG_ ESP_ZT_Rueckk_Umsetz : 15|1@1+ (1.0,0.0) [0.0|1] ""  XXX
 SG_ ESP_Tuerkontakt_Fahrertuer : 16|1@1+ (1.0,0.0) [0.0|1] ""  XXX
 SG_ ESP_Abrutschen_Stillstand : 18|1@1+ (1.0,0.0) [0.0|1] ""  XXX
 SG_ ESP_Fahrer_tritt_ZBR_Schw : 19|1@1+ (1.0,0.0) [0.0|1] ""  XXX
 SG_ ESP_QBit_v_ref : 41|1@1+ (1.0,0.0) [0.0|1] ""  XXX
 SG_ ESP_v_ref_Fahrtrichtung : 42|2@1+ (1.0,0.0) [0.0|3] ""  XXX
 SG_ ESC_Bremsdruckgradient : 44|8@1+ (10,0) [0|2500] "Unit_BarPerSecon"  XXX
 SG_ ESP_v_ref : 52|12@1+ (0.125,0) [0.000|511.500] "Unit_KiloMeterPerHour"  XXX

BO_ 919 LDW_02: 8 XXX
 SG_ LDW_Gong : 12|2@1+ (1,0) [0|3] ""  XXX
 SG_ LDW_SW_Warnung_links : 14|1@1+ (1,0) [0|1] ""  XXX
 SG_ LDW_SW_Warnung_rechts : 15|1@1+ (1,0) [0|1] ""  XXX
 SG_ LDW_Texte : 16|4@1+ (1,0) [0|15] ""  XXX
 SG_ LDW_Seite_DLCTLC : 20|1@1+ (1,0) [0|1] ""  XXX
 SG_ LDW_Lernmodus : 21|3@1+ (1,0) [0|7] ""  XXX
 SG_ LDW_Anlaufsp_VLR : 24|4@1+ (1,0) [0|15] ""  XXX
 SG_ LDW_Vib_Amp_VLR : 28|4@1+ (1,0) [0|15] ""  XXX
 SG_ LDW_Anlaufzeit_VLR : 32|4@1+ (1,0) [0|15] ""  XXX
 SG_ LDW_Lernmodus_rechts : 36|2@1+ (1,0) [0|3] ""  XXX
 SG_ LDW_Lernmodus_links : 38|2@1+ (1,0) [0|3] ""  XXX
 SG_ LDW_DLC : 40|8@1+ (0.01,-1.25) [-1.25|1.25] "Unit_Meter"  XXX
 SG_ LDW_TLC : 48|5@1+ (0.1,0) [0|3] "Unit_Secon"  XXX
 SG_ LDW_Warnung_links : 56|1@1+ (1,0) [0|1] ""  XXX
 SG_ LDW_Warnung_rechts : 57|1@1+ (1,0) [0|1] ""  XXX
 SG_ LDW_Codierinfo_fuer_VLR : 58|2@1+ (1,0) [0|3] ""  XXX
 SG_ LDW_Frontscheibenheizung_aktiv : 60|1@1+ (1,0) [0|1] ""  XXX
 SG_ LDW_Status_LED_gelb : 61|1@1+ (1,0) [0|1] ""  XXX
 SG_ LDW_Status_LED_gruen : 62|1@1+ (1,0) [0|1] ""  XXX
 SG_ LDW_KD_Fehler : 63|1@1+ (1,0) [0|1] ""  XXX

BO_ 780 ACC_02: 8 XXX
 SG_ CHECKSUM : 0|8@1+ (1,0) [0|255] ""  XXX
 SG_ COUNTER : 8|4@1+ (1,0) [0|15] ""  XXX
 SG_ ACC_Wunschgeschw_02 : 12|10@1+ (0.32,0) [0|327.04] "Unit_KiloMeterPerHour"  XXX
 SG_ ACC_Status_Prim_Anz : 22|2@1+ (1,0) [0|3] ""  XXX
 SG_ ACC_Abstandsindex : 24|10@1+ (1,0) [1|1021] ""  XXX
 SG_ ACC_Akustik_02 : 34|2@1+ (1,0) [0|3] ""  XXX
 SG_ ACC_Warnung_Verkehrszeichen_1 : 36|1@1+ (1,0) [0|1] ""  XXX
 SG_ ACC_Gesetzte_Zeitluecke : 37|3@1+ (1,0) [0|7] ""  XXX
 SG_ ACC_Optischer_Fahrerhinweis : 40|1@1+ (1,0) [0|1] ""  XXX
 SG_ ACC_Typ_Tachokranz : 41|1@1+ (1,0) [0|1] ""  XXX
 SG_ ACC_Anzeige_Zeitluecke : 42|1@1+ (1,0) [0|1] ""  XXX
 SG_ ACC_Tachokranz : 43|1@1+ (1,0) [0|1] ""  XXX
 SG_ ACC_Display_Prio : 44|2@1+ (1,0) [0|3] ""  XXX
 SG_ ACC_Relevantes_Objekt : 46|2@1+ (1,0) [0|3] ""  XXX
 SG_ ACC_Texte_Primaeranz : 48|7@1+ (1,0) [0|127] ""  XXX
 SG_ ACC_Wunschgeschw_erreicht : 55|1@1+ (1,0) [0|1] ""  XXX
 SG_ ACC_Typ_Tachokranz_unten : 60|1@1+ (1,0) [0|1] ""  XXX
 SG_ ACC_Status_Anzeige : 61|3@1+ (1,0) [0|7] ""  XXX

BO_ 302 ACC_07: 8 XXX
 SG_ CHECKSUM : 0|8@1+ (1,0) [0|255] ""  XXX
 SG_ COUNTER : 8|4@1+ (1,0) [0|15] ""  XXX
 SG_ ACC_Anhalteweg : 12|11@1+ (0.01,0) [0|20.45] "Unit_Meter"  XXX
 SG_ ACC_Anhalten : 23|1@1+ (1,0) [0|1] ""  XXX
 SG_ ACC_Boost_Anf : 24|1@1+ (1,0) [0|1] ""  XXX
 SG_ ACC_Freilauf_Anf : 25|1@1+ (1,0) [0|1] ""  XXX
 SG_ ACC_Freilauf_Info : 26|2@1+ (1,0) [0|3] ""  XXX
 SG_ ACC_Anforderung_HMS : 28|3@1+ (1,0) [0|7] ""  XXX
 SG_ ACC_Anfahren : 31|1@1+ (1,0) [0|1] ""  XXX
 SG_ ACC_Folgebeschl : 32|8@1+ (0.03,-4.6) [-4.6|2.99] "Unit_MeterPerSeconSquar"  XXX
 SG_ ACC_Sollbeschleunigung_02 : 53|11@1+ (0.005,-7.22) [-7.22|3.005] "Unit_MeterPerSeconSquar"  XXX

BO_ 264 Fahrwerk_01: 8 XXX
 SG_ Fahrwerk_01_BZ : 8|4@1+ (1,0) [0|15] "" XXX
 SG_ Fahrwerk_01_CRC : 0|8@1+ (1,0) [0|255] "" XXX

BO_ 695 RCTA_01: 8 XXX
 SG_ RCTA_01_BZ : 8|4@1+ (1,0) [0|15] "" XXX
 SG_ RCTA_01_CRC : 0|8@1+ (1,0) [0|255] "" XXX

BO_ 783 SWA_01: 8 Gateway_MQB
 SG_ CHECKSUM : 0|8@1+ (1,0) [0|255] ""  XXX
 SG_ COUNTER : 8|4@1+ (1,0) [0|15] ""  XXX
 SG_ SWA_Anzeigen : 12|4@1+ (1,0) [0|15] ""  XXX
 SG_ SWA_Blindheit_erkannt : 16|1@1+ (1,0) [0|1] ""  XXX
 SG_ SWA_rel_Nichtverf : 17|1@1+ (1,0) [0|1] ""  XXX
 SG_ SWA_rel_Fehler : 18|1@1+ (1,0) [0|1] ""  XXX
 SG_ SWA_Sta_aktiv : 19|1@1+ (1,0) [0|1] ""  XXX
 SG_ SWA_Sta_passiv : 20|1@1+ (1,0) [0|1] ""  XXX
 SG_ SWA_FT_RueckLED : 21|1@1+ (1,0) [0|1] ""  XXX
 SG_ ASW_Status : 22|2@1+ (1,0) [0|3] ""  XXX
 SG_ SWA_Standziele_li : 24|1@1+ (1,0) [0|1] ""  XXX
 SG_ SWA_Kolonne_li : 25|1@1+ (1,0) [0|1] ""  XXX
 SG_ SWA_Infostufe_SWA_li : 26|1@1+ (1,0) [0|1] ""  XXX
 SG_ SWA_Warnung_SWA_li : 27|1@1+ (1,0) [0|1] ""  XXX
 SG_ ASW_Warnung_FS : 28|1@1+ (1,0) [0|1] ""  XXX
 SG_ ASW_Warnung_BFS : 29|1@1+ (1,0) [0|1] ""  XXX
 SG_ ASW_Kombitexte : 30|3@1+ (1,0) [0|7] ""  XXX
 SG_ SWA_Kolonne_mi : 33|1@1+ (1,0) [0|1] ""  XXX
 SG_ SWA_Standziele_re : 40|1@1+ (1,0) [0|1] ""  XXX
 SG_ SWA_Kolonne_re : 41|1@1+ (1,0) [0|1] ""  XXX
 SG_ SWA_Infostufe_SWA_re : 42|1@1+ (1,0) [0|1] ""  XXX
 SG_ SWA_Warnung_SWA_re : 43|1@1+ (1,0) [0|1] ""  XXX
 SG_ HRE_Anzeigetexte : 44|4@1+ (1,0) [0|15] ""  XXX
 SG_ SWA_Gischtzaehler : 48|7@1+ (1,0) [0|100] "Unit_PerCent"  XXX
 SG_ Heckradar_Kombitexte : 56|5@1+ (1,0) [0|31] ""  XXX
 SG_ RCTA_Kombitexte : 61|3@1+ (1,0) [0|7] ""  XXX

BO_ 804 ACC_04: 8 XXX
 SG_ CHECKSUM : 0|8@1+ (1,0) [0|255] ""  XXX
 SG_ COUNTER : 8|4@1+ (1,0) [0|15] ""  XXX
 SG_ ACC_Texte_Sekundaeranz : 12|4@1+ (1,0) [0|15] ""  XXX
 SG_ ACC_Texte_Zusatzanz : 16|6@1+ (1,0) [0|63] ""  XXX
 SG_ ACC_Status_Zusatzanz : 22|5@1+ (1,0) [0|31] ""  XXX
 SG_ ACC_Texte : 27|5@1+ (1,0) [0|31] ""  XXX
 SG_ ACC_Texte_braking_guard : 32|3@1+ (1,0) [0|7] ""  XXX
 SG_ ACC_Warnhinweis : 35|1@1+ (1,0) [0|1] ""  XXX
 SG_ ACC_Zeitluecke_Abstandswarner : 36|6@1+ (0.1,0) [0|6] "Unit_Secon"  XXX
 SG_ ACC_Abstand_Abstandswarner : 42|9@1+ (1,0) [0|508] ""  XXX
 SG_ ACC_Tempolimit : 51|5@1+ (1,0) [0|31] ""  XXX
 SG_ ACC_Charisma_FahrPr : 56|4@1+ (1,0) [0|15] ""  XXX
 SG_ ACC_Charisma_Status : 60|2@1+ (1,0) [0|3] ""  XXX
 SG_ ACC_Texte_Abstandswarner : 62|2@1+ (1,0) [0|3] ""  XXX

BO_ 917 LWR_AFS_01: 8 XXX

BO_ 991 Gateway_76: 8 XXX

BO_ 997 TSG_FT_02: 8 XXX
 SG_ TSG_FT_02_BZ : 8|4@1+ (1,0) [0|15] "" XXX
 SG_ TSG_FT_02_CRC : 0|8@1+ (1,0) [0|255] "" XXX

BO_ 1175 Parkhilfe_01: 8 XXX

BO_ 427 ESP_33: 8 Gateway_MQB
 SG_ CHECKSUM : 0|8@1+ (1,0) [0|255] ""  Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ COUNTER : 8|4@1+ (1,0) [0|15] ""  Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ ESC_AHW_aktiv : 12|3@1+ (1,0) [0|7] ""  Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ ESC_AHW_nicht_verfuegbar : 15|1@1+ (1,0) [0|1] ""  Vector__XXX
 SG_ ESC_ANB_CM_aktiv : 16|2@1+ (1,0) [0|3] ""  Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ ESC_ANB_CM_nicht_verfuegbar : 18|1@1+ (1,0) [0|1] ""  Vector__XXX
 SG_ ESC_Warnruck_aktiv : 19|4@1+ (1,0) [0|15] ""  Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ ESC_Warnruck_nicht_verfuegbar : 23|1@1+ (1,0) [0|1] ""  Vector__XXX
 SG_ ESC_Prefill_aktiv : 24|1@1+ (1,0) [0|1] ""  Vector__XXX
 SG_ ESC_Prefill_nicht_verfuegbar : 25|1@1+ (1,0) [0|1] ""  Vector__XXX
 SG_ ESC_HBA_aktiv : 26|1@1+ (1,0) [0|1] ""  Airbag_MQB
 SG_ ESC_HBA_nicht_verfuegbar : 27|1@1+ (1,0) [0|1] ""  Airbag_MQB
 SG_ ESC_TSK_SRBM_Anf : 28|1@1+ (1,0) [0|1] ""  Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ ESC_TSK_SRBM_nicht_verfuegbar : 29|1@1+ (1,0) [0|1] ""  Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ ESC_Verz_Reg_aktiv : 30|4@1+ (1,0) [0|15] ""  Airbag_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ ESC_Verz_Reg_nicht_verfuegbar : 34|1@1+ (1,0) [0|1] ""  Airbag_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ ESC_Verz_Reg_TB_nicht_verfuegbar : 35|1@1+ (1,0) [0|1] ""  Vector__XXX
 SG_ ESC_Verz_Reg_ZB_nicht_verfuegbar : 36|1@1+ (1,0) [0|1] ""  Vector__XXX
 SG_ ESC_Bremslicht_unplausibel : 37|1@1+ (1,0) [0|1] ""  Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ ESC_Konsistenz_ACC : 38|1@1+ (1,0) [0|1] ""  Vector__XXX
 SG_ ESC_Konsistenz_AWV : 39|1@1+ (1,0) [0|1] ""  Vector__XXX
 SG_ ESC_Konsistenz_ARA : 40|1@1+ (1,0) [0|1] ""  Vector__XXX
 SG_ ESC_Konsistenz_IPA : 41|1@1+ (1,0) [0|1] ""  Vector__XXX
 SG_ ESC_Konsistenz_FCW : 42|1@1+ (1,0) [0|1] ""  Vector__XXX
 SG_ ESC_Konsistenz_NV : 43|1@1+ (1,0) [0|1] ""  Vector__XXX
 SG_ ESC_Konsistenz_RCTA : 44|1@1+ (1,0) [0|1] ""  Vector__XXX
 SG_ ESC_Konsistenz_TSK : 45|1@1+ (1,0) [0|1] ""  Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ ESC_Konsistenz_vFGS : 46|1@1+ (1,0) [0|1] ""  Vector__XXX
 SG_ ESC_Konsistenz_STA : 47|1@1+ (1,0) [0|1] ""  Vector__XXX
 SG_ ESC_Fahrer_Bremsdruck_bestimmend : 48|1@1+ (1,0) [0|1] ""  Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ ESC_Konsistenz_EA : 49|1@1+ (1,0) [0|1] ""  Vector__XXX
 SG_ ESC_Konsistenz_BFF : 50|1@1+ (1,0) [0|1] ""  Vector__XXX
 SG_ ESC_Konsistenz_MKB : 51|1@1+ (1,0) [0|1] ""  Airbag_MQB
 SG_ ESC_Verz_ASIL_Verfuegbarkeit : 52|3@1+ (1,0) [0|7] ""  Airbag_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ ESC_Pumpenanlauf_aktiv : 55|1@1+ (1,0) [0|1] ""  Vector__XXX
 SG_ ESC_Konsistenz_AGW : 56|1@1+ (1,0) [0|1] ""  Vector__XXX
 SG_ ESC_Konsistenz_KAS : 57|1@1+ (1,0) [0|1] ""  Vector__XXX
 SG_ ESC_Konsistenz_PCF : 58|1@1+ (1,0) [0|1] ""  Vector__XXX
 SG_ ESC_Konsistenz_FAS_VK : 59|1@1+ (1,0) [0|1] ""  Vector__XXX
 SG_ ESC_Verz_Begrenzung : 60|1@1+ (1,0) [0|1] ""  Vector__XXX
 SG_ ESC_Konsistenz_AWA : 61|1@1+ (1,0) [0|1] ""  Vector__XXX

BO_ 418 ESP_15: 8 XXX
 SG_ ESP_15_CRC : 0|8@1+ (1,0) [0|255] "" XXX
 SG_ ESP_15_BZ : 8|4@1+ (1,0) [0|15] "" XXX

BO_ 1122 PSD_04: 8 XXX
 SG_ PSD_Object_Index : 0|6@1+ (1,0) [0|63] "" XXX

BO_ 1123 PSD_05: 8 XXX
 SG_ PSD_Current_Route_Index : 0|6@1+ (1,0) [0|63] "" XXX
 SG_ Route_Distance_Remaining : 8|5@1+ (1,0) [0|31] "" XXX

BO_ 1124 PSD_06: 8 XXX

BO_ 988 Gateway_73: 8 XXX
 SG_ UNKNOWN_1 : 15|2@0+ (1,0) [0|3] "" XXX
 SG_ GE_Fahrstufe : 40|4@1+ (1,0) [0|15] "" XXX
 SG_ EPB_Status : 53|3@1+ (1,0) [0|7] "" XXX
 SG_ UNKNOWN_2 : 58|3@0+ (1,0) [0|7] "" XXX

BO_ 792 Kamera_Status: 8 XXX

BO_ 981 Licht_Anf_01: 8 Vector__XXX
 SG_ CHECKSUM : 0|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ COUNTER : 8|4@1+ (1,0) [0|15] "" Vector__XXX
 SG_ BCM1_Kurvenlicht_links_Anf : 12|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ BCM1_Kurvenlicht_rechts_Anf : 13|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ BCM1_Standlicht_Anf : 14|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ BCM1_Abblendlicht_Anf : 15|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ BCM1_Fernlicht_Anf : 16|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ BCM1_Nebellicht_Anf : 17|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ BCM1_Parklicht_li_Anf : 18|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ BCM1_Parklicht_re_Anf : 19|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ BCM1_Nebelschluss_Ahg_Anf : 20|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ BCM1_Nebelschluss_Fzg_Anf : 21|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ BCM1_Schlusslicht_Anf : 22|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ BCM_Rueckfahrlicht_Anf : 23|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ BCM1_Signaturlicht_Anf : 24|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ BCM1_Umfeldleuchten_Anf : 25|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ BCM1_Tagfahrlicht_Anf : 26|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ BCM1_Regenlicht_Anf : 27|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ BCM1_Autobahnlicht_Anf : 28|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ BCM1_Touristen_Licht_Anf : 29|1@1+ (1,0) [0|1] ""  Frontradar
 SG_ BCM1_CH_aktiv : 30|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ BCM1_LH_aktiv : 31|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ BCM1_Gleitende_Leuchtw_Anf : 32|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ BCM1_GLW_Fernlicht_Anf : 33|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ BCM1_Adaptive_Lichtvert_Anf : 34|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ BCM1_CH_LH_aktiv : 40|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ BCM1_Allwetterlicht_Anf : 41|1@1+ (1,0) [0|1] ""  Frontradar

BO_ 1440 RLS_01: 8 XXX

BO_ 870 Blinkmodi_02: 8 XXX
 SG_ Hazard_Switch : 20|1@1+ (1,0) [0|1] ""  XXX
 SG_ Comfort_Signal_Left : 23|1@1+ (1,0) [0|1] ""  XXX
 SG_ Comfort_Signal_Right : 24|1@1+ (1,0) [0|1] ""  XXX
 SG_ Left_Turn_Exterior_Bulb_1 : 25|1@1+ (1,0) [0|1] ""  XXX
 SG_ Right_Turn_Exterior_Bulb_1 : 26|1@1+ (1,0) [0|1] ""  XXX
 SG_ Left_Turn_Exterior_Bulb_2 : 27|1@1+ (1,0) [0|1] ""  XXX
 SG_ Right_Turn_Exterior_Bulb_2 : 28|1@1+ (1,0) [0|1] ""  XXX
 SG_ Fast_Send_Rate_Active : 37|1@1+ (1,0) [0|1] ""  XXX

BO_ 1385 HVEM_04: 8 XXX

BO_ 1605 FLA_01: 8 XXX

BO_ 1624 Licht_vorne_01: 8 XXX

BO_ 1646 Klima_03: 8 XXX

BO_ 1720 Kombi_03: 8 XXX
 SG_ KBI_Reifenumfang : 0|12@1+ (1,0) [0|4095] "Unit_MilliMeter"  XXX
 SG_ KBI_Variante_USA : 12|1@1+ (1.0,0.0) [0.0|1] ""  XXX
 SG_ KBI_Variante : 13|1@1+ (1.0,0.0) [0.0|1] ""  XXX
 SG_ KBI_BCmE_aktiv : 16|1@1+ (1.0,0.0) [0.0|1] ""  XXX
 SG_ KBI_Sparhinweis_quittiert : 17|1@1+ (1.0,0.0) [0.0|1] ""  XXX
 SG_ KBI_Tankfuellstand_Prozent : 18|7@1+ (1,0) [0|100] "Unit_PerCent"  XXX
 SG_ KBI_Nachtanken_erkannt : 25|1@1+ (1.0,0.0) [0.0|1] ""  XXX
 SG_ KBI_Tankinhalt_hochaufl : 26|14@1+ (0.01,0) [0.00|163.81] "Unit_Liter"  XXX
 SG_ KBI_Max_Tankinhalt : 40|8@1+ (0.5,0) [0.0|126.5] ""  XXX
 SG_ KBI_Reifenumfang_Sekundaer : 48|12@1+ (1,0) [0|4095] "Unit_MilliMeter"  XXX

BO_ 391 Motor_EV_01: 8 Motor_MQB_BEV
 SG_ CHECKSUM : 0|8@1+ (1,0) [0|255] ""  XXX
 SG_ COUNTER : 8|4@1+ (1,0) [0|15] ""  XXX
 SG_ EV_Rekuperationsstufe : 12|3@1+ (1.0,0.0) [0.0|7] ""  XXX
 SG_ HV_Bordnetz_aktiv : 15|1@1+ (1.0,0.0) [0.0|1] ""  XXX
 SG_ MO_Waehlpos : 16|3@1+ (1.0,0.0) [0.0|7] ""  XXX
 SG_ MO_Fehler_NTKreis : 19|1@1+ (1.0,0.0) [0.0|1] ""  XXX
 SG_ MO_Fehler_Notabschaltung_Klima : 20|2@1+ (1.0,0.0) [0.0|3] ""  XXX
 SG_ MO_KLE_FStatus : 22|2@1+ (1.0,0.0) [0.0|3] ""  XXX
 SG_ MO_WH_Texte : 24|3@1+ (1.0,0.0) [0.0|7] ""  XXX
 SG_ MO_Drehzahl_VM : 32|16@1+ (0.25,0) [0.00|16383.00] "Unit_MinutInver"  XXX
 SG_ HV_Bordnetz_Fehler : 48|1@1+ (1.0,0.0) [0.0|1] ""  XXX
 SG_ MO_Tankbereitschaft_Status : 49|3@1+ (1.0,0.0) [0.0|7] ""  XXX
 SG_ MO_Tankklappensteuerung : 52|2@1+ (1.0,0.0) [0.0|3] ""  XXX
 SG_ MO_HVEM_Eskalation : 54|1@1+ (1.0,0.0) [0.0|1] ""  XXX
 SG_ MO_HVEM_MaxLeistung : 55|9@1+ (50,0) [0|25450] "Unit_Watt"  XXX

CM_ SG_ 134 LWI_Lenkradwinkel "Steering angle WITH variable ratio effect included";
CM_ SG_ 159 EPS_HCA_Status "Status of Heading Control Assist feature";
CM_ SG_ 159 EPS_Lenkmoment "Steering input by driver, torque";
CM_ SG_ 159 EPS_VZ_Lenkmoment "Steering input by driver, direction";
CM_ SG_ 159 EPS_Berechneter_LW "Raw steering angle, degrees";
CM_ SG_ 159 EPS_VZ_BLW "Raw steering angle, direction";
CM_ SG_ 173 COUNTER_DISABLED "Message not renamed to COUNTER because J533 rate-limiting makes it look like messages are being lost";
CM_ SG_ 294 HCA_01_Vib_Freq "Frequenz der Lenkradvibration";
CM_ SG_ 294 HCA_01_LM_Offset "Von HCA angefordertes Lenkmoment (Betrag)";
CM_ SG_ 294 EA_ACC_Sollstatus "Status-Anforderung ACC von Emergency Alert. Statuswechsel bei Flanke. Solange Wert=1, wird EA_ACC_Wunschgeschwindigkeit übernommen. Wert=2 führt zu Zustand ¿ACC_GRA_passiv¿";
CM_ SG_ 294 EA_Ruckprofil "Emergency Alert Anforderung an ESP, welcher Ruck verwendet werden soll. Eine Umsetzung der Ruckanforderung im ESP erfolgt nur mit gesetztem Bit 'EA_Ruckfreigabe'.";
CM_ SG_ 294 HCA_01_Sendestatus "Gibt den Sendestatus der HCA_01 an (notwendig für IL-Unterstützung)";
CM_ SG_ 294 HCA_01_LM_OffSign "Vorzeichen des HCA-Lenkmoments";
CM_ SG_ 294 HCA_01_Status_HCA "Statusinformation vom HCA und Manoevrierassistent für Handshakemechanismus mit der Lenkung";
CM_ SG_ 294 HCA_01_Vib_Amp "Momentenamplitude der Lenkradvibration";
CM_ SG_ 294 EA_Ruckfreigabe "Emergency Alert Freigabit für die Warnruck-Anforderung an das ESP";
CM_ SG_ 294 EA_ACC_Wunschgeschwindigkeit "Emergency Alert Anforderung neue Wunschgeschwindigkeit";
CM_ SG_ 302 ACC_Hold_Request "Active request for ABS brake hold in ACC_Hold_Type";
CM_ SG_ 302 ACC_Boost_Request "Hybrid engine start related";
CM_ SG_ 302 ACC_Freewheel_Request "Active request for DSG sailing/coasting in ACC_Freewheel_Type";
CM_ SG_ 302 ACC_Hold_Release "Request to ABS to release brake hold";
CM_ SG_ 302 ACC_Accel_Secondary "Target acceleration of the secondary controller";
CM_ SG_ 302 ACC_Accel_TSK "Mirror of request to TSK to implement a target acceleration";
CM_ SG_ 870 Hazard_Switch "Four-way flashers active";
CM_ SG_ 870 Comfort_Signal_Left "Comfort turn signal active, left";
CM_ SG_ 870 Comfort_Signal_Right "Comfort turn signal active, right";
CM_ SG_ 870 Left_Turn_Exterior_Bulb_1 "Probably front";
CM_ SG_ 870 Right_Turn_Exterior_Bulb_1 "Probably front";
CM_ SG_ 870 Left_Turn_Exterior_Bulb_2 "Probably rear";
CM_ SG_ 870 Right_Turn_Exterior_Bulb_2 "Probably rear";
CM_ SG_ 870 Fast_Send_Rate_Active "CAN message send rate";
CM_ SG_ 919 LDW_DLC "Probable DLC (distance to line crossing)";
CM_ SG_ 919 LDW_TLC "Probable TLC (time to line crossing)";
CM_ SG_ 919 LDW_Unknown "Might be a steering pressed / driver active flag";
CM_ SG_ 919 Alert_Message "Static table of alert messages to be invoked in the instrument cluster, some with or without beeps, 0 = no current message";
CM_ SG_ 919 LDW_Direction "Left/right indicator for DLC and TLC";
CM_ SG_ 919 Right_Lane_Status "Display brightness range, 0 = no lane, 3 = full brightness";
CM_ SG_ 919 Left_Lane_Status "Display brightness range, 0 = no lane, 3 = full brightness";
CM_ SG_ 919 Kombi_Lamp_Orange "Enables orange LDW light in instrument cluster";
CM_ SG_ 919 Kombi_Lamp_Green "Enables green LDW light in instrument cluster";
CM_ SG_ 780 Folgefahrt "Following another vehicle";
CM_ SG_ 780 SetAbstand "Set following distance";
CM_ SG_ 780 Abstand "Following distance";
CM_ SG_ 780 SetSpeed "ACC set speed";
CM_ SG_ 391 MO_Waehlpos "Traditional PRND plus B-mode aggressive regen, B-mode mapped to Drive";
CM_ SG_ 679 ACC_ADAPTIVE "TSK_06.TSK_Limiter_ausgewaehlt seems to take precedence";
CM_ SG_ 960 ZAS_Kl_15 "Indicates ignition on";
CM_ SG_ 1720 KBI_BCmE_aktiv "Anzeige BCmE aktiv (BCmE-Screen oder Einsparhinweis in der Anzeige)";
CM_ SG_ 1720 KBI_Max_Tankinhalt "Mitteilung des maximalen Tankinhalts an das Reichweitenmodul";
CM_ SG_ 1720 KBI_Nachtanken_erkannt "Statusinformation Nachtankmodus";
CM_ SG_ 1720 KBI_Reifenumfang "Mittlerer Radumfang aus der K-Zahl gerechnet in Millimeter.  Byte 2 Bit 5,4 reserviert, Byte 2 Bit 3..0 und Byte 1 Bit 7..0; Wertebereich 0..4096 mm
";
CM_ SG_ 1720 KBI_Reifenumfang_Sekundaer "Fahrzeuge mit unterschiedlichen Reifenumfängen Vorderachse / Hinterachse:

Primärachse: KBI_Reifenumfang
Sekundärachse: KBI_Reifenumfang_Sekundaer
";
CM_ SG_ 1720 KBI_Sparhinweis_quittiert "angezeigter Sparhinweis ist quittiert. Signal wird nach zwei Sendebotschaften wieder auf '0' gesetzt.";
CM_ SG_ 1720 KBI_Tankfuellstand_Prozent "Tankfüllstand in %";
CM_ SG_ 1720 KBI_Tankinhalt_hochaufl "angezeigter Tankinhalt hochauflösend zur Restreichweitenberechnung";
CM_ SG_ 1720 KBI_Variante "Zeigt an ob es sich um ein konventionelles Zeiger-Kombiinstrument handelt oder um eine Volldisplay-Kombiinstrument";
CM_ SG_ 1720 KBI_Variante_USA "In diesem Signal wird die HW-Variante des Kombis ausgegeben, ACC plausibilisiert auf dieses Signal hin seine US-Codierung";


VAL_ 159 EPS_HCA_Status 0 "disabled" 1 "initializing" 2 "fault" 3 "ready" 4 "rejected" 5 "active" 8 "preempted" ;
VAL_ 173 GE_Fahrstufe 5 "P" 6 "R" 7 "N" 8 "D" 9 "S" 10 "E" 13 "T" 14 "T" ;
VAL_ 288 TSK_Status 0 "init" 1 "disabled" 2 "enabled" 3 "regulating" 4 "accel_pedal_override" 5 "brake_only" 6 "temp_fault" 7 "perm_fault" ;
VAL_ 288 TSK_v_Begrenzung_aktiv 0 "inaktiv" 1 "aktiv" ;
VAL_ 288 TSK_Standby_Anf_ESP 0 "keine_Standby_Anforderung" 1 "Standby_Anforderung" ;
VAL_ 288 TSK_Freig_WU 0 "TSK_Uebersetzungswunsch_nicht_freigegeben" 1 "TSK_Uebersetzungswunsch_freigegeben" ;
VAL_ 288 TSK_Freig_Verzoeg_Anf 0 "Verzoegerungsanforderung_nicht_freigegeben" 1 "Verzoegerungsanforderung_freigegeben" ;
VAL_ 288 TSK_Limiter_ausgewaehlt 0 "kein_Limiter_ausgewaehlt" 1 "Limiter_ausgewaehlt" ;
VAL_ 288 TSK_Wunsch_Uebersetz 0 "Init" ;
VAL_ 288 TSK_Hauptschalter_GRA_ACC 0 "Init" 1 "Aus" 2 "Ein" 3 "Fehler" ;
VAL_ 288 TSK_ax_Getriebe_02 511 "Neutralwert" ;
VAL_ 288 TSK_Zwangszusch_ESP 0 "keine_ESP_ASR_Beeinflussung" 1 "ESP_ASR_Beeinflussung" ;
VAL_ 294 EA_ACC_Sollstatus 0 "Init" 1 "ACC_aktivieren" 2 "ACC_deaktivieren" ;
VAL_ 294 EA_Ruckprofil 0 "Init" 1 "Profil_1" 2 "Profil_2" 3 "Profil_3" 4 "Profil_4" 5 "Profil_5" 6 "Profil_6" 7 "Profil_7" ;
VAL_ 294 HCA_01_Sendestatus 0 "HCA_sendet_mit_1000ms" 1 "HCA_sendet_mit_20ms" ;
VAL_ 294 HCA_01_LM_OffSign 0 "positives_Vorzeichen" 1 "negatives_Vorzeichen" ;
VAL_ 294 HCA_01_Status_HCA 0 "deaktiviert" 1 "reserviert" 2 "reserviert" 3 "funktionsbereit" 4 "reserviert" 5 "HCA_Momenteneingriff_1" 6 "MA_Aktiv" 7 "HCA_Momenteneingriff_2" 8 "reserviert" 9 "reserviert" 10 "reserviert" 11 "reserviert" 12 "reserviert" 13 "reserviert" 14 "reserviert" 15 "reserviert" ;
VAL_ 294 EA_Ruckfreigabe 0 "keine_Freigabe" 1 "Freigabe" ;
VAL_ 294 EA_ACC_Wunschgeschwindigkeit 1023 "Init" ;
VAL_ 346 MO_Anforderung_HMS 0 "keine_Anforderung" 1 "halten" 2 "parken" 3 "halten_Standby" 4 "anfahren" 5 "Loesen_ueber_Rampe" ;
VAL_ 346 TSK_Status_EA 0 "Aus" 1 "Init_oder_nicht_verbaut" 3 "Aktiv" 4 "Uebertreten" 5 "Abschaltung_laeuft" 6 "Reversibel_aus" 7 "Irreversibel_Aus" ;
VAL_ 346 TSK_vMax_Fahrerassistenz 511 "Init_ungueltig_keine_Beschraenkung" ;
VAL_ 346 TSK_Einheit_vMax_Fahrerassistenz 0 "kmh" 1 "mph" ;
VAL_ 346 TSK_Status_PLA 0 "Aus_Funktionsbereit" 1 "Init_oder_nicht_verbaut" 2 "aktivierbar" 3 "aktiv" 5 "Abschaltung_laeuft" 6 "reversibel_aus" 7 "Fehler" ;
VAL_ 346 TSK_aktives_System 0 "keine_Funktion_aktiv" 1 "GRA_ACC" 2 "ARA" 3 "Speedlimiter" 4 "IPA" 5 "PLA" 6 "PEA_Ausrollassistent" 7 "EA" ;
VAL_ 346 TSK_erhoehter_Fahrwiderstand 0 "kein_erhoehter_Fahrwiderstand" 1 "erhoehter_Fahrwiderstand" ;
VAL_ 346 TSK_Anf_Antriebsmoment 0 "keine_Anforderung" 1 "Anforderung_aktiv" ;
VAL_ 346 TSK_Status_ARA 0 "Aus" 1 "Init_oder_nicht_verbaut" 2 "aktivierbar" 3 "aktiv" 5 "abschaltung_laeuft" 6 "reversibel_aus" 7 "Fehler" ;
VAL_ 346 TSK_Status_IPA 0 "Aus_Funktionsbereit" 1 "Init_oder_nicht_verbaut" 2 "aktivierbar" 3 "aktiv" 5 "Abschaltung_laueft" 6 "reversibel_aus" 7 "Fehler" ;
VAL_ 798 TSK_Wunschgeschw 1022 "keine_Anzeige" 1023 "kein_Wert_im_Speicher" ;
VAL_ 798 TSK_Texte 0 "kein_Text" 1 "GRA_Modus_ausgewaehlt" 2 "ACC_Modus_ausgewaehlt" 3 "Lim_Modus_ausgewaehlt" 4 "Lim_nicht_verfuegbar_ESC_passiv" 5 "GRA_nicht_verfuegbar_ESC_passiv" 6 "Lim_nicht_verfuegbar_Charisma" 7 "GRA_nicht_verfuegbar_Charisma" 8 "Lim_nicht_verfuegbar_HDC" 9 "GRA_nicht_verfuegbar_HDC" ;
VAL_ 798 TSK_Akustik 0 "keine_Akustik" 1 "einzelner_Warnton" 2 "dauerhafter_Warnton" ;
VAL_ 798 TSK_Texte_Primaeranz 0 "keine_Anzeige" 1 "GRA_Symbol_passiv_xxx_kmh_mph" 2 "GRA_Symbol_aktiv_xxx_kmh_mph" 3 "Bremse_ueberhitzt" 4 "Limiter_Modus_aktiviert" 5 "GRA_Modus_aktiviert" 6 "ACC_Modus_aktiviert" 7 "Opt_Geschwindigkeitswarnung" 8 "Opt_und_akustische_GeschwWarnung" 9 "Opt_GeschwWarnung_dauerhaft_mit_einmal_Akustik" 10 "Limiter_passiv_mit_Akustik" 11 "Limiter_Fehler_mit_Akustik" 12 "Limiter_Symbol_passiv_xxx_kmh_mph" 13 "Limiter_Symbol_aktiv_xxx_kmh_mph" 14 "Popup_Geschw_zu_hoch__Resume_unzulaessig" ;
VAL_ 798 TSK_Limiter_Fahrerinfo 0 "keine_Info" 1 "Limit_erreicht" 2 "Ueberschritten" 3 "Vom_Fahrer_Ueberstimmt" ;
VAL_ 798 TSK_Limiter_Anzeige 0 "Display_Anzeige_GRA_ACC" 1 "Display_Anzeige_Limiter" ;
VAL_ 798 TSK_Fahrzeugstatus_GRA 0 "GRA_verfuegbar" 1 "GRA_nicht_verfuegbar" ;
VAL_ 798 TSK_Fahrzeugstatus_Limiter 0 "Limiter_verfuegbar" 1 "Limiter_nicht_verfuegbar" ;
VAL_ 798 MO_Motorlaufwarnung 0 "keine_Anzeige" 1 "Anforderung_Motorlaufwarnung" ;
VAL_ 798 TSK_Status_Anzeige 0 "Hauptschalter_aus" 1 "Init" 2 "passiv" 3 "aktiv" 4 "Uebertreten" 5 "Limitiierung_aktiv" 6 "reversibel_aus" 7 "irreversibel_aus" ;
VAL_ 780 ACC_Wunschgeschw_02 1023 "keine_Anzeige" ;
VAL_ 780 ACC_Status_Prim_Anz 0 "Symbol nicht beleuchtet" 1 "Farbe 1 (typisch 'gruen')" 2 "Farbe 2 (typisch 'rot')" 3 "Farbe 3 (typisch 'gelb')" ;
VAL_ 780 ACC_Abstandsindex 0 "Sonderanzeige_graue_Fahrbahn" 1022 "Sonderanzeige_graue_Fahrbahn" 1023 "Sonderanzeige_Fahrbahn_mit_gruenem_roten_Bereich" ;
VAL_ 780 ACC_Akustik_02 0 "keine_Akustik" 1 "hochpriore_Akustik" 2 "niederpriore_Akustik" 3 "hochpriore_Dauerakustik" ;
VAL_ 780 ACC_Warnung_Verkehrszeichen_1 0 "keine_Warnung_Initialwert" 1 "Warnung" ;
VAL_ 780 ACC_Gesetzte_Zeitluecke 0 "keine_Anzeige" 1 "Zeitluecke_1" 2 "Zeitluecke_2" 3 "Zeitluecke_3" 4 "Zeitluecke_4" 5 "Zeitluecke_5" 6 "nicht_definiert" 7 "nicht_definiert" ;
VAL_ 780 ACC_Optischer_Fahrerhinweis 0 "optischer_Fahrerhinweis_AUS" 1 "optischer_Fahrerhinweis_EIN" ;
VAL_ 780 ACC_Typ_Tachokranz 0 "Tachokranz_lang" 1 "Tachokranz_kurz" ;
VAL_ 780 ACC_Anzeige_Zeitluecke 0 "Anzeige_Zeitluecke_nicht_angefordert" 1 "Anzeige_Zeitluecke_angefordert" ;
VAL_ 780 ACC_Tachokranz 0 "Tachokranz_nicht_beleuchtet" 1 "Tachokranz_beleuchtet" ;
VAL_ 780 ACC_Display_Prio 0 "hoechste_Prio" 1 "mittlere_Prio" 2 "geringe_Prio" 3 "keine_Prio" ;
VAL_ 780 ACC_Relevantes_Objekt 0 "Symbol_nicht_beleuchtet" 1 "Farbe_1_typisch_gruen" 2 "Farbe_2_typisch_rot" 3 "Farbe_3_typisch_gelb" ;
VAL_ 780 ACC_Texte_Primaeranz 0 "keine Anzeige" 1 "ACC nicht verfuegbar !" 2 "Auto_Auto_ _ _" 3 "Auto_ _Auto_ _" 4 "Auto_ _ _Auto_" 5 "Auto_ _ _ _Auto" 6 "Auto_Auto_ _ _ Gong (durchgestrichen)" 7 "Auto_ _Auto_ _ Gong (durchgestrichen)" 8 "Auto_ _ _Auto_ Gong (durchgestrichen)" 9 "Auto_ _ _ _Auto Gong (durchgestrichen)" 10 "ACC bereit" 11 "keine Abstandsregelung" 12 "ACC Sensor Sicht !" 13 "ACC nicht verfuegbar" 14 "o  o  o" 15 "Hochschalten" 16 "ESP Eingriff" 17 "Herunterschalten" 18 "Parkbremse !" 19 "Geschwindigkeitsgrenze" 20 "Waehlhebelposition !" 21 "VDA ACC-Symbol YYY km/h / mph" 22 "Tempolimit XXX km/h / mph" 23 "Kurve XXX km/h / mph" 24 "ACC Abschaltung" 25 "Symbol 'Eieruhr'" 26 "!" 27 "--- km/h / mph" 28 "XXX km/h / mph (Schriftart 2)" 29 "Lenkradwinkel" 30 "Anfahren bestaetigen" 31 "Fahrzeug verloren" 32 "Im Stand nicht moeglich" 33 "Ungueltiger Anfahrbefehl" 34 "Tuer offen !" 35 "Fahrer Gurtschloss offen !" 36 "Schalthebelposition !" 37 "Drehzahl !" 38 "Kurvenassistent aus" 39 "Tempolimit aus" 40 "Abbiegeassistent" 41 "Ortsanfang XXX km/h / mph" 42 "Ortsende XXX km/h / mph" 43 "Tempolimit Ende XXX km/h / mph" 44 "HDC aktiv" 45 "braking guard Bremsruck" 46 "braking guard aus" 47 "braking guard aus" 48 "Uebernehmen !" 49 "Steigung zu gross" 50 "Stehendes Objekt voraus" 51 "SET / 'GRA Symbol'___xxx km/h / mph" 52 "SET / 'GRA Symbol' xxx km/h / mph" 53 "ACC aus" 54 "ACC startet" 55 "ACC reinigen" 56 "ACC Fehler" 57 "ACC haelt an !" 58 "Bremse betaetigen !" 59 "Kupplung betaetigt" 60 "LIM AUS" 61 "LIM AKTIV" 62 "LIM PASSIV" 63 "LIM FEHLER" 64 "Bremse ueberhitzt !" 65 "Bremse haelt !" 66 "ESP PASSIV !" 67 "ACC_anfahrbereit" 68 "Gang_einlegen" 69 "Rechtsueberholen_verhindert" 70 "Linksueberholen_verhindert" 71 "Achtung_Geschwindigkeitsueberschreitung" 72 "Tempolimit_und_Kurvenassistent_nicht_verfuegbar" ;
VAL_ 780 ACC_Wunschgeschw_erreicht 0 "Wunschgeschwindigkeit_nicht_erreicht" 1 "Wunschgeschwindigkeit_erreicht" ;
VAL_ 780 ACC_Typ_Tachokranz_unten 0 "LEDs_an" 1 "LEDs_aus" ;
VAL_ 780 ACC_Status_Anzeige 0 "ACC_GRA_Hauptschalter_aus" 1 "ACC_in_Init_nicht_bei_GRA" 2 "ACC_GRA_passiv" 3 "ACC_GRA_aktiv" 4 "ACC_GRA_im_Hintergrund_uebertreten" 5 "ACC_GRA_Abschaltreaktion" 6 "ACC_reversibel_aus_nicht_bei_GRA" 7 "ACC_GRA_irreversibel_aus" ;
VAL_ 804 ACC_Texte_Sekundaeranz 0 "keine_Anzeige" 1 "Zielfahrzeug_erkannt" 2 "Rechtskurve_voraus" 3 "Linkskurve_voraus" 4 "Tempolimit_voraus" 5 "Sensorsicht" 6 "Anfahrbereit" 7 "Tempolimit_erkannt" 8 "Kreuzung_voraus" 9 "Kreisverkehr_voraus" ;
VAL_ 804 ACC_Texte_Zusatzanz 0 "keine_Anzeige" 1 "ACC_AUS" 2 "ACC_BEREIT" 3 "UEBERTRETEN" 4 "ABSTAND" 5 "DISTANZ_1" 6 "DISTANZ_2" 7 "DISTANZ_3" 8 "DISTANZ_4" 9 "DISTANZ_1__dyn" 10 "DISTANZ_2__dyn" 11 "DISTANZ_3__dyn" 12 "DISTANZ_4__dyn" 13 "DISTANZ_1__stand" 14 "DISTANZ_2__stand" 15 "DISTANZ_3__stand" 16 "DISTANZ_4__stand" 17 "DISTANZ_1__comf" 18 "DISTANZ_2__comf" 19 "DISTANZ_3__comf" 20 "DISTANZ_4__comf" 21 "DISTANZ_1__efficiency" 22 "DISTANZ_2__efficiency" 23 "DISTANZ_3__efficiency" 24 "DISTANZ_4__efficiency" 25 "DISTANZ_5" 26 "DISTANZ_5__dyn" 27 "DISTANZ_5__stand" 28 "DISTANZ_5__comf" 29 "DISTANZ_5__efficiency" 30 "ACHTUNG" 31 "Abstandsanzeige" 32 "Abstandsanz_Warnung_aktiviert" ;
VAL_ 804 ACC_Status_Zusatzanz 0 "keine Anzeige" 1 "Bild 1 (Fzg. Silhouette, typ. farblos)" 2 "Bild 2 (Fzg. Farbe 1, typ. grau)" 3 "Bild 3 (Fzg. Farbe 2, typ. rot)" 4 "Bild 4 (Symbol 1 ACCplus, typ. Doppelfzg. gelb)" 5 "Bild 5 (Symbol 2 ACCplus, typ. Doppelfzg. grau)" 6 "Bild 6 (Fzg. Farbe 2, typ. rot) mit Priorität im Kombi (Pop-up)" ;
VAL_ 804 ACC_Texte 0 "keine_Anzeige" 1 "ACC_nicht_verfuegbar" 2 "ACC_und_AWV_nicht_verfuegbar" 3 "ACC_keine_Sensorsicht" 4 "ACC_und_AWV_keine_Sensorsicht" 5 "ACC_Steigung_zu_gross" 6 "ACC_nur_in_Fahrstufe_verfuegbar" 7 "ACC_Parkbremse_betaetigt" 8 "ACC_ESP_Eingriff" 9 "ACC_Bitte_uebernehmen" 10 "ACC_HDC_betaetigt" 11 "ACC_Geschwindigkeitsgrenze" 12 "ACC_Schalthebelposition" 13 "ACC_Drehzahl" 14 "ACC_Kupplung_betaetigt" 15 "ACC_Aktivierverhinderung" 16 "ACC_Abschaltung" 17 "ACC_Parkassistent_aktiv" 18 "ACC_ESC_aus" 19 "ACC_Charisma_Modus_inkompatibel" 20 "ACC_Stehendes_Objekt_voraus" 21 "ACA_Fahreruebernahme" 22 "ACA_Querfuehrung_nicht_verfuegbar" ;
VAL_ 804 ACC_Texte_braking_guard 0 "keine_Anzeige" 1 "AWV_aus" 2 "AWV_Warnung" 3 "AWV_Demomodus" 4 "AWV_Systemstoerung" 5 "AWV_Eingriff" 6 "AWV_Vorwarnung_aus" 7 "AWV_keine_Sensorsicht" ;
VAL_ 804 ACC_Warnhinweis 0 "kein_Warnhinweis" 1 "Warnhinweis" ;
VAL_ 804 ACC_Zeitluecke_Abstandswarner 61 "Freifahrt" 62 "nicht_definiert" 63 "keine_Anzeige" ;
VAL_ 804 ACC_Abstand_Abstandswarner 509 "Freifahrt" 510 "nicht_definiert" 511 "keine_Anzeige" ;
VAL_ 804 ACC_Tempolimit 0 "keine_Anzeige" 1 "5_zulHoechstgeschw" 2 "7_zulHoechstgeschw" 3 "10_zulHoechstgeschw" 4 "15_zulHoechstgeschw" 5 "20_zulHoechstgeschw" 6 "25_zulHoechstgeschw" 7 "30_zulHoechstgeschw" 8 "35_zulHoechstgeschw" 9 "40_zulHoechstgeschw" 10 "45_zulHoechstgeschw" 11 "50_zulHoechstgeschw" 12 "55_zulHoechstgeschw" 13 "60_zulHoechstgeschw" 14 "65_zulHoechstgeschw" 15 "70_zulHoechstgeschw" 16 "75_zulHoechstgeschw" 17 "80_zulHoechstgeschw" 18 "85_zulHoechstgeschw" 19 "90_zulHoechstgeschw" 20 "95_zulHoechstgeschw" 21 "100_zulHoechstgeschw" 22 "110_zulHoechstgeschw" 23 "120_zulHoechstgeschw" 24 "130_zulHoechstgeschw" 25 "140_zulHoechstgeschw" 26 "150_zulHoechstgeschw" 27 "160_zulHoechstgeschw" 28 "200_zulHoechstgeschw" 30 "250_zulHoechstgeschw" 31 "Ende_zulHoechstgeschw" ;
VAL_ 804 ACC_Charisma_FahrPr 0 "keine_Funktion" 1 "Programm_1" 2 "Programm_2" 3 "Programm_3" 4 "Programm_4" 5 "Programm_5" 6 "Programm_6" 7 "Programm_7" 8 "Programm_8" 9 "Programm_9" 10 "Programm_10" 11 "Programm_11" 12 "Programm_12" 13 "Programm_13" 14 "Programm_14" 15 "Programm_15" ;
VAL_ 804 ACC_Charisma_Status 0 "Init" 1 "verfuegbar" 2 "nicht_verfuegbar" 3 "asynchron_durch_Fahrerwunsch" ;
VAL_ 804 ACC_Texte_Abstandswarner 0 "keine_Anzeige" 1 "Systemstoerung" 2 "keine_Sensorsicht" 3 "zurzeit_eingeschraenkt" ;
VAL_ 290 ACC_limitierte_Anfahrdyn 0 "keine_Limitierung" 1 "Limitierung_Anfahrdynamik_angefordert" ;
VAL_ 290 ACC_nachtr_Stopp_Anf 0 "nicht_angefordert" 1 "angefordert" ;
VAL_ 290 ACC_StartStopp_Info 0 "Motorlauf_langfristig_nicht_notwendig_Stoppfreigabe" 1 "Motoranlauf_nicht_zwingend_notwendig_Stoppverbot_keine_Startanforderung" 2 "Motoranlauf_zwingend_notwendig_Startanforderung" 3 "Systemfehler" ;
VAL_ 290 ACC_Sollbeschleunigung_02 2046 "Neutralwert" 2047 "Fehler" ;
VAL_ 290 ACC_Anfahren 0 "keine_Anforderung_Anfahren" 1 "Anforderung_Anfahren" ;
VAL_ 290 ACC_Anhalten 0 "kein_Anhalten_gewuenscht" 1 "Anhalten_gewuenscht" ;
VAL_ 290 ACC_Typ 0 "Basis_ACC" 1 "ACC_mit_FollowToStop" 2 "ACC_mit_StopAndGo" 3 "ACC_nicht_codiert" ;
VAL_ 290 ACC_Status_ACC 0 "ACC_OFF_Hauptschalter_aus" 1 "ACC_INIT" 2 "ACC_STANDBY" 3 "ACC_AKTIV_regelt" 4 "ACC_OVERRIDE" 5 "ACC_Abschaltreaktion" 6 "reversibler_Fehler_im_ACC_System" 7 "irreversibler_Fehler_im_ACC_System" ;
VAL_ 290 ACC_Minimale_Bremsung 0 "Anforderung_Minimale_Bremsung_nicht_aktiv" 1 "Anforderung_Minimale_Bremsung_aktiv" ;
VAL_ 302 ACC_Anhalteweg 2046 "Neutralwert" 2047 "Fehler" ;
VAL_ 302 ACC_Anhalten 0 "kein_Anhalten_gewuenscht" 1 "Anhalten_gewuenscht" ;
VAL_ 302 ACC_Freilauf_Anf 0 "keine Freilauf-Anforderung" 1 "Freilauf-Anforderung" ;
VAL_ 302 ACC_Freilauf_Info 0 "Freilauf_freigegeben" 1 "kein_Uebergang_in_Freilauf_zulaessig" 2 "Freilauf_nicht_freigegeben" 3 "Freilauf_Anforderung" ;
VAL_ 302 ACC_Anforderung_HMS 0 "keine_Anforderung" 1 "halten" 2 "parken" 3 "halten_Standby" 4 "anfahren" 5 "Loesen_ueber_Rampe" ;
VAL_ 302 ACC_Anfahren 0 "keine_Anforderung_Anfahren" 1 "Anforderung_Anfahren" ;
VAL_ 302 ACC_Folgebeschl 254 "Neutralwert" ;
VAL_ 302 ACC_Sollbeschleunigung_02 2046 "Neutralwert" 2047 "Fehler" ;
VAL_ 279 AWV1_Anf_Prefill 0 "Prefill_nicht_aktivieren" 1 "Prefill_aktivieren" ;
VAL_ 279 ANB_CM_Info 0 "Standard" 1 "Erweitert" ;
VAL_ 279 AWV2_Freigabe 0 "keine_Freigabe" 1 "Freigabe" ;
VAL_ 279 AWV1_HBA_Param 0 "Defaultparametersatz" 1 "Parametersatz_mit_leicht_erhoehter_Empfindlichkeit" 2 "Parametersatz_mit_erhoehter_Empfindlichkeit" 3 "Parametersatz_mit_hoechster_Empfindlichkeit" ;
VAL_ 279 AWV2_Priowarnung 0 "Anzeige_Verlassen_der_Fahrspur_wird_nicht_unterdrueckt" 1 "Anzeige_Verlassen_der_Fahrspur_wird_unterdrueckt" ;
VAL_ 279 ANB_CM_Anforderung 0 "keine_Anforderung" 1 "Anforderung_aktiv" ;
VAL_ 279 ANB_Info_Teilbremsung 0 "Auspraegung_Standard" 1 "Auspraegung_Erweitert" ;
VAL_ 279 ANB_Notfallblinken 0 "kein_ANB_Notfallblinken" 1 "Notfallblinken_ANB_angefordert" ;
VAL_ 279 ANB_Teilbremsung_Freigabe 0 "Teilbremsung_nicht_freigegeben" 1 "Teilbremsung_freigegeben" ;
VAL_ 279 ANB_Zielbremsung_Freigabe 0 "Zielbremsung_nicht_freigegeben" 1 "Zielbremsung_freigegeben" ;
VAL_ 279 AWV_Vorstufe 0 "keine_Notbremsung_erwartet" 1 "Notbremsung_in_Kuerze" ;
VAL_ 279 AWV_Halten 0 "keine_Anforderung" 1 "Anforderung_das_Fzg_im_Stillstand_zu_halten" ;
VAL_ 279 AWV_CityANB_Auspraegung 0 "autom_Bremsung_im_ges_vBereich" 1 "autom_Bremsung_im_def_vBereich" ;
VAL_ 279 PCF_Freigabe 0 "keine_Freigabe_PreCrashFront" 1 "Freigabe_PreCrashFront" ;
VAL_ 279 AWV1_ECD_Anlauf 0 "ECD_Anlauf_nicht_aktivieren" 1 "ECD_Anlauf_aktivieren" ;
VAL_ 279 PCF_Time_to_collision 255 "Objektstatus=0x0__oder_berechneter_TTC_Wert_groesser_als_Maximalwert" ;
VAL_ 679 ACC_Regelgeschw 1023 "keine_Anzeige" ;
VAL_ 679 ACC_Einheit_maxSetzgeschw 0 "kmh" 1 "mph" ;
VAL_ 679 ACC_maxSetzgeschw 511 "Init_Neutralwert" ;
VAL_ 679 ACC_minRegelgeschw 255 "keine_Anzeige" ;
VAL_ 679 ACC_maxRegelgeschw 255 "keine_Anzeige" ;
VAL_ 679 ACC_Tempolimitassistent 0 "keine_Anzeige" 1 "Tempolimitassistent_aktiv" 2 "Tempolimitassistent_nicht_verfuegbar" 3 "Tempolimitassistent_Fahreruebernahme" ;
VAL_ 679 ACC_Kurvenassistent 0 "keine_Anzeige" 1 "Kreuzung" 2 "Rechtskurve" 3 "Linkskurve" 4 "Kreisverkehr" ;
VAL_ 679 ACC_RUV 0 "keine_Anzeige" 1 "RUV_aktiv_Rechtsverkehr" 2 "RUV_aktiv_Linksverkehr" ;
VAL_ 679 ACC_Tachokranz 0 "Tachokranz_nicht_beleuchtet" 1 "Tachokranz_beleuchtet" ;
VAL_ 679 ACC_Typ_Tachokranz_unten 0 "LEDs_an" 1 "LEDs_aus" ;
VAL_ 679 ACC_ENG_Texte 0 "keine_Anzeige" 1 "keine_Laenderverfuegbarkeit" 2 "nicht_verfuegbar" 3 "Geschwindigkeitsgrenze" ;
VAL_ 679 ACC_ADAPTIVE 1 "adaptive" 2 "non-adaptive" ;
VAL_ 681 AWV_Warnung 0 "keine_Anzeige" 1 "latente_Vorwarnung" 2 "Vorwarnung" 3 "Akutwarnung" 4 "Eingriff" 5 "Fahreruebernahmeaufforderung" 6 "Abbiegewarnung" ;
VAL_ 681 AWV_Texte 0 "keine_Anzeige" 1 "Systemstoerung" 2 "keine_Sensorsicht" 3 "Demomodus" 4 "System_aus" 5 "nicht_definiert" 6 "ESC_aus" 7 "zurzeit_eingeschraenkt" ;
VAL_ 681 AWV_Status_Anzeige 0 "Init" 1 "verfuegbar" 2 "nicht_verfuegbar" ;
VAL_ 681 AWV_Einstellung_System_FSG 0 "deaktiviert" 1 "aktiviert" ;
VAL_ 681 AWV_Einstellung_Warnung_FSG 0 "Aus" 1 "Setting_2" 2 "Setting_3" 3 "Setting_4" 4 "Setting_5" 5 "Ein" ;
VAL_ 681 AWV_Warnlevel 0 "keine_Gefaehrdung" 63 "max_Gefaehrdung" ;
VAL_ 391 MO_Waehlpos 2 "P" 3 "R" 4 "N" 5 "D" 6 "D" ;
VAL_ 391 EV_Rekuperationsstufe 0 "default" 1 "B1" 2 "B2" 3 "B3" ;
VAL_ 870 Fast_Send_Rate_Active 0 "1 Hz" 1 "50 Hz" ;
VAL_ 988 EPB_Status 0 "offen" 1 "geschlossen_Parken" 2 "teilgespannt_Halten" 3 "im_Lauf_oeffnen" 4 "im_Lauf_schliessen" 5 "tbd" 6 "Init" 7 "unbekannt";
VAL_ 988 GE_Fahrstufe 0 "Zwischenstellung" 1 "Init" 5 "P" 6 "R" 7 "N" 8 "D" 9 "D" 10 "E" 13 "T" 14 "T" 15 "Fehler";
VAL_ 1720 KBI_Variante_USA 0 "keine USA-Variante" 1 "USA-Variante" ;
VAL_ 1720 KBI_Variante 0 "Zeiger Kombiinstrument" 1 "Volldisplay Kombiinstrument" ;
VAL_ 1720 KBI_BCmE_aktiv 0 "Anzeige_nicht_aktiv" 1 "Anzeige_aktiv" ;
VAL_ 1720 KBI_Sparhinweis_quittiert 0 "nicht_quittiert" 1 "quittiert" ;
VAL_ 1720 KBI_Tankfuellstand_Prozent 126 "Init" 127 "Fehler" ;
VAL_ 1720 KBI_Nachtanken_erkannt 0 "Geberbetrieb" 1 "Nachtankmodus" ;
VAL_ 1720 KBI_Tankinhalt_hochaufl 16382 "Init" 16383 "Fehler" ;
VAL_ 1720 KBI_Max_Tankinhalt 254 "Init" 255 "Fehler" ;
