BO_ 96 CAR_OVERALL_SIGNALS2: 64 XXX
 SG_ CRC1 : 7|8@0+ (1,0) [0|255] "" XXX
 SG_ COUNTER1 : 59|4@0+ (1,0) [0|15] "" XXX
 SG_ CRC2 : 71|8@0+ (1,0) [0|255] "" XXX
 SG_ GAS_POSITION : 79|8@0+ (0.393700787,0) [0|255] "%" XXX
 SG_ BRAKE_SIGNAL : 86|1@0+ (1,0) [0|1] "" XXX
 SG_ ACC_GAS_POSITION : 103|8@0+ (0.393700787,0) [0|255] "%" XXX
 SG_ COUNTER2 : 123|4@0+ (1,0) [0|15] "" XXX
 SG_ CRC3 : 135|8@0+ (1,0) [0|255] "" XXX
 SG_ REQ_REVIEW_UND_SIGNAL : 143|10@0+ (1,0) [0|255] "" XXX
 SG_ REQ_REVIEW_POWER_CONSUMPTION : 144|9@0+ (1,-175) [0|511] "" XXX
 SG_ REQ_REVIEW_POWER_CONSUMPTION2 : 180|9@0+ (1,-176) [0|511] "" XXX
 SG_ COUNTER3 : 187|4@0+ (1,0) [0|15] "" XXX
 SG_ REQ_REVIEW_POWER_STATE_SIGNAL : 302|4@0+ (1,0) [0|15] "" XXX
 SG_ REQ_REVIEW_POWER_STATE_SIGNAL2 : 370|1@0+ (1,0) [0|1] "" XXX

BO_ 147 IMPRECISE_SPEED_INFORMATION: 8 XXX
 SG_ IMPRECISE_SPEED : 15|8@0+ (3.5,0) [0|255] "kph" XXX
 SG_ REQ_REVIEW_SPEED : 31|8@0+ (0.430107526,0) [0|255] "Kph" XXX
 SG_ REQ_REVIEW_SPEED2 : 39|8@0+ (0.430107526,0) [0|255] "Kph" XXX
 SG_ REQ_REVIEW_POWER_CONSUMPTION : 52|8@0+ (1,0) [0|255] "" XXX
 SG_ COUNTER : 59|4@0+ (1,0) [0|15] "" XXX

BO_ 161 STEER_AND_AP_STALK: 8 XXX
 SG_ CRC : 7|8@0+ (1,0) [0|255] "" XXX
 SG_ STEERING_ANGLE : 15|16@0+ (0.05,0) [0|65535] "degs" XXX
 SG_ STEERING_TORQUE : 31|16@0+ (1,0) [0|65535] "??" XXX
 SG_ AP_REDUCE_DISTANCE_COMMAND : 44|1@0+ (1,0) [0|1] "" XXX
 SG_ AP_INCREASE_DISTANCE_COMMAND : 45|1@0+ (1,0) [0|1] "AUTOPILOT_STALK" XXX
 SG_ AP_CANCEL_COMMAND : 46|1@0+ (1,0) [0|1] "" XXX
 SG_ AP_ENABLE_COMMAND : 47|1@0+ (1,0) [0|1] "" XXX
 SG_ AP_DECREASE_SPEED_COMMAND : 50|1@0+ (1,0) [0|1] "" XXX
 SG_ AP_INCREASE_SPEED_COMMAND : 51|1@0+ (1,0) [0|1] "" XXX
 SG_ COUNTER : 59|4@0+ (1,0) [0|255] "" XXX

BO_ 259 SPEED: 64 XXX
 SG_ CRC : 199|8@0+ (1,0) [0|255] "" XXX
 SG_ UNDEFINED_ACCUMULATOR : 207|8@0+ (1,0) [0|255] "" XXX
 SG_ UNDEFINED_COUNTER : 215|8@0+ (1,0) [0|255] "" XXX
 SG_ ODD_GAS_POSITION : 231|8@0+ (1,0) [0|255] "" XXX
 SG_ SPEED : 239|8@0+ (1,0) [0|255] "Khp" XXX
 SG_ COUNTER2 : 251|4@0+ (1,0) [0|15] "" XXX

BO_ 273 UNDEFINED_2: 8 XXX
 SG_ CRC : 7|8@0+ (1,0) [0|255] "" XXX
 SG_ UNDEFINED_SIGNAL_RELATED_ACC : 46|13@0+ (1,0) [0|8191] "" XXX
 SG_ COUNTER : 59|4@0+ (1,0) [0|15] "" XXX

BO_ 288 BRAKE2: 64 XXX
 SG_ CRC : 7|8@0+ (1,0) [0|255] "" XXX
 SG_ REVIEW_REQ_BRAKE_SIGNAL1 : 15|8@0+ (1,0) [0|255] "" XXX
 SG_ REQ_REVIEW_MOVING : 22|1@0+ (1,0) [0|1] "" XXX
 SG_ ODD_BRAKE_PRESSURE : 39|8@0+ (1,-27) [0|255] "" XXX
 SG_ REVIEW_REQ_BRAKE_SIGNAL2 : 47|8@0+ (1,0) [0|255] "" XXX
 SG_ COUNTER : 59|4@0+ (1,0) [0|15] "" XXX

BO_ 299 AUTOPILOT: 64 XXX
 SG_ REQ_REVIEW_CRC : 71|8@0+ (1,0) [0|255] "" XXX
 SG_ AP_UNDEFINED_SIGNAL2 : 73|2@0+ (1,0) [0|3] "" XXX
 SG_ REQ_REVIEW_COUNTER : 78|5@0+ (1,0) [0|31] "" XXX
 SG_ AP_UNDEFINED_SIGNAL : 87|6@0+ (1,0) [0|63] "" XXX
 SG_ AP_STEERING_UNDEFINED_SIGNAL1 : 102|10@0- (1,0) [0|2047] "" XXX
 SG_ AP_UNDEFINED_SIGNAL3 : 103|1@0+ (1,0) [0|1] "" XXX
 SG_ COUNTER : 123|4@0+ (1,0) [0|15] "" XXX
 SG_ AP_STATE : 125|1@0+ (1,0) [0|1] "" XXX
 SG_ CRC : 135|8@0+ (1,0) [0|255] "" XXX

BO_ 303 CAR_OVERALL_SIGNALS: 64 XXX
 SG_ CRC1 : 71|8@0+ (1,0) [0|255] "" XXX
 SG_ DRIVE_MODE_SIGNAL : 73|1@0+ (1,0) [0|1] "" XXX
 SG_ REQ_REVIEW_POWER_STATE : 80|1@0+ (1,0) [0|1] "" XXX
 SG_ REQ_REVIEW_POWER_STATE2 : 83|1@0+ (1,0) [0|1] "" XXX
 SG_ GAS_SIGNAL : 86|1@0+ (1,0) [0|1] "" XXX
 SG_ REQ_REVIEW_POWER_STATE3 : 113|1@0+ (1,0) [0|1] "" XXX
 SG_ COUNTER1 : 123|4@0+ (1,0) [0|15] "" XXX
 SG_ CRC2 : 135|8@0+ (1,0) [0|255] "" XXX
 SG_ DRIVER_MODE_SIGNAL2 : 161|1@0+ (1,0) [0|1] "" XXX
 SG_ DRIVE_MODE_P : 162|1@0+ (1,0) [0|1] "" XXX
 SG_ DRIVE_MODE_D : 163|1@0+ (1,0) [0|1] "" XXX
 SG_ DRIVE_MODE_SIGNAL3 : 165|1@0+ (1,0) [0|1] "" XXX
 SG_ DRIVE_MODE : 166|2@0+ (1,0) [0|1] "" XXX
 SG_ BRAKE_SIGNAL : 176|1@0+ (1,0) [0|1] "" XXX
 SG_ COUNTER2 : 187|4@0+ (1,0) [0|15] "" XXX

BO_ 311 BRAKE: 64 XXX
 SG_ CRC1 : 71|8@0+ (1,0) [0|255] "" XXX
 SG_ REQ_REVIEW_DRIVE_MODE : 77|1@0+ (1,0) [0|1] "" XXX
 SG_ COUNTER1 : 123|4@0+ (1,0) [0|15] "" XXX
 SG_ CRC2 : 135|8@0+ (1,0) [0|255] "" XXX
 SG_ REQ_REVIEW_HANDBRAKE_ENABLED : 143|1@0+ (1,0) [0|1] "" XXX
 SG_ BRAKE_SIGNAL1 : 152|1@0+ (1,0) [0|1] "" XXX
 SG_ REVIEW_REQ_HANDBRAKE_RELEASED : 153|1@0+ (1,0) [0|1] "" XXX
 SG_ REVIEW_REQ_HANDBRAKE_ENABLED : 154|1@0+ (1,0) [0|1] "DURING POWER BRAKE IS FALSE" XXX
 SG_ BRAKE_SIGNAL2 : 155|1@0+ (1,0) [0|1] "" XXX
 SG_ COUNTER2 : 187|4@0+ (1,0) [0|15] "" XXX
 SG_ CRC3 : 199|8@0+ (1,0) [0|255] "" XXX
 SG_ BRAKE_PRESSURE : 207|13@0+ (1,0) [0|8191] "" XXX
 SG_ REQ_REVIEW_BRAKE_PRESSURE : 207|14@0+ (1,0) [0|1] "" XXX
 SG_ COUNTER3 : 251|4@0+ (1,0) [0|15] "" XXX
 SG_ CRC4 : 263|8@0+ (1,0) [0|255] "" XXX

BO_ 315 WHEEL_SPEEDS: 64 XXX
 SG_ FRONT_CRC : 7|8@0+ (1,0) [0|255] "" XXX
 SG_ FRONT_LEFT_WHEEL_SPEED : 12|13@0+ (0.05924739,0) [0|8191] "Kph" XXX
 SG_ FRONT_RIGHT_WHEEL_SPEED : 28|13@0+ (0.05924739,0) [0|8191] "Kph" XXX
 SG_ FRONT_DISTANCE_ACCUMULATOR : 47|16@0+ (1,0) [0|65535] "??" XXX
 SG_ FRONT_COUNTER : 59|4@0+ (1,0) [0|15] "" XXX
 SG_ REAR_CRC : 327|8@0+ (1,0) [0|255] "" XXX
 SG_ REAR_LEFT_WHEEL_SPEED : 332|13@0+ (0.05924739,0) [0|8191] "Kph" XXX
 SG_ REAR_RIGHT_WHEEL_SPEED : 348|13@0+ (0.05924739,0) [0|8191] "Kph" XXX
 SG_ REAR_DISTANCE_ACCUMULATOR : 367|16@0+ (1,0) [0|65535] "" XXX
 SG_ REAR_COUNTER : 379|4@0+ (1,0) [0|15] "" XXX

BO_ 323 SPEED2: 64 XXX
 SG_ NEW_SIGNAL_1 : 71|8@0+ (1,0) [0|255] "" XXX
 SG_ SPEED_REAL : 119|8@0+ (1,0) [0|255] "Kph" XXX
 SG_ COUNTER : 123|4@0+ (1,0) [0|15] "" XXX
 SG_ CRC2 : 135|8@0+ (1,0) [0|255] "" XXX

BO_ 327 NEW_MSG_147: 64 XXX

BO_ 347 NEW_MSG_15B: 64 XXX

BO_ 357 NEW_MSG_165: 8 XXX

BO_ 367 NEW_MSG_16F: 64 XXX

BO_ 412 LIGHTS: 16 XXX
 SG_ BRAKE_LIGHTS : 24|3@0+ (1,0) [0|7] "" XXX
 SG_ RIGHT_TURN_SIGNAL : 30|1@0+ (1,0) [0|1] "ON/OFF ACCORDINGLY LIGHT" XXX
 SG_ LEFT_TURN_SIGNAL : 31|1@0+ (1,0) [0|1] "ON/OFF ACCORDINGLY LIGHT" XXX

BO_ 415 NEW_MSG_19F: 64 XXX

BO_ 573 UNDEFINED_RELATIONSHIP_WITH_CAR_RUNNING: 64 XXX
 SG_ COUNTER1 : 123|4@0+ (1,0) [0|15] "" XXX
 SG_ CRC2 : 135|8@0+ (1,0) [0|255] "" XXX
 SG_ COUNTER2 : 187|12@0+ (1,0) [0|4095] "" XXX
 SG_ COUNTER3 : 251|4@0+ (1,0) [0|15] "" XXX
 SG_ CRC3 : 263|8@0+ (1,0) [0|255] "" XXX

BO_ 581 NEW_MSG_245: 8 XXX

BO_ 639 NEW_MSG_27F: 16 XXX
 SG_ CRC : 7|8@0+ (1,0) [0|255] "" XXX
 SG_ REQ_REVIEW_ODOMETER : 23|16@0+ (1,0) [0|65535] "" XXX
 SG_ COUNTER : 59|4@0+ (1,0) [0|15] "" XXX

BO_ 649 DRIVE_MODE: 64 XXX
 SG_ DRIVE_MODE_ENABLED : 382|1@0+ (1,0) [0|1] "" XXX
 SG_ DRIVE_MODE_DISABLED : 383|1@0+ (1,0) [0|1] "" XXX

BO_ 683 ACC: 64 XXX
 SG_ COUNTER1 : 123|4@0+ (1,0) [0|15] "" XXX
 SG_ CRC1 : 135|8@0+ (1,0) [0|255] "" XXX
 SG_ CAR_DISTANCE_SELECTION : 170|3@0+ (1,0) [0|7] "" XXX
 SG_ ACC_SPEED_SELECTION : 183|8@0+ (1,0) [0|255] "Kph" XXX
 SG_ COUNTER2 : 187|4@0+ (1,0) [0|15] "" XXX

BO_ 696 UNDEFINED_INTERESTING_SIGNALS: 64 XXX
 SG_ UNDEFINED_SIGNAL2 : 20|9@0+ (1,0) [0|511] "" XXX
 SG_ UNDEFINED_SIGNAL1 : 27|12@0+ (1,0) [0|4095] "" XXX
 SG_ UNDEFINED_ACCUMULATOR : 47|8@0+ (1,0) [0|255] "" XXX
 SG_ UNDEFINED_ACCUMULATOR2 : 47|12@0+ (1,0) [0|15] "" XXX
 SG_ UNDEFINED_SIGNAL3 : 51|12@0+ (1,0) [0|4095] "" XXX
 SG_ UNDEFINED_SIGNAL4 : 265|10@0+ (1,0) [0|1023] "" XXX
 SG_ NEW_SIGNAL_1 : 354|7@0+ (1,0) [0|127] "" XXX

BO_ 714 BUTTONS: 8 XXX
 SG_ LOCK_DOORS_BUTTON : 16|1@0+ (1,0) [0|1] "" XXX

BO_ 793 DOOR_DRIVER: 16 XXX
 SG_ REQ_REVIEW_CAR_STATE : 16|1@0+ (1,0) [0|1] "" XXX
 SG_ DOOR_REAR_RIGHT_OPEN : 19|1@0+ (1,0) [0|1] "" XXX
 SG_ DOOR_FRONT_RIGHT_OPEN : 20|1@0+ (1,0) [0|1] "" XXX
 SG_ DOOR_REAR_LEFT_OPEN : 21|1@0+ (1,0) [0|1] "" XXX
 SG_ DOOR_DRIVER_OPEN : 22|1@0+ (1,0) [0|1] "" XXX

BO_ 837 DOOR_LOCK_STATES: 16 XXX
 SG_ DOOR_LOCK_STATE : 13|1@0+ (1,0) [0|1] "" XXX
 SG_ DOOR_LOCK_STATE2 : 34|1@0+ (1,0) [0|1] "" XXX

BO_ 849 SEATBELT: 16 XXX
 SG_ CRC : 7|8@0+ (1,0) [0|255] "" XXX
 SG_ SEAT_BELT_DRIVER_STATE : 11|1@0+ (1,0) [0|1] "" XXX
 SG_ SEAT_BELT_DRIVER_STATE2 : 24|1@0+ (1,0) [0|1] "" XXX
 SG_ COUNTER : 59|4@0+ (1,0) [0|15] "" XXX

CM_ SG_ 96 GAS_POSITION "0 - 100%";
CM_ SG_ 96 BRAKE_SIGNAL "1 = BRAKE PRESSED";
CM_ SG_ 96 ACC_GAS_POSITION "0 - 100% | WHEN ACC REMAINS INFORMATION";
CM_ SG_ 96 REQ_REVIEW_POWER_CONSUMPTION2 "REQ FACTOR CONVERTER";
CM_ SG_ 96 REQ_REVIEW_POWER_STATE_SIGNAL "1 = OFF | 12 WARM-UP | 5 = ON";
CM_ SG_ 96 REQ_REVIEW_POWER_STATE_SIGNAL2 "1 = OFF | 0 = ON";
CM_ SG_ 147 IMPRECISE_SPEED "lower precision, reverse is positive as well";
CM_ SG_ 147 REQ_REVIEW_SPEED "INITIALLY ADJUSTED TO KPH BUT ITS NOT";
CM_ SG_ 147 REQ_REVIEW_SPEED2 "INITIALLY ADJUSTED TO KPH BUT ITS NOT";
CM_ SG_ 161 STEERING_ANGLE "0 CENTER, NO LEFT / RIGHT SIGNAL";
CM_ SG_ 161 STEERING_TORQUE "Peak identified 3680, 0 = stopped";
CM_ SG_ 161 AP_REDUCE_DISTANCE_COMMAND "AUTOPILOT STALK";
CM_ SG_ 161 AP_CANCEL_COMMAND "AUTOPILOT STALK - FRONT MOVEMENT = CANCEL COMMAND";
CM_ SG_ 161 AP_ENABLE_COMMAND "AUTOPILOT STALK - REAR MOVEMENT = ENABLE COMMAND";
CM_ SG_ 161 AP_DECREASE_SPEED_COMMAND "AUTOPILOT STALK - MOVEMENT";
CM_ SG_ 161 AP_INCREASE_SPEED_COMMAND "AUTOPILOT STALK - DOWN MOVEMENT]";
CM_ SG_ 259 UNDEFINED_ACCUMULATOR "WHEN ELETRIC MODE = 0, PERHAPS COMBUSTION ENGINE";
CM_ SG_ 259 UNDEFINED_COUNTER "0 WHEN ELECTRIC, PERHAPS COMBUSTION ENGINE";
CM_ SG_ 259 ODD_GAS_POSITION "17 = 0, 253 = FULL, SOMETIMES 4";
CM_ SG_ 259 SPEED "PRECISE, KPH, DASHBOARD SPEED, BIT LOWER THAN GPS SPEED";
CM_ SG_ 288 REVIEW_REQ_BRAKE_SIGNAL1 "0 | 128 SIMILAR TO BRAKE SIGNAL BUT ADDITIONAL DELAY";
CM_ SG_ 288 REQ_REVIEW_MOVING "1 = CAR IS MOVING";
CM_ SG_ 288 ODD_BRAKE_PRESSURE "ODD BRAKE PRESSURE, NEGATIVE/OFFSET";
CM_ SG_ 288 REVIEW_REQ_BRAKE_SIGNAL2 "0 | 128 BRAKE PRESSED";
CM_ SG_ 299 REQ_REVIEW_COUNTER "COUNTER NOT STABLE";
CM_ SG_ 299 AP_UNDEFINED_SIGNAL3 "PERHAPS HANDS ON SENSOR";
CM_ SG_ 299 AP_STATE "1 = ON | 0 = OFF";
CM_ SG_ 303 DRIVE_MODE_SIGNAL "D/R = 0 | N/P = 1";
CM_ SG_ 303 REQ_REVIEW_POWER_STATE "1 = ON | 0 = IDLE";
CM_ SG_ 303 REQ_REVIEW_POWER_STATE2 "1 = ON | 0 = IDLE";
CM_ SG_ 303 GAS_SIGNAL "1 = GAS PRESSED";
CM_ SG_ 303 REQ_REVIEW_POWER_STATE3 "0 = OFF / 1 = ON";
CM_ SG_ 303 DRIVER_MODE_SIGNAL2 "D/R = 1 | N/P=0";
CM_ SG_ 303 DRIVE_MODE_SIGNAL3 "P/R = 1 | P/N = 0";
CM_ SG_ 303 DRIVE_MODE "0 = P | 1 = D | 2 = N | 3 = R (GOOD SIGNAL)";
CM_ SG_ 303 BRAKE_SIGNAL "1 BRAKE PRESSED";
CM_ SG_ 311 REQ_REVIEW_DRIVE_MODE "1 = P | 0 = D";
CM_ SG_ 311 BRAKE_SIGNAL1 "0 = NO | 1 = BRAKE PRESSED";
CM_ SG_ 311 REVIEW_REQ_HANDBRAKE_RELEASED "DURING POWER BRAKE IS TRUE";
CM_ SG_ 311 BRAKE_SIGNAL2 "0 NO | 1 = BRAKE PRESSED";
CM_ SG_ 311 BRAKE_PRESSURE "MAX PRESSURE IDENTIFIED = 4184 | 0 = NO PRESSURE";
CM_ SG_ 311 REQ_REVIEW_BRAKE_PRESSURE "ADDITIONAL BIT";
CM_ SG_ 315 FRONT_LEFT_WHEEL_SPEED "Based on the car's dashboard speed";
CM_ SG_ 315 FRONT_RIGHT_WHEEL_SPEED "Based on the car's dashboard speed";
CM_ SG_ 315 FRONT_DISTANCE_ACCUMULATOR "?? doubts";
CM_ SG_ 315 REAR_LEFT_WHEEL_SPEED "Based on the car's dashboard speed";
CM_ SG_ 315 REAR_RIGHT_WHEEL_SPEED "Based on the car's dashboard speed";
CM_ SG_ 315 REAR_DISTANCE_ACCUMULATOR "PULSES";
CM_ SG_ 323 SPEED_REAL "PRECISE, EQUAL GPS SPEED";
CM_ SG_ 412 BRAKE_LIGHTS "POSSIBLE 3 DIFFERENT LIGHTS";
CM_ SG_ 412 LEFT_TURN_SIGNAL "ON/OFF ACCORDINGLY LIGHT";
CM_ BO_ 573 "APPEARS TO HAVE GOOD MOVING INFORMATION";
CM_ BO_ 649 "FRONT";
CM_ SG_ 649 DRIVE_MODE_ENABLED "DRIVE = 1 | OTHER MODES = 0";
CM_ SG_ 649 DRIVE_MODE_DISABLED "D = 0 | OTHER MODES = 0";
CM_ SG_ 683 CAR_DISTANCE_SELECTION "AUTOPILOT CAR DISTANCE SELECTED BY DRIVER, OPTIONS 1, 2, 3, 4";
CM_ BO_ 696 "POSSIBLE RELATED TO ACC OR ENGINE";
CM_ SG_ 714 LOCK_DOORS_BUTTON "1 = PRESSED";
CM_ SG_ 793 REQ_REVIEW_CAR_STATE "1 = STOPPED | 0 = MOVING";
CM_ SG_ 837 DOOR_LOCK_STATE "0 = LOCKED / 1 = UNLOCKED";
CM_ SG_ 837 DOOR_LOCK_STATE2 "0 = LOCKED / 1 = UNLOCKED";
CM_ SG_ 849 SEAT_BELT_DRIVER_STATE "0 = Fastened / 1 = Released";
CM_ SG_ 849 SEAT_BELT_DRIVER_STATE2 "0 = Fastened / 1 = Released";
