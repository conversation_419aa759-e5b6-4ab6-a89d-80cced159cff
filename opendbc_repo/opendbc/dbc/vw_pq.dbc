VERSION ""


NS_ :
	NS_DESC_
	CM_
	BA_DEF_
	BA_
	VAL_
	CAT_DEF_
	CAT_
	FILTER
	BA_DEF_DEF_
	EV_DATA_
	ENVVAR_DATA_
	SGTYPE_
	SGTYPE_VAL_
	BA_DEF_SGTYPE_
	BA_SGTYPE_
	SIG_TYPE_REF_
	VAL_TABLE_
	SIG_GROUP_
	SIG_VALTYPE_
	SIGTYPE_VALTYPE_

BS_:

BU_: XXX


BO_ 1394 ZAS_1: 2 XXX
 SG_ Fehlerspeichereintrag__ZAS_ : 15|1@1+ (1,0) [0|0] "" XXX
 SG_ Frei_ZAS_1_3 : 8|7@1+ (1,0) [0|0] "" XXX
 SG_ Frei_ZAS_1_2 : 7|1@1+ (1,0) [0|0] "" XXX
 SG_ Klemme_15_SV : 6|1@1+ (1,0) [0|0] "" XXX
 SG_ Frei_ZAS_1_1 : 5|1@1+ (1,0) [0|0] "" XXX
 SG_ Klemme_P__Parklichtstellung_ : 4|1@1+ (1,0) [0|0] "" XXX
 SG_ Klemme_50__Starten_ : 3|1@1+ (1,0) [0|0] "" XXX
 SG_ Klemme_X__Startvorgang_ : 2|1@1+ (1,0) [0|0] "" XXX
 SG_ Klemme_15__Z_ndung_ein_ : 1|1@1+ (1,0) [0|0] "" XXX
 SG_ S_Kontakt__Schl_ssel_steckt_ : 0|1@1+ (1,0) [0|0] "" XXX

BO_ 1336 Wischer_1: 2 XXX
 SG_ Blockierung_Heckwischer_erkannt : 15|1@1+ (1,0) [0|0] "" XXX
 SG_ Frei_Wischer_1_2 : 12|3@1+ (1,0) [0|0] "" XXX
 SG_ Fehlerspeichereintrag__Wischer_ : 11|1@1+ (1,0) [0|0] "" XXX
 SG_ Ansteuerung_Scheibenwischer_Hec : 10|1@1+ (1,0) [0|0] "" XXX
 SG_ Ansteuerung_Wascher_Heck : 9|1@1+ (1,0) [0|0] "" XXX
 SG_ Scheibenwischer_Heck_eingeschal : 8|1@1+ (1,0) [0|0] "" XXX
 SG_ Blockierung_Frontwischer_erkann : 7|1@1+ (1,0) [0|0] "" XXX
 SG_ Frei_Wischer_1_1 : 6|1@1+ (1,0) [0|0] "" XXX
 SG_ Status_Waschduesenheizung : 5|1@1+ (1,0) [0|0] "" XXX
 SG_ Parklage_Frontwischer : 4|1@1+ (1,0) [0|0] "" XXX
 SG_ Ansteuerung_Frontwischer_Schnel : 3|1@1+ (1,0) [0|0] "" XXX
 SG_ Ansteuerung_Frontwischer_Normal : 2|1@1+ (1,0) [0|0] "" XXX
 SG_ Ansteuerung_Wascher_Front : 1|1@1+ (1,0) [0|0] "" XXX
 SG_ Frontwischer__eingeschaltet : 0|1@1+ (1,0) [0|0] "" XXX

BO_ 1464 WFS_1: 2 XXX
 SG_ WFS_Textbits : 8|8@1+ (1,0) [0|0] "" XXX
 SG_ Frei_WFS_1_1 : 1|7@1+ (1,0) [0|0] "" XXX
 SG_ WFS_LED : 0|1@1+ (1,0) [0|0] "" XXX

BO_ 1096 Waehlhebel_1: 4 XXX
 SG_ Frei_Waehlhebel_1_1 : 29|3@1+ (1,0) [0|0] "" XXX
 SG_ Test_aktiv_Flag : 28|1@1+ (1,0) [0|0] "" XXX
 SG_ Zaehler_Waehlhebel_1 : 24|4@1+ (1,0) [0|15] "" XXX
 SG_ Waehlhebel_Testergebnis : 16|8@1+ (1,0) [0|255] "" XXX
 SG_ Fehler_Waehlhebel : 8|8@1+ (1,0) [0|0] "" XXX
 SG_ Waehlhebelposition : 4|4@1+ (1,0) [0|0] "" XXX
 SG_ Waehlhebel_Initialisierung : 3|1@1+ (1,0) [0|0] "" XXX
 SG_ Shiftlock_Position : 0|3@1+ (1,0) [0|0] "" XXX

BO_ 1496 Verbauliste_1: 8 XXX
 SG_ TV_Tuner : 63|1@1+ (1,0) [0|0] "" XXX
 SG_ DSP : 62|1@1+ (1,0) [0|0] "" XXX
 SG_ CD_Wechsler : 61|1@1+ (1,0) [0|0] "" XXX
 SG_ Spracheingabe : 60|1@1+ (1,0) [0|0] "" XXX
 SG_ Telematik : 59|1@1+ (1,0) [0|0] "" XXX
 SG_ Navigation : 58|1@1+ (1,0) [0|0] "" XXX
 SG_ Telefon : 57|1@1+ (1,0) [0|0] "" XXX
 SG_ Radio : 56|1@1+ (1,0) [0|0] "" XXX
 SG_ MMI_vorne : 55|1@1+ (1,0) [0|0] "" XXX
 SG_ MMI_hinten : 54|1@1+ (1,0) [0|0] "" XXX
 SG_ Frei_Verbauliste_1_11 : 53|1@1+ (1,0) [0|0] "" XXX
 SG_ Klimabedienteil_HL : 52|1@1+ (1,0) [0|0] "" XXX
 SG_ Frei_Verbauliste_1_10 : 50|2@1+ (1,0) [0|0] "" XXX
 SG_ Tankgeber : 49|1@1+ (1,0) [0|0] "" XXX
 SG_ Assistenzfahrlicht : 48|1@1+ (1,0) [0|0] "" XXX
 SG_ Memory_hinter_Fahrer : 47|1@1+ (1,0) [0|0] "" XXX
 SG_ Sitzmemory_hinten : 46|1@1+ (1,0) [0|0] "" XXX
 SG_ Sitzmemory_Beifahrer : 45|1@1+ (1,0) [0|0] "" XXX
 SG_ Anh_ngersteuergeraet : 44|1@1+ (1,0) [0|0] "" XXX
 SG_ Energiemanagement : 43|1@1+ (1,0) [0|0] "" XXX
 SG_ Wischermodul : 42|1@1+ (1,0) [0|0] "" XXX
 SG_ EZS___Kessy__Komfort_ : 41|1@1+ (1,0) [0|0] "" XXX
 SG_ Verdecksteuergeraet : 40|1@1+ (1,0) [0|0] "" XXX
 SG_ Standheizung : 39|1@1+ (1,0) [0|0] "" XXX
 SG_ Frei_Verbauliste_1_9 : 38|1@1+ (1,0) [0|0] "" XXX
 SG_ Einparkhilfe : 37|1@1+ (1,0) [0|0] "" XXX
 SG_ Klimasteuergeraet_Komfort : 36|1@1+ (1,0) [0|0] "" XXX
 SG_ Gateway : 35|1@1+ (1,0) [0|0] "" XXX
 SG_ Lenksaeulenmodul : 34|1@1+ (1,0) [0|0] "" XXX
 SG_ Reifendruck : 33|1@1+ (1,0) [0|0] "" XXX
 SG_ Kombiinstrument_Komfort : 32|1@1+ (1,0) [0|0] "" XXX
 SG_ Dachmodul : 31|1@1+ (1,0) [0|0] "" XXX
 SG_ Memory : 30|1@1+ (1,0) [0|0] "" XXX
 SG_ TSG_HR : 29|1@1+ (1,0) [0|0] "" XXX
 SG_ TSG_HL : 28|1@1+ (1,0) [0|0] "" XXX
 SG_ TSG_BT : 27|1@1+ (1,0) [0|0] "" XXX
 SG_ TSG_FT : 26|1@1+ (1,0) [0|0] "" XXX
 SG_ ZKE : 25|1@1+ (1,0) [0|0] "" XXX
 SG_ BSG_Komfort : 24|1@1+ (1,0) [0|0] "" XXX
 SG_ Frei_Verbauliste_1_8 : 19|5@1+ (1,0) [0|0] "" XXX
 SG_ Stabi_Entkopplung : 18|1@1+ (1,0) [0|0] "" XXX
 SG_ Waehlhebel : 17|1@1+ (1,0) [0|0] "" XXX
 SG_ Batteriemanager : 16|1@1+ (1,0) [0|0] "" XXX
 SG_ Daempfer_SG : 15|1@1+ (1,0) [0|0] "" XXX
 SG_ Niveauregulierung : 14|1@1+ (1,0) [0|0] "" XXX
 SG_ EZS___Kessy__Antrieb_ : 13|1@1+ (1,0) [0|0] "" XXX
 SG_ Bremsbooster : 12|1@1+ (1,0) [0|0] "" XXX
 SG_ Lenkhilfe : 11|1@1+ (1,0) [0|0] "" XXX
 SG_ Kombi_Antrieb : 10|1@1+ (1,0) [0|0] "" XXX
 SG_ Einspritzpumpe : 9|1@1+ (1,0) [0|0] "" XXX
 SG_ Lenkwinkel : 8|1@1+ (1,0) [0|0] "" XXX
 SG_ Allrad : 7|1@1+ (1,0) [0|0] "" XXX
 SG_ BSG_Antrieb : 6|1@1+ (1,0) [0|0] "" XXX
 SG_ ADR : 5|1@1+ (1,0) [0|0] "" XXX
 SG_ Airbag : 4|1@1+ (1,0) [0|0] "" XXX
 SG_ Climatronic_Antrieb : 3|1@1+ (1,0) [0|0] "" XXX
 SG_ ABS : 2|1@1+ (1,0) [0|0] "" XXX
 SG_ Getriebesteuergeraet : 1|1@1+ (1,0) [0|0] "" XXX
 SG_ Motorsteuergeraet : 0|1@1+ (1,0) [0|0] "" XXX

BO_ 1488 Systeminfo_1: 6 XXX
 SG_ CAN_Stand_4_1_Antrieb_Daten_Hau : 44|4@1+ (1,0) [0|15] "" XXX
 SG_ CAN_Stand_4_1_Antrieb_Daten_Neb : 40|4@1+ (1,0) [0|15] "" XXX
 SG_ CAN_Stand_4_1_Komfort_Daten_Hau : 36|4@1+ (1,0) [0|15] "" XXX
 SG_ CAN_Stand_4_1_Komfort_Daten_Neb : 32|4@1+ (1,0) [0|15] "" XXX
 SG_ Frei_Systeminfo_1_6 : 30|1@1+ (1,0) [0|0] "" XXX
 SG_ Viertuerer : 29|1@1+ (1,0) [0|0] "" XXX
 SG_ Rechtslenker : 28|1@1+ (1,0) [0|0] "" XXX
 SG_ Fahrzeug_Index : 24|4@1+ (1,0) [0|0] "" XXX
 SG_ Fahrzeug_Generation : 20|4@1+ (1,0) [0|15] "" XXX
 SG_ Fahrzeug_Derivat : 16|4@1+ (1,0) [0|15] "" XXX
 SG_ Fahrzeug_Marke_2 : 12|4@1+ (1,0) [0|15] "" XXX
 SG_ Fahrzeug_Klasse : 8|4@1+ (1,0) [0|15] "" XXX
 SG_ Verbauinformation_gueltig : 7|1@1+ (1,0) [0|0] "" XXX
 SG_ CAN_Infotainment_verbaut : 6|1@1+ (1,0) [0|0] "" XXX
 SG_ CAN_Infotainment_in_Diagnose : 5|1@1+ (1,0) [0|0] "" XXX
 SG_ Sleep_CAN_Infotainment : 4|1@1+ (1,0) [0|0] "" XXX
 SG_ CAN_Komfort_in_Diagnose : 3|1@1+ (1,0) [0|0] "" XXX
 SG_ Sleep_CAN_Komfort : 2|1@1+ (1,0) [0|0] "" XXX
 SG_ CAN_Antrieb_in_Diagnose : 1|1@1+ (1,0) [0|0] "" XXX
 SG_ CAN_Extern_zugeschaltet : 0|1@1+ (1,0) [0|0] "" XXX

BO_ 268 Slave_1: 8 XXX
 SG_ Delta_reduziertes_Sollmoment : 48|1@1+ (1,0) [0|0] "" XXX
 SG_ Delta_Drosselklappenwinkel : 32|16@1+ (1,0) [0|0] "" XXX
 SG_ Frei_Slave_1_1 : 25|7@1+ (1,0) [0|0] "" XXX
 SG_ Delta_Zuendwinkelausgabe : 24|1@1+ (1,0) [0|0] "" XXX
 SG_ Ubat_Freigabe_DVE__Slave_ : 23|1@1+ (1,0) [0|0] "" XXX
 SG_ Drosselklappe_Sollwertbegrenzun : 22|1@1+ (1,0) [0|0] "" XXX
 SG_ Einspritzverbot_lernen__Slave_ : 21|1@1+ (1,0) [0|0] "" XXX
 SG_ Master_erkannt__Slave_ : 20|1@1+ (1,0) [0|0] "" XXX
 SG_ Fehler_Momentenausgabe : 19|1@1+ (1,0) [0|0] "" XXX
 SG_ Fehler_Getriebe_Moment : 18|1@1+ (1,0) [0|0] "" XXX
 SG_ Fehler_Bremsenbotschaft : 17|1@1+ (1,0) [0|0] "" XXX
 SG_ Indiziertes_Istmoment__Slave_ : 0|16@1+ (0.0015259,0) [0|99.998] "%" XXX
 SG_ Timeout_Bremsenbotschaft : 0|1@1+ (1,0) [0|0] "" XXX

BO_ 1332 Sitz_info: 2 XXX
 SG_ Positionserkennung_Beifahrersit : 15|1@1+ (1,0) [0|0] "" XXX
 SG_ Beifahrersitz_im_vorderen_Dritt : 14|1@1+ (1,0) [0|0] "" XXX
 SG_ Positionserkennung_Fahrersitz_u : 13|1@1+ (1,0) [0|0] "" XXX
 SG_ Fahrersitz_im_vorderen_Drittel : 12|1@1+ (1,0) [0|0] "" XXX
 SG_ Zaehler_Sitzinfo : 8|4@1+ (1,0) [0|15] "" XXX
 SG_ Checksumme_Sitzinfo : 0|8@1+ (1,0) [0|0] "" XXX

BO_ 1810 PSG_3: 2 XXX
 SG_ Selbsttestergebnis : 0|16@1+ (1,0) [0|0] "" XXX

BO_ 1298 PSG_2: 8 XXX
 SG_ RAM_Inhalt_4 : 48|16@1+ (1,0) [0|0] "" XXX
 SG_ RAM_Inhalt_3 : 32|16@1+ (1,0) [0|0] "" XXX
 SG_ RAM_Inhalt_2 : 16|16@1+ (1,0) [0|0] "" XXX
 SG_ RAM_Inhalt_1 : 0|16@1+ (1,0) [0|0] "" XXX

BO_ 274 PSG_1: 8 XXX
 SG_ Pumpentemperatur__2_1_ : 48|16@1+ (0.0625,0) [0|4096] "K" XXX
 SG_ Pumpentemperatur__3_2_2_ : 44|12@1+ (1,0) [0|0] "" XXX
 SG_ Zylinderzaehler__3_2_2_ : 43|3@1+ (1,0) [1|8] "Zaehler" XXX
 SG_ Ansteuerdauer__3_2_2_ : 32|11@1+ (0.0469,0) [0|96] "NW" XXX
 SG_ Nockenwellendrehzahl__3_2_2_ : 20|12@1+ (4,0) [0|16380] "upm" XXX
 SG_ Pumpen_Statuswort__3_2_2_ : 0|20@1+ (1,0) [0|0] "" XXX

BO_ 1300 NOX_1: 8 XXX
 SG_ OBD_fuer_NOX : 56|8@1+ (1,0) [0|0] "" XXX
 SG_ Frei_NOX1_4 : 53|3@1+ (1,0) [0|0] "" XXX
 SG_ IP2 : 52|1@1+ (1,0) [0|0] "" XXX
 SG_ IP1 : 51|1@1+ (1,0) [0|0] "" XXX
 SG_ IP0 : 50|1@1+ (1,0) [0|0] "" XXX
 SG_ Sondenheizung_NOX : 49|1@1+ (1,0) [0|0] "" XXX
 SG_ Offsetkorrektur_NOX : 48|1@1+ (1,0) [0|0] "" XXX
 SG_ Frei_NOX1_3 : 43|5@1+ (1,0) [0|0] "" XXX
 SG_ Sauerstoff_binaer : 32|11@1+ (1,-200) [-200|1847] "mV" XXX
 SG_ Frei_NOX1_2 : 27|5@1+ (1,0) [0|0] "" XXX
 SG_ Sauerstoff_linear : 16|11@1+ (1,0) [0|2047] "1000/Lambd" XXX
 SG_ Frei_NOX1_1 : 11|5@1+ (1,0) [0|0] "" XXX
 SG_ NOX_Signal : 0|11@1+ (1,0) [0|2047] "ppm" XXX

BO_ 1424 Niveau_1: 6 XXX
 SG_ Beladungszustand : 40|8@1+ (1,0) [0|253] "Zuladung" XXX
 SG_ Fehlerspeichereintrag__Niveau_1 : 39|1@1+ (1,0) [0|0] "" XXX
 SG_ Systemstatus__Niveau_1_ : 38|1@1+ (1,0) [0|0] "" XXX
 SG_ Reserve_Fahrzeugart : 37|1@1+ (1,0) [0|0] "" XXX
 SG_ Fahrzeugart_Niveau : 36|1@1+ (1,0) [0|0] "" XXX
 SG_ Textbits__Niveau_1_ : 32|4@1+ (1,0) [0|0] "" XXX
 SG_ Verstellung_HL : 31|1@1+ (1,0) [0|0] "" XXX
 SG_ Verstellung_HR : 30|1@1+ (1,0) [0|0] "" XXX
 SG_ Verstellung_VL : 29|1@1+ (1,0) [0|0] "" XXX
 SG_ Verstellung_VR : 28|1@1+ (1,0) [0|0] "" XXX
 SG_ Absenkung_Fahrzeug : 27|1@1+ (1,0) [0|0] "" XXX
 SG_ Anhebung_Fahrzeug : 26|1@1+ (1,0) [0|0] "" XXX
 SG_ Verstellung_aktiv : 25|1@1+ (1,0) [0|0] "" XXX
 SG_ Kompressorlauf_in_Kuerze : 24|1@1+ (1,0) [0|0] "" XXX
 SG_ Frei_Niveau_1_5 : 23|1@1+ (1,0) [0|0] "" XXX
 SG_ Taster_Niveau : 22|1@1+ (1,0) [0|0] "" XXX
 SG_ Parkniveau : 21|1@1+ (1,0) [0|0] "" XXX
 SG_ Zwischenniveau : 20|1@1+ (1,0) [0|0] "" XXX
 SG_ Niveaustati : 16|4@1+ (1,0) [0|0] "" XXX
 SG_ MSG_Einschraenkung : 15|1@1+ (1,0) [0|0] "" XXX
 SG_ ESP_Beeinflussung : 14|1@1+ (1,0) [0|0] "" XXX
 SG_ Warnlampe_Niveau_1 : 13|1@1+ (1,0) [0|0] "" XXX
 SG_ Frei_Niveau_1_1 : 12|1@1+ (1,0) [0|0] "" XXX
 SG_ Zaehler_Niveau_1 : 8|4@1+ (1,0) [0|15] "Zaehler" XXX
 SG_ Checksumme_Niveau_1 : 0|8@1+ (1,0) [0|0] "" XXX

BO_ 1328 Navigation_1: 7 XXX
 SG_ Kreuzungstyp : 54|2@1+ (1,0) [0|3] "" XXX
 SG_ Entfernung_bis_Kreuzung : 48|6@1+ (5,0) [0|315] "m" XXX
 SG_ Entfernung_bis_Kurvenanfang : 40|8@1+ (1,0) [0|255] "m" XXX
 SG_ Voarusliegende_Kurvenrichtung : 39|1@1+ (1,0) [0|0] "" XXX
 SG_ Vorausliegender_Kurvenverlauf : 32|7@1+ (50,0) [0|6350] "m" XXX
 SG_ Fehler_Navigation : 31|1@1+ (1,0) [0|0] "" XXX
 SG_ Anzahl_Fahrbahnen__0_ist_unguel : 28|3@1+ (1,0) [1|7] "" XXX
 SG_ Strassentyp : 24|4@1+ (1,0) [0|15] "" XXX
 SG_ Laenderkennung : 16|8@1+ (1,0) [0|0] "" XXX
 SG_ Vorzeichen_Gierrate______ : 15|1@1+ (1,0) [0|0] "" XXX
 SG_ Gierratenfehler : 14|1@1+ (1,0) [0|0] "" XXX
 SG_ Gierrate : 0|14@1+ (0.01,0) [0|100] "deg/sek" XXX

BO_ 1792 MSG_3: 3 XXX
 SG_ MSG_Konfiguration : 16|8@1+ (1,0) [0|0] "" XXX
 SG_ Lage_des_OT_Impuls : 0|16@1+ (0.01172,-384) [-384|384] "KW" XXX

BO_ 1280 MSG_2: 8 XXX
 SG_ RAM_Adresse_4 : 48|16@1+ (1,0) [0|0] "" XXX
 SG_ RAM_Adresse_3 : 32|16@1+ (1,0) [0|0] "" XXX
 SG_ RAM_Adresse_2 : 16|16@1+ (1,0) [0|0] "" XXX
 SG_ Ram_Adresse_1 : 0|16@1+ (1,0) [0|0] "" XXX

BO_ 256 MSG_1: 8 XXX
 SG_ Kurbelwellendrehzahl__3_2_2_ : 56|8@1+ (1,0) [0|0] "" XXX
 SG_ Soll_Foerderbeginn_KW__3_2_2_ : 40|16@1+ (0.01172,-384) [-384|384] "KW" XXX
 SG_ Soll_Foerderbeginn_NW__3_2_2_ : 28|12@1+ (0.01172,0) [0|768] "degNW" XXX
 SG_ Soll_Voreinspritzung : 16|12@1+ (1,0) [0|0] "" XXX
 SG_ Soll_Einspritzmenge : 0|16@1+ (0.03125,0) [0|2047] "mg/H" XXX

BO_ 1796 Motor_NOX: 8 XXX
 SG_ Frei_Motor_NOX_1_2 : 24|40@1+ (1,0) [0|0] "" XXX
 SG_ Frei_Motor_NOX_1_1 : 19|5@1+ (1,0) [0|0] "" XXX
 SG_ Heizleistungsanforderung : 18|1@1+ (1,0) [0|0] "" XXX
 SG_ Offsetkorrektur_moeglich : 17|1@1+ (1,0) [0|0] "" XXX
 SG_ Betriebsbereich : 16|1@1+ (1,0) [0|0] "" XXX
 SG_ Abgastemperatur_NOX : 8|8@1+ (5,-40) [-40|1230] "C" XXX
 SG_ Abgasdruck_NOX : 0|8@1+ (5,600) [600|1870] "mbar" XXX

BO_ 900 Motor_Momente: 8 XXX
 SG_ Momentenangaben_ungenau__Moment : 35|2@1+ (1,0) [0|0] "" XXX

BO_ 1408 Motor_Flexia: 8 XXX
 SG_ Ansaugsystem m0 : 63|1@1+ (1,0) [0|0] "" XXX
 SG_ Hubraum m0 : 56|7@1+ (0.1,0) [0|12.7] "l" XXX
 SG_ Steigung_der_Befuellungskennlin m1 : 56|8@1+ (0.001,0) [0|0.255] "l/mm" XXX
 SG_ Anzahl_Zylinder m0 : 52|4@1+ (1,0) [0|15] "Vent./Zyl." XXX
 SG_ Bewertungsfaktor_Russindex_Turb m1 : 50|6@1+ (0.1,0) [0|6.3] "" XXX
 SG_ Anzahl_Ventile m0 : 49|3@1+ (1,0) [0|7] "Vent./Zyl." XXX
 SG_ Bewertungsfaktor_Verschleissind m1 : 44|6@1+ (0.1,0) [0|6.3] "" XXX
 SG_ Hersteller_Code m1 : 40|4@1+ (1,0) [0|15] "" XXX
 SG_ Motorleistung m0 : 40|9@1+ (1,0) [0|512] "KW" XXX
 SG_ Max_Drehmoment m0 : 32|8@1+ (10,0) [0|2550] "Nm" XXX
 SG_ Normierter_Verbrauch m1 : 32|8@1+ (10,0) [0|2550] "l/Zyl." XXX
 SG_ Oelniveauschwelle m1 : 24|8@1+ (0.25,0) [0|63.75] "cm" XXX
 SG_ Drehzahl_MaxNorm m0 : 24|8@1+ (100,0) [0|25500] "U/min" XXX
 SG_ Verschleissindex : 16|8@1+ (1,0) [0|254] "" XXX
 SG_ Russindex : 8|8@1+ (1,0) [0|254] "" XXX
 SG_ Verbrennungsart : 7|1@1+ (1,0) [0|0] "" XXX
 SG_ Frei_Motor_Flexia_1 : 6|1@1+ (1,0) [0|0] "" XXX
 SG_ Warm_Up_Cycle : 5|1@1+ (1,0) [0|0] "" XXX
 SG_ Driving_Cycle : 4|1@1+ (1,0) [0|0] "" XXX
 SG_ Zaehler_Motor_Flexia : 1|3@1+ (1,0) [0|15] "" XXX
 SG_ Multiplex_Schalter_Motor_Flexia M : 0|1@1+ (1,0) [0|0] "" XXX

BO_ 1416 Motor_7: 8 XXX
 SG_ Oltemperatur : 56|8@1+ (1,0) [0|0] "" XXX
 SG_ Frei_Motor_7_3 : 40|16@1+ (1,0) [0|0] "" XXX
 SG_ Ladedruck : 32|8@1+ (0.01,0) [0|2.54] "bar" XXX
 SG_ Vorzeichen_Motordrehzahlgradien : 31|1@1+ (1,0) [0|0] "" XXX
 SG_ Motordrehzahlgradient : 24|7@1+ (1,0) [0|126] "U/min" XXX
 SG_ Hoeheninfo__Motor_7_ : 16|8@1+ (0.00787,0) [0|2] "" XXX
 SG_ Klemme_DFM : 8|8@1+ (0.4,0) [0|101.6] "%" XXX
 SG_ PTC___Gluehstifte_ausgeschaltet : 5|3@1+ (1,0) [0|0] "" XXX
 SG_ Frei_Motor_7_1 : 4|1@1+ (1,0) [0|0] "" XXX
 SG_ Fehlerspeichereintrag__Motor_7_ : 3|1@1+ (1,0) [0|0] "" XXX
 SG_ Statusbit_Geschwindikeitsbegren : 2|1@1+ (1,0) [0|0] "" XXX
 SG_ Geschwindigkegrenzung_aktivierb : 1|1@1+ (1,0) [0|0] "" XXX
 SG_ Leerlauf_Solldrehzahl_auf_Max_W : 0|1@1+ (1,0) [0|0] "" XXX

BO_ 1160 Motor_6: 8 XXX
 SG_ Zaehler_Motor_6 : 60|4@1+ (1,0) [0|15] "" XXX
 SG_ Frei_Motor_6_4 : 58|2@1+ (1,0) [0|0] "" XXX
 SG_ ltemperaturschutz : 57|1@1+ (1,0) [0|0] "" XXX
 SG_ GRA_Bremseingriff_Freigabe : 56|1@1+ (1,0) [0|0] "" XXX
 SG_ Frei_Motor_6_3 : 48|8@1+ (1,0) [0|0] "" XXX
 SG_ Ruckmeldung_Momenten : 40|8@1+ (0.39,0) [0|100] "" XXX
 SG_ GRA_Sollbeschleunigung : 32|8@1+ (0.024,-3.984) [-3.984|2.112] "m/s2" XXX
 SG_ Hoeheninfo__Motor_6_ : 24|8@1+ (0.00787,0) [0|2] "" XXX
 SG_ Istmoment_f_r_Getriebe : 16|8@1+ (0.39,0) [0|99] "MDI" XXX
 SG_ Sollmoment_f_r_Getriebe : 8|8@1+ (0.39,0) [0|99] "MDI" XXX
 SG_ Checksumme_Motor_6 : 0|8@1+ (1,0) [0|0] "" XXX

BO_ 1152 Motor_5: 8 XXX
 SG_ CHECKSUM : 56|8@1+ (1,0) [0|0] "" XXX
 SG_ Motortext_Bits__4_1_ : 52|4@1+ (1,0) [0|0] "" XXX
 SG_ Doppelte_Momente : 51|1@1+ (1,0) [0|0] "" XXX
 SG_ GRA_Hauptschalter : 50|1@1+ (1,0) [0|0] "" XXX
 SG_ Anlasser_Ausspuren : 49|1@1+ (1,0) [0|0] "" XXX
 SG_ Anlasser_Freigabe : 48|1@1+ (1,0) [0|0] "" XXX
 SG_ Klimadrucksignal__Motor_5_ : 40|8@1+ (0.2,0) [0|50.8] "bar" XXX
 SG_ K_hlerluefteransteuerung : 32|8@1+ (0.4,0) [0|101.6] "%" XXX
 SG_ Verbrauch_Ueberlauf : 31|1@1+ (1,0) [0|0] "" XXX
 SG_ Kraftstoffverbrauchssignal : 16|15@1+ (1,0) [0|32767] "ul" XXX
 SG_ Klimakompressor_Leistungsreduzi : 15|1@1+ (1,0) [0|0] "" XXX
 SG_ Kennfeldkuehlung : 14|1@1+ (1,0) [0|0] "" XXX
 SG_ Klimakompressor_aus__Motor_5_ : 13|1@1+ (1,0) [0|0] "" XXX
 SG_ CAT_Warnung : 12|1@1+ (1,0) [0|0] "" XXX
 SG_ OBD_2_Lampe : 11|1@1+ (1,0) [0|0] "" XXX
 SG_ E_Gas_Lampe : 10|1@1+ (1,0) [0|0] "" XXX
 SG_ Vorgluehlampe__Motor_5_ : 9|1@1+ (1,0) [0|0] "" XXX
 SG_ Ladekontroll_Lampe : 8|1@1+ (1,0) [0|0] "" XXX
 SG_ Multiplex_Code M : 6|2@1+ (1,0) [0|0] "" XXX
 SG_ Multiplex_Info_norm__Verbrauch m3 : 0|6@1+ (20,0) [0|1260] "l/Zyl" XXX
 SG_ Multiplex_Info_Motortyp m2 : 0|6@1+ (1,0) [0|0] "" XXX
 SG_ Multiplex_Info_Drehzahl_MD_Max m1 : 0|6@1+ (100,0) [0|6300] "U/min" XXX
 SG_ Multiplex_Info_Max_Drehmoment m0 : 0|6@1+ (10,0) [0|630] "Nm" XXX

BO_ 896 Motor_3: 8 XXX
 SG_ Drosselklappenpoti : 56|8@1+ (0.4,0) [0|101.6] "%" XXX
 SG_ Motor_Wunschdrehzahl : 48|8@1+ (25,0) [0|6350] "U/min" XXX
 SG_ Motordrehzahlbeeinflussung : 40|8@1+ (0.392,0) [0|100] "%" XXX
 SG_ Kein_Start_Stop : 39|1@1+ (1,0) [0|0] "" XXX
 SG_ Kein_E_Gas : 38|1@1+ (1,0) [0|0] "" XXX
 SG_ Reserviert_Motor_3_1 : 37|1@1+ (1,0) [0|0] "" XXX
 SG_ Vorzeichen_Rad_Wunschmoment : 36|1@1+ (1,0) [0|0] "" XXX
 SG_ Rad_Wunschmoment : 24|12@1+ (0.39,0) [0|1597] "MDI" XXX
 SG_ Fahrpedal_Rohsignal : 16|8@1+ (0.4,0) [0|101.6] "%" XXX
 SG_ Ansauglufttemperatur : 8|8@1+ (0.75,-48) [-48|142.5] "" XXX
 SG_ Fehlerstatus_Ansauglufttemperat : 7|1@1+ (1,0) [0|0] "" XXX
 SG_ Motorsteuerger_t_gesperrt : 6|1@1+ (1,0) [0|0] "" XXX
 SG_ Drosselklappenwinkel_ungenau : 5|1@1+ (1,0) [0|0] "" XXX
 SG_ Fahrpedalwert_ungenau__Motor_3_ : 4|1@1+ (1,0) [0|0] "" XXX
 SG_ Frei_Motor_3_1 : 3|1@1+ (1,0) [0|0] "" XXX
 SG_ Motor_Wunschdrehzahl_Priorit_t : 2|1@1+ (1,0) [0|0] "" XXX
 SG_ Uebertemperaturschutz__Motor_3_ : 1|1@1+ (1,0) [0|0] "" XXX
 SG_ Vorgluehmeldung : 0|1@1+ (1,0) [0|0] "" XXX

BO_ 648 Motor_2: 8 XXX
 SG_ Minimales_Motormoment_bei_Zuend : 56|8@1+ (0.39,0) [0|99] "MDI" XXX
 SG_ Begrenzungsmoment : 48|8@1+ (0.39,0) [0|99] "MDI" XXX
 SG_ Leerlaufsolldrehzahl__Motor_2_ : 40|8@1+ (10,0) [0|2540] "U/min" XXX
 SG_ Soll_Geschwindigkeit_bei_GRA_Be : 32|8@1+ (1.28,0) [0|325] "km/h" XXX
 SG_ Fahrzeuggeschwindigkeit : 24|8@1+ (1.28,0) [0|325] "km/h" XXX
 SG_ GRA_Status : 22|2@1+ (1,0) [0|0] "" XXX
 SG_ OBD_2_freeze_frame : 21|1@1+ (1,0) [0|0] "" XXX
 SG_ Status_Normalbetrieb : 20|1@1+ (1,0) [0|0] "" XXX
 SG_ Ansteuerung_Klima__4_1_ : 19|1@1+ (1,0) [0|0] "" XXX
 SG_ Fehlerstatus_Kuhlmitteltempera : 18|1@1+ (1,0) [0|0] "" XXX
 SG_ Bremstestschalter : 17|1@1+ (1,0) [0|0] "" XXX
 SG_ Bremslichtschalter : 16|1@1+ (1,0) [0|0] "" XXX
 SG_ Kuehlmitteltemperatur__Motor_2_ : 8|8@1+ (0.75,-48) [-48|142.5] "" XXX
 SG_ Multiplex_Code_Motor_2 M : 6|2@1+ (1,0) [0|0] "" XXX
 SG_ Multiplex_Info_Motorcode__4_x_ m1 : 0|6@1+ (1,0) [0|0] "" XXX
 SG_ Multiplex_Info_Getriebecode m2 : 0|6@1+ (1,0) [0|0] "" XXX
 SG_ Multiplex_Info_Max_Moment__Norm m3 : 0|6@1+ (10,0) [0|630] "Nm" XXX
 SG_ Multiplex_Info_CAN_Stand m0 : 0|6@1+ (1,0) [0|0] "" XXX

BO_ 640 Motor_1: 8 XXX
 SG_ Fahrerwunschmoment : 56|8@1+ (0.39,0) [0|99] "MDI" XXX
 SG_ mechanisches_Motor_Verlustmomen : 48|8@1+ (0.39,0) [0|99] "MDI" XXX
 SG_ Fahrpedalwert_oder_Drosselklapp : 40|8@1+ (0.4,0) [0|101.6] "%" XXX
 SG_ inneres_Motor_Moment_ohne_exter : 32|8@1+ (0.39,0) [0|99] "MDI" XXX
 SG_ Motordrehzahl : 16|16@1+ (0.25,0) [0|16256] "U/min" XXX
 SG_ inneres_Motor_Moment : 8|8@1+ (0.39,0) [0|99] "MDI" XXX
 SG_ Momentenangaben_ungenau : 7|1@1+ (1,0) [0|0] "" XXX
 SG_ Fehlerstatus_Getriebe_Momentene : 6|1@1+ (1,0) [0|0] "" XXX
 SG_ Fehlerstatus_Brems_Momenteneing : 5|1@1+ (1,0) [0|0] "" XXX
 SG_ Time_Out_Bremsen_Botschaft : 4|1@1+ (1,0) [0|0] "" XXX
 SG_ Kupplungsschalter : 3|1@1+ (1,0) [0|0] "" XXX
 SG_ Kickdownschalter : 2|1@1+ (1,0) [0|0] "" XXX
 SG_ Fahrpedalwert_ungenau__Motor_1_ M : 1|1@1+ (1,0) [0|0] "" XXX
 SG_ Leergasinformation : 0|1@1+ (1,0) [0|0] "" XXX

BO_ 262 Master_3: 8 XXX
 SG_ Frei_Master_3_1 : 56|8@1+ (1,0) [0|0] "" XXX
 SG_ Motortemperatur_linearisiert : 48|8@1+ (0.75,-48) [-48|143.25] "" XXX
 SG_ Indiziertes_Sollmoment_f_r_Vmax : 32|16@1+ (0.0015259,0) [0|100] "%" XXX
 SG_ Relative_Momentenanforderung_de : 16|16@1+ (0.003052,0) [0|200] "%" XXX
 SG_ Delta_Motormoment_aus_Verlustmo : 0|16@1+ (0.003052,-100) [-100|100] "%" XXX

BO_ 260 Master_2: 8 XXX
 SG_ Ubat_Freigabe_DVE : 45|1@1+ (1,0) [0|0] "" XXX
 SG_ DK_Sollwertbegrenzung : 44|1@1+ (1,0) [0|0] "" XXX
 SG_ Einspritzverbot_DV_E_lernen : 43|1@1+ (1,0) [0|0] "" XXX
 SG_ Master_erkannt : 42|1@1+ (1,0) [0|0] "" XXX
 SG_ Vmax_Begrenzung_aktiv : 41|1@1+ (1,0) [0|0] "" XXX
 SG_ SA_Verbot_von_FGR : 40|1@1+ (1,0) [0|0] "" XXX
 SG_ Pedalwert_Komplement : 24|16@1+ (1,0) [0|0] "" XXX
 SG_ Zaehler__Master_2_ : 16|8@1+ (1,0) [0|255] "" XXX
 SG_ Normierter_Fahrpedalwinkel : 0|16@1+ (0.001526,0) [0|100] "%" XXX

BO_ 258 Master_1: 8 XXX
 SG_ Stationaere_Solldrehzahl : 56|8@1+ (10,0) [0|2550] "Umin" XXX
 SG_ Drehmoment_LLR__I_Anteil_ : 40|16@1+ (0.003052,-100) [-100|100] "%" XXX
 SG_ Drehmoment_LLR__PD_Anteil_ : 24|16@1+ (0.003052,-100) [-100|100] "%" XXX
 SG_ Drehmoment_LLR__Luftpfad_ : 8|16@1+ (0.003052,-100) [-100|100] "%" XXX
 SG_ Frei_Master_1_1 : 4|4@1+ (1,0) [0|0] "" XXX
 SG_ LLR_ist_aktiv : 3|1@1+ (1,0) [0|0] "" XXX
 SG_ I_Anteil_der_LLR_aktiv : 2|1@1+ (1,0) [0|0] "" XXX
 SG_ PD_Anteil_der_LLR_aktiv : 1|1@1+ (1,0) [0|0] "" XXX
 SG_ LLR_Freigabe_nach_Start : 0|1@1+ (1,0) [0|0] "" XXX

BO_ 1984 LWS_Kalibrierung: 2 XXX
 SG_ Frei_LWS_Kalibrierung_1_1 : 15|1@1+ (1,0) [0|0] "" XXX
 SG_ LWS_Identifier : 8|7@1+ (1,0) [0|0] "" XXX
 SG_ Frei_LWS_Kalibrierung_1_2 : 4|4@1+ (1,0) [0|0] "" XXX
 SG_ Command_Dode_Word : 0|4@1+ (1,0) [0|0] "" XXX

BO_ 1986 Lenkwinkel_Init: 4 XXX
 SG_ Kodierbytes : 8|24@1+ (1,0) [0|0] "" XXX
 SG_ Identiifier_f_r_LWS_Init : 0|8@1+ (1,0) [0|0] "" XXX

BO_ 192 Lenkwinkel_1__RB_: 2 XXX
 SG_ Vorzeichen__RB_ : 15|1@1+ (1,0) [0|0] "" XXX
 SG_ Lenkwinkel__RB_ : 5|10@1+ (2.5,-720) [-720|720] "" XXX
 SG_ LWS_OK__RB_ : 4|1@1+ (1,0) [0|0] "" XXX
 SG_ LWS_Abgleich__RB_ : 3|1@1+ (1,0) [0|0] "" XXX
 SG_ Frei_Lenkwinkel_1_1__RB_ : 2|1@1+ (1,0) [0|0] "" XXX
 SG_ FINE_CHECK__RB_ : 1|1@1+ (1,0) [0|0] "" XXX
 SG_ COARSE_CHECK__RB_ : 0|1@1+ (1,0) [0|0] "" XXX

BO_ 196 Lenkwinkel_1__ITT_: 2 XXX
 SG_ Vorzeichen__ITT_ : 15|1@1+ (1,0) [0|0] "" XXX
 SG_ Lenkwinkel__ITT_ : 5|10@1+ (1.5,-768) [-768|766.5] "" XXX
 SG_ LWS_OK : 4|1@1+ (1,0) [0|0] "" XXX
 SG_ LWS_Abgleich__ITT_ : 3|1@1+ (1,0) [0|0] "" XXX
 SG_ LWS_Initialisierung__ITT_ : 2|1@1+ (1,0) [0|0] "" XXX
 SG_ FINE_CHECK__ITT_ : 1|1@1+ (1,0) [0|0] "" XXX
 SG_ COARSE_CHECK__ITT_ : 0|1@1+ (1,0) [0|0] "" XXX

BO_ 194 Lenkwinkel_1: 8 XXX
 SG_ Checksumme_Lenkwinkel_1 : 56|8@1+ (1,0) [0|0] "" XXX
 SG_ Kodierdaten : 48|8@1+ (1,0) [0|0] "" XXX
 SG_ Zaehler_Lenkwinkel_1 : 44|4@1+ (1,0) [0|15] "" XXX
 SG_ Status_KL30_Ausfall__z_Zt__nur : 43|1@1+ (1,0) [0|0] "" XXX
 SG_ Interner_Status : 41|2@1+ (1,0) [0|0] "" XXX
 SG_ Frei_Lenkwinkel_1_1 : 40|1@1+ (1,0) [0|0] "" XXX
 SG_ Lenkwinkel_ID : 32|8@1+ (1,0) [0|0] "" XXX
 SG_ Lenkradwinkel_Geschwindigkeit_S : 31|1@1+ (1,0) [0|0] "" XXX
 SG_ Lenkradwinkel_Geschwindigkeit : 16|15@1+ (0.04375,0) [0|1433.6] "Grad/Sek" XXX
 SG_ Lenkradwinkel_Sign : 15|1@1+ (1,0) [0|0] "" XXX
 SG_ Lenkradwinkel : 0|15@1+ (0.04375,0) [0|1433.6] "Grad" XXX

BO_ 1502 Lenkhilfe_Fehler: 7 XXX
 SG_ Werkstattcode : 48|8@1+ (1,0) [0|0] "" XXX
 SG_ Multiplex_Signal : 45|3@1+ (1,0) [0|7] "" XXX
 SG_ Sensorcodierung_Lenkhilfe : 44|1@1+ (1,0) [0|0] "" XXX
 SG_ Kennliniencodierung_Lenkhilfe : 40|4@1+ (1,0) [0|15] "Kennlinie" XXX
 SG_ Geber_f__Lenkw__Speicher_ausles : 39|1@1+ (1,0) [0|0] "" XXX
 SG_ Kombiinstr__Speicher_auslesen : 38|1@1+ (1,0) [0|0] "" XXX
 SG_ Steuerger__Speicher_auslesen : 37|1@1+ (1,0) [0|0] "" XXX
 SG_ Lenkhilfe_Steuergeraet_defekt : 36|1@1+ (1,0) [0|0] "" XXX
 SG_ Frei_Lenkhilfe_1_3 : 32|4@1+ (1,0) [0|0] "" XXX
 SG_ Frei_Lenkhilfe_1_4 : 30|2@1+ (1,0) [0|0] "" XXX
 SG_ Geber_f__Lenkwi__k__CAN_Komm_sp : 29|1@1+ (1,0) [0|0] "" XXX
 SG_ Geber_f__Lenkwinkel_k__CAN_Komm : 28|1@1+ (1,0) [0|0] "" XXX
 SG_ Kombiinstr__k__CAN_Kommunik__sp : 27|1@1+ (1,0) [0|0] "" XXX
 SG_ Kombiinstrument_k__CAN_Kommunik : 26|1@1+ (1,0) [0|0] "" XXX
 SG_ Steuergeraet_k__CAN_Kommunik__S : 25|1@1+ (1,0) [0|0] "" XXX
 SG_ Steuergeraet_keine_CAN_Kommunik : 24|1@1+ (1,0) [0|0] "" XXX
 SG_ Lenkhilfe_Spannung_Ks__nach_Mas : 23|1@1+ (1,0) [0|0] "" XXX
 SG_ Lenkhilfe_Spannung_Ks_nach_Mass : 22|1@1+ (1,0) [0|0] "" XXX
 SG_ Lenkhilfe_Spannung_Ks__nach___s : 21|1@1+ (1,0) [0|0] "" XXX
 SG_ Lenkhilfe_Spannung_Ks__nach__ : 20|1@1+ (1,0) [0|0] "" XXX
 SG_ Lenkhilfe_Temperaturschutz_sp_ : 19|1@1+ (1,0) [0|0] "" XXX
 SG_ Lenkhilfe_Temperaturschutz : 18|1@1+ (1,0) [0|0] "" XXX
 SG_ Lenkhilfe_Betrieb_unpl__Sig__sp : 17|1@1+ (1,0) [0|0] "" XXX
 SG_ Lenkhilfe_Betrieb_unpl__Signal : 16|1@1+ (1,0) [0|0] "" XXX
 SG_ Lenkhilfesensor_k__Kommunik__sp : 15|1@1+ (1,0) [0|0] "" XXX
 SG_ Lenkhilfesensor_keine_Kommunik_ : 14|1@1+ (1,0) [0|0] "" XXX
 SG_ Lenkhilfesensor_defekt_sp_ : 13|1@1+ (1,0) [0|0] "" XXX
 SG_ Lenkhilfesensor_defekt : 12|1@1+ (1,0) [0|0] "" XXX
 SG_ Lenkhilfesensor_Unterbrechung_s : 11|1@1+ (1,0) [0|0] "" XXX
 SG_ Lenkhilfesensor_Unterbrechung : 10|1@1+ (1,0) [0|0] "" XXX
 SG_ Lenkhilfesensor_Ks_nach_Masse_s : 9|1@1+ (1,0) [0|0] "" XXX
 SG_ Lenkhifesensor_Ks_nach_Masse : 8|1@1+ (1,0) [0|0] "" XXX
 SG_ Spannung_Kl_15_zu_klein_sp_ : 7|1@1+ (1,0) [0|0] "" XXX
 SG_ Spannung_Kl_15_zu_klein : 6|1@1+ (1,0) [0|0] "" XXX
 SG_ Frei_Lenkhilfe_1_5 : 4|2@1+ (1,0) [0|0] "" XXX
 SG_ Spannung_Kl_30_zu_klein_sp_ : 3|1@1+ (1,0) [0|0] "" XXX
 SG_ Spannung_Kl_30_zu_klein : 2|1@1+ (1,0) [0|0] "" XXX
 SG_ Spannung_Kl_30_zu_gro__sp_ : 1|1@1+ (1,0) [0|0] "" XXX
 SG_ Spannung_Kl_30_zu_gro_ : 0|1@1+ (1,0) [0|0] "" XXX

BO_ 976 Lenkhilfe_1: 2 XXX
 SG_ Fehlerspeichereintrag__Lenkhilf : 15|1@1+ (1,0) [0|0] "" XXX
 SG_ Frei_Lenkhilfe_1_2 : 9|6@1+ (1,0) [0|0] "" XXX
 SG_ Fehlerlampe_Lenkhilfe : 8|1@1+ (1,0) [0|0] "" XXX
 SG_ Fehlerstatus_Lastinformation : 7|1@1+ (1,0) [0|0] "" XXX
 SG_ Lastinformation : 0|7@1+ (1,0) [0|127] "A" XXX

BO_ 1312 Kombi_3: 8 XXX
 SG_ Frei_Kombi_3_2 : 60|4@1+ (1,0) [0|0] "" XXX
 SG_ Kilometerstand : 40|20@1+ (1,0) [0|1000000] "km" XXX
 SG_ Fehlerstatus_Standzeit : 39|1@1+ (1,0) [0|0] "" XXX
 SG_ Standzeit : 24|15@1+ (4,0) [0|131068] "sec" XXX
 SG_ Frei_Kombi_3_1 : 20|4@1+ (1,0) [0|0] "" XXX
 SG_ Schluesselinfo : 16|4@1+ (1,0) [0|15] "" XXX
 SG_ Kombi_Multiplex_Code M : 14|2@1+ (1,0) [0|0] "" XXX
 SG_ Kombi_Verbauliste_Niveauregulie m3 : 11|1@1+ (1,0) [0|0] "" XXX
 SG_ Kombi_Multiplex_Marke m1 : 11|3@1+ (1,0) [0|0] "" XXX
 SG_ Kombi_Verbauliste_Lenkhilfe m3 : 10|1@1+ (1,0) [0|0] "" XXX
 SG_ Kombi_Verbauliste_Dieselpumpe m3 : 9|1@1+ (1,0) [0|0] "" XXX
 SG_ Kombi_Verbauliste_Lenkwinkel m3 : 8|1@1+ (1,0) [0|0] "" XXX
 SG_ Kombi_Multiplex_Baureihe m1 : 8|3@1+ (1,0) [0|0] "" XXX
 SG_ Kombi_Multiplex_Laendervariante m0 : 8|6@1+ (1,0) [0|0] "" XXX
 SG_ Kombi_Verbauliste_Allrad m3 : 7|1@1+ (1,0) [0|0] "" XXX
 SG_ Kombi_Verbauliste_Bordnetz m3 : 6|1@1+ (1,0) [0|0] "" XXX
 SG_ Kombi_Verbauliste_ACC m3 : 5|1@1+ (1,0) [0|0] "" XXX
 SG_ Kombi_Verbauliste_Airbag m3 : 4|1@1+ (1,0) [0|0] "" XXX
 SG_ Kombi_Multiplex_Generation m1 : 4|4@1+ (1,0) [0|0] "" XXX
 SG_ Kombi_Verbauliste_Klima m3 : 3|1@1+ (1,0) [0|0] "" XXX
 SG_ Kombi_Verbauliste_ABS m3 : 2|1@1+ (1,0) [0|0] "" XXX
 SG_ Kombi_Verbauliste_Motor m3 : 0|1@1+ (1,0) [0|0] "" XXX
 SG_ Kombi_Multiplex_Derivat m1 : 0|4@1+ (1,0) [0|0] "" XXX
 SG_ Kombi_Multiplex_Sprachvariante m0 : 0|8@1+ (1,0) [0|0] "" XXX
 SG_ Kombi_Multiplex_Reifenumfang m2 : 0|12@1+ (1,0) [0|4095] "mm" XXX

BO_ 1056 Kombi_2: 8 XXX
 SG_ Frei_Kombi_2_2 : 56|8@1+ (1,0) [0|0] "" XXX
 SG_ Fehlerstatus_Kl__58_s : 55|1@1+ (1,0) [0|0] "" XXX
 SG_ Klemme_58s__Kombi_2_ : 48|7@1+ (1,0) [0|100] "%" XXX
 SG_ Fehlerstatus_Kl__58_d : 47|1@1+ (1,0) [0|0] "" XXX
 SG_ Klemme_58d__Kombi_2_ : 40|7@1+ (1,0) [0|100] "%" XXX
 SG_ Kuehlmitteltemp__4_1__Kombi_2_ : 32|8@1+ (0.75,-48) [-48|142.5] "C" XXX
 SG_ Oeltemperatur_4_1 : 24|8@1+ (1,-60) [-60|194] "C" XXX
 SG_ Aussentemp__ungefiltert_4_1__Ko : 16|8@1+ (0.5,-50) [-50|77] "C" XXX
 SG_ Aussentemperatur_gefiltert : 8|8@1+ (0.5,-50) [-50|77] "C" XXX
 SG_ Fehlerspeichereintrag__Kombi_ : 7|1@1+ (1,0) [0|0] "" XXX
 SG_ Frei_Kombi_2_1 : 4|3@1+ (1,0) [0|0] "" XXX
 SG_ Anhaenger_erkannt : 3|1@1+ (1,0) [0|0] "" XXX
 SG_ Fehlerst__Kuehlmitteltemp__4_1 : 2|1@1+ (1,0) [0|0] "" XXX
 SG_ Fehlerstatus_Oeltemperatur_4_1 : 1|1@1+ (1,0) [0|0] "" XXX
 SG_ Fehlerstatus_Aussentemp__4_1 : 0|1@1+ (1,0) [0|0] "" XXX

BO_ 800 Kombi_1: 8 XXX
 SG_ Frei_Kombi_1_3 : 56|8@1+ (1,0) [0|0] "" XXX
 SG_ Angezeigte_Geschwindigkeit : 46|10@1+ (0.32,0) [0|325] "km/h" XXX
 SG_ Blinker_rechts_4_1 : 45|1@1+ (1,0) [0|0] "" XXX
 SG_ Blinker_links_4_1 : 44|1@1+ (1,0) [0|0] "" XXX
 SG_ Gesetzte_Zeitluecke__Kombi_1_ : 43|1@1+ (1,0) [0|0] "" XXX
 SG_ ADR_Summer_abgeschaltet : 42|1@1+ (1,0) [0|0] "" XXX
 SG_ Frei_Kombi_1_2 : 40|2@1+ (1,0) [0|0] "" XXX
 SG_ Geschwindigkeit__Kombi_1_ : 25|15@1+ (0.01,0) [0|326] "km/h" XXX
 SG_ Signalquelle_Geschwindigkeit_4_ : 24|1@1+ (1,0) [0|0] "" XXX
 SG_ Tankwarnung : 23|1@1+ (1,0) [0|0] "" XXX
 SG_ Tankinhalt : 16|7@1+ (1,0) [0|126] "l" XXX
 SG_ Tankstop : 15|1@1+ (1,0) [0|0] "" XXX
 SG_ Frei_Kombi_1_7 : 12|3@1+ (1,0) [0|0] "" XXX
 SG_ Kombi_im_Stellgliedtest : 11|1@1+ (1,0) [0|0] "" XXX
 SG_ Ladekontroll_Lampe__Kombi_ : 10|1@1+ (1,0) [0|0] "" XXX
 SG_ Bremsinfo : 8|2@1+ (1,0) [0|0] "" XXX
 SG_ Vorgluehlampe__Kombi_1_ : 7|1@1+ (1,0) [0|0] "" XXX
 SG_ Tankwarnlampe : 6|1@1+ (1,0) [0|0] "" XXX
 SG_ Heissleuchten_Vorwarnung : 5|1@1+ (1,0) [0|0] "" XXX
 SG_ Kuehlmittelmangel : 4|1@1+ (1,0) [0|0] "" XXX
 SG_ Dynamische_Oeldruckwarnung : 3|1@1+ (1,0) [0|0] "" XXX
 SG_ Oeldruck : 2|1@1+ (1,0) [0|0] "" XXX
 SG_ Fehlerstatus_Tank : 1|1@1+ (1,0) [0|0] "" XXX
 SG_ Fahrertuer_4_1 : 0|1@1+ (1,0) [0|0] "" XXX

BO_ 1504 Klima_1: 8 XXX
 SG_ Aussentemp__ungef__Sto_f__4_1 : 56|8@1+ (0.5,-50) [-50|77] "C" XXX
 SG_ Fehlerspeichereintrag__Klima_ : 55|1@1+ (1,0) [0|0] "" XXX
 SG_ Frei_Klima_1_5 : 50|5@1+ (1,0) [0|0] "" XXX
 SG_ AC_Schalter : 49|1@1+ (1,0) [0|0] "" XXX
 SG_ Temperatureinheit : 48|1@1+ (1,0) [0|0] "" XXX
 SG_ Kuehlerluefteransteuerung__Klim : 40|8@1+ (0.4,0) [0|101.6] "%" XXX
 SG_ Geblaeselast_4_1 : 32|8@1+ (0.4,0) [0|101.6] "%" XXX
 SG_ Kompressorlast : 24|8@1+ (0.25,0) [0|63.5] "Nm" XXX
 SG_ Klimadrucksignal__Klima_1_ : 16|8@1+ (0.2,0) [0|50.8] "bar" XXX
 SG_ Aussentemp__ungef__4_1__Klima_1 : 8|8@1+ (0.5,-50) [-50|77] "C" XXX
 SG_ Kaeltemitteldruck_veraltet : 7|1@1+ (1,0) [0|0] "" XXX
 SG_ Kompressormoment_veraltet_4_1 : 6|1@1+ (1,0) [0|0] "" XXX
 SG_ Keine_Heizleistg_gewuenscht_4_1 : 5|1@1+ (1,0) [0|0] "" XXX
 SG_ Kompressorzustand__4_1_ : 4|1@1+ (1,0) [0|0] "" XXX
 SG_ Heizbare_Frontscheibe : 3|1@1+ (1,0) [0|0] "" XXX
 SG_ Heizbare_Heckscheibe : 2|1@1+ (1,0) [0|0] "" XXX
 SG_ Fahrerwunsch_Zuheizer : 1|1@1+ (1,0) [0|0] "" XXX
 SG_ Drehzahlanhebung : 0|1@1+ (1,0) [0|0] "" XXX

BO_ 906 GRA_Neu: 4 XXX
 SG_ CHECKSUM : 0|8@1+ (1,0) [0|255] "" XXX
 SG_ GRA_Hauptschalt : 8|1@1+ (1,0) [0|1] "" XXX
 SG_ GRA_Abbrechen : 9|1@1+ (1,0) [0|1] "" XXX
 SG_ GRA_Down_kurz : 10|1@1+ (1,0) [0|1] "" XXX
 SG_ GRA_Up_kurz : 11|1@1+ (1,0) [0|1] "" XXX
 SG_ GRA_Down_lang : 12|1@1+ (1,0) [0|1] "" XXX
 SG_ GRA_Up_lang : 13|1@1+ (1,0) [0|1] "" XXX
 SG_ GRA_Fehler_Bed : 14|1@1+ (1,0) [0|1] "" XXX
 SG_ GRA_Kodierinfo : 15|1@1+ (1,0) [0|1] "" XXX
 SG_ GRA_Neu_Setzen : 16|1@1+ (1,0) [0|1] "" XXX
 SG_ GRA_Recall : 17|1@1+ (1,0) [0|1] "" XXX
 SG_ GRA_Sender : 18|2@1+ (1,0) [0|3] "" XXX
 SG_ COUNTER : 20|4@1+ (1,0) [0|15] "" XXX
 SG_ GRA_Tip_Down : 24|1@1+ (1,0) [0|1] "" XXX
 SG_ GRA_Tip_Up : 25|1@1+ (1,0) [0|1] "" XXX
 SG_ GRA_Zeitluecke : 26|2@1+ (1,0) [0|3] "" XXX
 SG_ GRA_Sta_Limiter : 28|1@1+ (1,0) [0|1] "" XXX
 SG_ GRA_Typ_Hauptschalt : 29|1@1+ (1,0) [0|1] "" XXX
 SG_ GRA_Sportschalter : 30|1@1+ (1,0) [0|1] "" XXX
 SG_ GRA_Fehler_Tip : 31|1@1+ (1,0) [0|1] "" XXX

BO_ 904 GRA: 3 XXX
 SG_ Checksumme_GRA_alt : 16|8@1+ (1,0) [0|0] "" XXX
 SG_ Frei_GRA_alt : 15|1@1+ (1,0) [0|0] "" XXX
 SG_ GRA_alt__ADR_Bedienteil_Fehler : 14|1@1+ (1,0) [0|0] "" XXX
 SG_ GRA_alt__ADR_beschleunigen : 13|1@1+ (1,0) [0|0] "" XXX
 SG_ GRA_alt__ADR_verzoegern : 12|1@1+ (1,0) [0|0] "" XXX
 SG_ GRA_alt__ADR___Tipschalter__Wie : 11|1@1+ (1,0) [0|0] "" XXX
 SG_ GRA_alt__ADR___Tipschalter__Set : 10|1@1+ (1,0) [0|0] "" XXX
 SG_ GRA_alt__ADR___Tipschalter__Aus : 9|1@1+ (1,0) [0|0] "" XXX
 SG_ GRA_alt__ADR___Hauptschalter : 8|1@1+ (1,0) [0|0] "" XXX
 SG_ Zaehler_GRA_alt : 0|8@1+ (1,0) [0|255] "" XXX

BO_ 1352 Getriebe_4: 3 XXX
 SG_ Testparameter_2 : 16|8@1+ (1,0) [0|255] "" XXX
 SG_ Testparameter_1 : 8|8@1+ (1,0) [0|255] "" XXX
 SG_ Waehlhebelausleuchtung : 4|4@1+ (1,0) [0|0] "" XXX
 SG_ Frei_Getriebe_4_1 : 3|1@1+ (1,0) [0|0] "" XXX
 SG_ Testfreigabeflag : 2|1@1+ (1,0) [0|0] "" XXX
 SG_ Handbremserinnerung_s_Lampe : 1|1@1+ (1,0) [0|0] "" XXX
 SG_ Shiftlock_Getriebe_4 : 0|1@1+ (1,0) [0|0] "" XXX

BO_ 1344 Getriebe_2: 8 XXX
 SG_ eingelegte_Fahrstufe : 60|4@1+ (1,0) [0|0] "" XXX
 SG_ Ganganzeige_Kombi___Getriebe_Va : 56|4@1+ (1,0) [0|15] "" XXX
 SG_ Fehlerlampe_f_r_Kupplung_bei_VL : 55|1@1+ (1,0) [0|0] "" XXX
 SG_ Anforderung_Kriechadaption : 54|1@1+ (1,0) [0|0] "" XXX
 SG_ ECO_Anzeige__4_1_ : 53|1@1+ (1,0) [0|0] "" XXX
 SG_ Shift_Lock_Lampe : 52|1@1+ (1,0) [0|0] "" XXX
 SG_ Unterdrueckung_von_Warnungen : 51|1@1+ (1,0) [0|0] "" XXX
 SG_ Gong : 50|1@1+ (1,0) [0|0] "" XXX
 SG_ Starter_wird_angesteuert : 49|1@1+ (1,0) [0|0] "" XXX
 SG_ Hochschaltlampe : 48|1@1+ (1,0) [0|0] "" XXX
 SG_ Synchronisationszeit : 40|8@1+ (20,0) [0|5080] "ms" XXX
 SG_ invertierte_Synchronisations_Wu : 32|8@1+ (25,0) [0|6350] "U/min" XXX
 SG_ Synchronisations_Wunschdrehzahl : 24|8@1+ (25,0) [0|6350] "U/min" XXX
 SG_ Gradientenbegrenzung : 16|8@1+ (10,0) [0|2540] "Nm/s" XXX
 SG_ Leerlaufsolldrehzahl__Getriebe : 8|8@1+ (10,0) [0|2540] "U/min" XXX
 SG_ Zahler_Getriebe_2 : 4|4@1+ (1,0) [0|15] "" XXX
 SG_ Zwischengasflag : 3|1@1+ (1,0) [0|0] "" XXX
 SG_ Ecomatic__4_1_ : 2|1@1+ (1,0) [0|0] "" XXX
 SG_ Schubabschaltunterstuetzung : 1|1@1+ (1,0) [0|0] "" XXX
 SG_ LFR_Adaption_Freigabeflag : 0|1@1+ (1,0) [0|0] "" XXX

BO_ 1088 Getriebe_1: 8 XXX
 SG_ Wandlerverlustmoment : 56|8@1+ (0.39,0) [0|99.06] "MDI" XXX
 SG_ Fehlerspeichereintrag__Getriebe : 55|1@1+ (1,0) [0|0] "" XXX
 SG_ COUNTER : 51|4@1+ (1,0) [0|15] "" XXX
 SG_ Gang_eingelegt : 50|1@1+ (1,0) [0|0] "" XXX
 SG_ Schaltabsicht : 49|1@1+ (1,0) [0|0] "" XXX
 SG_ Motor_aus : 48|1@1+ (1,0) [0|0] "" XXX
 SG_ OBD_Status__Getriebe_1___4_1_ : 46|2@1+ (1,0) [0|0] "" XXX
 SG_ Kuehlleistung : 44|2@1+ (1,0) [0|0] "" XXX
 SG_ Getriebe_Notlauf : 40|4@1+ (1,0) [0|0] "" XXX
 SG_ Fahrwiderstandsindex : 32|8@1+ (0.249,-31.6) [-31.6|31.6] "" XXX
 SG_ inneres_Soll_Motormoment : 24|8@1+ (0.39,0) [0|99.06] "MDI" XXX
 SG_ Uebertragungsfunktion : 16|8@1+ (0.1,0) [0|25.4] "" XXX
 SG_ Waehlhebelposition__Getriebe_1_ : 12|4@1+ (1,0) [0|0] "" XXX
 SG_ Zielgang_oder_eingelegter_Gang : 8|4@1+ (1,0) [0|0] "" XXX
 SG_ EGS_Anforderung : 7|1@1+ (1,0) [0|0] "" XXX
 SG_ Kodierung_im_MSG : 6|1@1+ (1,0) [0|0] "" XXX
 SG_ Leerlaufsolldrehzahlanhebung : 5|1@1+ (1,0) [0|0] "" XXX
 SG_ Wandlerkupplung : 3|2@1+ (1,0) [0|0] "" XXX
 SG_ Klimakompressor_aus__Getriebe_1 : 2|1@1+ (1,0) [0|0] "" XXX
 SG_ Status_Getriebe_und_Wandlerschu : 1|1@1+ (1,0) [0|0] "" XXX
 SG_ Schaltung_aktiv__Getriebe_1_ : 0|1@1+ (1,0) [0|0] "" XXX

BO_ 912 Gate_Komf_1: 8 XXX
 SG_ GK1_Sta_RDK_Warn : 0|1@1+ (1,0) [0|1] "" XXX
 SG_ GK1_Sta_Anhaen : 1|1@1+ (1,0) [0|1] "" XXX
 SG_ GK1_Sta_Licht1 : 2|1@1+ (1,0) [0|1] "" XXX
 SG_ GK1_Sta_Licht3 : 3|1@1+ (1,0) [0|1] "" XXX
 SG_ GK1_Sta_Tuerkont : 4|1@1+ (1,0) [0|1] "" XXX
 SG_ GK1_Sta_Li_vorn : 5|1@1+ (1,0) [0|1] "" XXX
 SG_ GK1_SleepAckn : 7|1@1+ (1,0) [0|1] "" XXX
 SG_ GK1_CharismaModus m1 : 8|4@1+ (1,0) [0|15] "" XXX
 SG_ GK1_SamFktNr M : 12|4@1+ (1,0) [0|15] "" XXX
 SG_ GK1_Fa_Tuerkont : 16|1@1+ (1,0) [0|1] "" XXX
 SG_ GK1_RueckfahrSch : 17|1@1+ (1,0) [0|1] "" XXX
 SG_ GK1_ELV_verrieg : 18|1@1+ (1,0) [0|1] "" XXX
 SG_ GK1_Sta_Kessy_2 : 19|1@1+ (1,0) [0|1] "" XXX
 SG_ GK1_Sta_Stdhzg : 20|1@1+ (1,0) [0|1] "" XXX
 SG_ GK1_SH_Verbau : 21|1@1+ (1,0) [0|1] "" XXX
 SG_ GK1_ParkFrontWi : 22|1@1+ (1,0) [0|1] "" XXX
 SG_ GK1_KW_Warm : 23|1@1+ (1,0) [0|1] "" XXX
 SG_ BCM_Remotestart_Betrieb : 24|1@1+ (1,0) [0|1] "" XXX
 SG_ BSK_HL_geoeffnet : 26|1@1+ (1,0) [0|1] "" XXX
 SG_ BSK_HR_geoeffnet : 27|1@1+ (1,0) [0|1] "" XXX
 SG_ GK1_Rueckfahr : 28|1@1+ (1,0) [0|1] "" XXX
 SG_ GK1_BrLi_links : 29|1@1+ (1,0) [0|1] "" XXX
 SG_ GK1_BrLi_rechts : 30|1@1+ (1,0) [0|1] "" XXX
 SG_ GK1_BrLi_mitte : 31|1@1+ (1,0) [0|1] "" XXX
 SG_ GK1_BLS_ILM : 32|1@1+ (1,0) [0|1] "" XXX
 SG_ GK1_EDC_ILM : 33|1@1+ (1,0) [0|1] "" XXX
 SG_ GK1_Blinker_li : 34|1@1+ (1,0) [0|1] "" XXX
 SG_ GK1_Blinker_re : 35|1@1+ (1,0) [0|1] "" XXX
 SG_ GK1_def_P_verr : 36|1@1+ (1,0) [0|1] "" XXX
 SG_ GK1_LS1_Fernlicht : 37|1@1+ (1,0) [0|1] "" XXX
 SG_ GK1_Sta_Licht2 : 38|1@1+ (1,0) [0|1] "" XXX
 SG_ GK1_Sta_LSM : 39|1@1+ (1,0) [0|1] "" XXX
 SG_ GK1_Count_Anhaen : 40|4@1+ (1,0) [0|15] "" XXX
 SG_ BSK_BT_geoeffnet : 41|1@1+ (1,0) [0|1] "" XXX
 SG_ BSK_HD_Hauptraste : 43|1@1+ (1,0) [0|1] "" XXX
 SG_ GK1_BLS_AAG : 44|1@1+ (1,0) [0|1] "" XXX
 SG_ GK1_EDC_AAG : 45|1@1+ (1,0) [0|1] "" XXX
 SG_ GK1_Anhaenger : 46|1@1+ (1,0) [0|1] "" XXX
 SG_ GK1_BrLi_Anhaen : 47|1@1+ (1,0) [0|1] "" XXX
 SG_ GK1_Abblendlicht : 48|1@1+ (1,0) [0|1] "" XXX
 SG_ GK1_Fernlicht : 49|1@1+ (1,0) [0|1] "" XXX
 SG_ GK1_Wischer_vorn : 50|1@1+ (1,0) [0|1] "" XXX
 SG_ GK1_Sta_ILM_F_1 : 51|1@1+ (1,0) [0|1] "" XXX
 SG_ GK1_Abbl_VL_def : 52|1@1+ (1,0) [0|1] "" XXX
 SG_ GK1_Abbl_VR_def : 53|1@1+ (1,0) [0|1] "" XXX
 SG_ GK1_Blink_Autob : 54|1@1+ (1,0) [0|1] "" XXX
 SG_ GK1_Warnblk_Status : 55|1@1+ (1,0) [0|1] "" XXX
 SG_ GK1_SH_laeuft : 56|1@1+ (1,0) [0|1] "" XXX
 SG_ SH1_ein_Wasserpumpe : 57|1@1+ (1,0) [0|1] "" XXX
 SG_ GK1_Nebel_ein : 58|1@1+ (1,0) [0|1] "" XXX
 SG_ GK1_Bremslicht : 59|1@1+ (1,0) [0|1] "" XXX
 SG_ GK1_Anh_abgesteckt : 60|1@1+ (1,0) [0|1] "" XXX
 SG_ GK1_AnhKonLamp : 61|1@1+ (1,0) [0|1] "" XXX
 SG_ LDS_Stellung_AFL : 62|1@1+ (1,0) [0|1] "" XXX
 SG_ GK1_SH_Zusatzfkt : 63|1@1+ (1,0) [0|1] "" XXX


BO_ 1340 Fahrwerk_1: 1 XXX
 SG_ Frei_Fahrwerk_1_2 : 7|1@1+ (1,0) [0|0] "" XXX
 SG_ Frei_Fahrwerk_1_1 : 6|2@1+ (1,0) [0|0] "" XXX
 SG_ Einstellung_Fahrwerkdaempfung_4 : 4|3@1+ (1,0) [0|7] "" XXX
 SG_ Ansteuererung_Fahrzeugniveau : 0|4@1+ (1,0) [0|15] "" XXX

BO_ 1472 EPB_1: 8 XXX
 SG_ COUNTER : 0|4@1+ (1,0) [0|15] "" XXX
 SG_ EP1_Fehler_Sta : 4|2@1+ (1,0) [0|3] "" Vector__XXX
 SG_ EP1_Sta_EPB : 6|1@1+ (1,0) [0|1] "" XXX
 SG_ EP1_Sta_Schalter : 7|1@1+ (1,0) [0|1] "" XXX
 SG_ EP1_Spannkraft : 8|5@1+ (1,0) [0|30] "Unit_KiloNewto" XXX
 SG_ EP1_Schalterinfo : 13|2@1+ (1,0) [0|3] "" XXX
 SG_ EP1_Sta_NWS : 15|1@1+ (1,0) [0|1] "" XXX
 SG_ EP1_Neig_winkel : 16|8@1+ (1,-128) [-128|127] "Unit_PerCentOfForceOfGravi" XXX
 SG_ EP1_Verzoegerung : 24|8@1+ (0.048,-7.968) [-7.968|4.224] "Unit_MeterPerSeconSquar" XXX
 SG_ EP1_Fehlereintr : 32|1@1+ (1,0) [0|1] "" XXX
 SG_ EP1_Freigabe_Ver : 33|1@1+ (1,0) [0|1] "" XXX
 SG_ EP1_AutoHold_zul : 34|1@1+ (1,0) [0|1] "" XXX
 SG_ EP1_AutoHold_aktiv : 35|1@1+ (1,0) [0|1] "" XXX
 SG_ EP1_SleepInd : 36|1@1+ (1,0) [0|1] "" XXX
 SG_ EP1_Status_Kl_15 : 37|1@1+ (1,0) [0|1] "" XXX
 SG_ EP1_Lampe_AutoP : 38|1@1+ (1,0) [0|1] "" XXX
 SG_ EP1_Bremslicht : 39|1@1+ (1,0) [0|1] "" XXX
 SG_ EP1_Warnton1 : 40|1@1+ (1,0) [0|1] "" XXX
 SG_ EP1_Warnton2 : 41|1@1+ (1,0) [0|1] "" XXX
 SG_ EP1_AnfShLock : 42|1@1+ (1,0) [0|1] "" XXX
 SG_ EPB_Autoholdlampe : 43|1@1+ (1,0) [0|1] "" XXX
 SG_ EP1_QualNeigWi : 44|1@1+ (1,0) [0|1] "" XXX
 SG_ EP1_KuppModBer : 45|2@1+ (1,0) [0|3] "" XXX
 SG_ EP1_HydrHalten : 47|1@1+ (1,0) [0|1] "" XXX
 SG_ EP1_Fkt_Lampe : 48|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ EP1_Warnton : 49|1@1+ (1,0) [0|1] "" XXX
 SG_ EP1_Fehler_BKL : 50|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ EP1_Fehler_gelb : 51|1@1+ (1,0) [0|1] "" XXX
 SG_ EP1__Text : 52|4@1+ (1,0) [0|8] "" Vector__XXX
 SG_ CHECKSUM : 56|8@1+ (1,0) [0|255] "" XXX

BO_ 1326 Diag_Lenkhilfe: 3 XXX
 SG_ Werkstattcode__Diag_ : 16|8@1+ (1,0) [0|0] "" XXX
 SG_ Multiplex_Signal__Diag_ : 13|3@1+ (1,0) [0|7] "" XXX
 SG_ Befehl_Sensorcodierung_Lenkhilf : 12|1@1+ (1,0) [0|0] "" XXX
 SG_ Befehl_Kennliniencodierung_Lenk : 8|4@1+ (1,0) [0|0] "" XXX
 SG_ Befehl_Fehlerspeicher_loeschen : 0|8@1+ (1,0) [0|0] "" XXX

BO_ 1432 Daempfer_1: 2 XXX
 SG_ Frei_Daempfer_1_4 : 12|4@1+ (1,0) [0|0] "" XXX
 SG_ Textbits_Daempfer : 8|4@1+ (1,0) [0|0] "" XXX
 SG_ Fehlerspeicherbit__Daempfer_1_ : 7|1@1+ (1,0) [0|0] "" XXX
 SG_ Systemstatus__Daempfer_1_ : 6|1@1+ (1,0) [0|0] "" XXX
 SG_ Frei_Daempfer_1_3 : 5|1@1+ (1,0) [0|0] "" XXX
 SG_ Status_CDC_Taster : 4|1@1+ (1,0) [0|0] "" XXX
 SG_ Frei_Daempfer_1_2 : 3|1@1+ (1,0) [0|0] "" XXX
 SG_ Status_Daempferregelung_4_1 : 0|3@1+ (1,0) [0|7] "" XXX

BO_ 1392 BSG_Last: 4 XXX
 SG_ Klimaanlage_abschalten : 31|1@1+ (1,0) [0|0] "" XXX
 SG_ Sitzbelueftung_abschalten : 30|1@1+ (1,0) [0|0] "" XXX
 SG_ Wischwasserheizung_abschalten : 29|1@1+ (1,0) [0|0] "" XXX
 SG_ Lenkradheizung_abschalten : 28|1@1+ (1,0) [0|0] "" XXX
 SG_ Heizbare_Sitze_abschalten : 27|1@1+ (1,0) [0|0] "" XXX
 SG_ Heizbare_Aussenspiegel_abschalt : 26|1@1+ (1,0) [0|0] "" XXX
 SG_ Heizbare_Frontscheibe_abschalte : 25|1@1+ (1,0) [0|0] "" XXX
 SG_ Heizbare_Heckscheibe_abschalten : 24|1@1+ (1,0) [0|0] "" XXX
 SG_ Batteriespannung_Bordnetzbatter : 16|8@1+ (0.05,5) [5|17.7] "V" XXX
 SG_ Motorhaubenkontakt : 15|1@1+ (1,0) [0|0] "" XXX
 SG_ Leuchtweitenregulierung : 14|1@1+ (1,0) [0|0] "" XXX
 SG_ Fehlerspeichereintrag__BSG_Last : 13|1@1+ (1,0) [0|0] "" XXX
 SG_ Zustand_der_Starterbatterie : 11|2@1+ (1,0) [0|0] "" XXX
 SG_ Zustand_der_Bordnetzbatterie : 9|2@1+ (1,0) [0|0] "" XXX
 SG_ LL_Drehzahlanhebung : 8|1@1+ (1,0) [0|0] "" XXX
 SG_ Klemme_L : 7|1@1+ (1,0) [0|0] "" XXX
 SG_ Frei_BSG_Last_1_1 : 4|3@1+ (1,0) [0|0] "" XXX
 SG_ ZAS_Klemme_50 : 3|1@1+ (1,0) [0|0] "" XXX
 SG_ ZAS_Klemme_X : 2|1@1+ (1,0) [0|0] "" XXX
 SG_ ZAS_Klemme_15 : 1|1@1+ (1,0) [0|0] "" XXX
 SG_ ZAS_Klemme_S : 0|1@1+ (1,0) [0|0] "" XXX

BO_ 1136 BSG_Kombi: 5 XXX
 SG_ Frei_BSG_Kombi_1_3 : 36|4@1+ (1,0) [0|0] "" XXX
 SG_ Ruecksitzlehne_HR_verr__4_1 : 35|1@1+ (1,0) [0|0] "" XXX
 SG_ Ruecksitzlehne_HL_verr__4_1 : 34|1@1+ (1,0) [0|0] "" XXX
 SG_ Fehlerlampe_Lenkhilfe_veraltet : 33|1@1+ (1,0) [0|0] "" XXX
 SG_ Fehlerlampe_Lenkhilfe__BSG_Komb : 32|1@1+ (1,0) [0|0] "" XXX
 SG_ Fehlerstatus_Kl__58s : 31|1@1+ (1,0) [0|0] "" XXX
 SG_ Klemme_58s__BSG_Kombi_ : 24|7@1+ (1,0) [0|100] "%" XXX
 SG_ Fehlerstatus_Kl__58d : 23|1@1+ (1,0) [0|0] "" XXX
 SG_ Klemme_58d__BSG_Kombi_ : 16|7@1+ (1,0) [0|100] "%" XXX
 SG_ Unterspannung : 15|1@1+ (1,0) [0|0] "" XXX
 SG_ Frei_BSG_Kombi_1_2 : 14|1@1+ (1,0) [0|0] "" XXX
 SG_ Heckdeckel_geoeffnet : 13|1@1+ (1,0) [0|0] "" XXX
 SG_ Motorhaube_geoeffnet : 12|1@1+ (1,0) [0|0] "" XXX
 SG_ Tuer_hinten_rechts_geoeffnet : 11|1@1+ (1,0) [0|0] "" XXX
 SG_ Tuer_hinten_links_geoeffnet : 10|1@1+ (1,0) [0|0] "" XXX
 SG_ Beifahrertuer_geoeffnet : 9|1@1+ (1,0) [0|0] "" XXX
 SG_ Fahrertuer_geoeffnet : 8|1@1+ (1,0) [0|0] "" XXX
 SG_ Lade_Kontrollampe : 7|1@1+ (1,0) [0|0] "" XXX
 SG_ Frei_BSG_Kombi_1_1 : 6|1@1+ (1,0) [0|0] "" XXX
 SG_ Rueckfahrlicht : 5|1@1+ (1,0) [0|0] "" XXX
 SG_ DWA_Akku : 4|1@1+ (1,0) [0|0] "" XXX
 SG_ Warnblink_Mode : 3|1@1+ (1,0) [0|0] "" XXX
 SG_ Anhaenger_Kontrollampe : 2|1@1+ (1,0) [0|0] "" XXX
 SG_ Blinker_rechts_Kontrollampe : 1|1@1+ (1,0) [0|0] "" XXX
 SG_ Blinker_links_Kontrollampe : 0|1@1+ (1,0) [0|0] "" XXX

BO_ 424 Bremse_6: 3 XXX
 SG_ Checksumme_Bremse_6 : 16|8@1+ (1,0) [0|0] "" XXX
 SG_ Zaehler_Bremse_6 : 12|4@1+ (1,0) [0|15] "" XXX
 SG_ Status_Bremsdruck__Bremse_6__du : 11|1@1+ (1,0) [0|0] "" XXX
 SG_ Frei_Bremse_6_1 : 10|1@1+ (1,0) [0|0] "" XXX
 SG_ Bremsdruck__Bremse_6_ : 0|10@1+ (0.3255,-40) [-40|293] "bar" XXX

BO_ 1192 Bremse_5: 8 XXX
 SG_ CHECKSUM : 56|8@1+ (1,0) [0|0] "" XXX
 SG_ COUNTER : 52|4@1+ (1,0) [0|15] "" XXX
 SG_ BR5_ECD_Lampe : 51|1@1+ (1,0) [0|0] "" XXX
 SG_ BR5_ZT_Rueckk_Umsetz : 48|1@1+ (1,0) [0|1] "" XXX
 SG_ BR5_Anhi_Sta : 40|1@1+ (1,0) [0|1] "" XXX
 SG_ ESP_Rollenmodus_Deactiveieren : 34|1@1+ (1,0) [0|1] "" XXX
 SG_ BR5_Sign_Druck : 31|1@1+ (1,0) [0|1] "" XXX
 SG_ BR5_Sta_Druck : 30|1@1+ (1,0) [0|1] "" XXX
 SG_ BR5_Druckvalid : 29|1@1+ (1,0) [0|1] "" XXX
 SG_ BR5_Stillstand : 28|1@1+ (1,0) [0|1] "" XXX
 SG_ BR5_Bremsdruck : 16|12@1+ (0.1,0) [0|250] "bar" XXX
 SG_ BR5_Vorzeichen : 15|1@1+ (1,0) [0|1] "" XXX
 SG_ BR5_Sta_Gierrate : 14|1@1+ (1,0) [0|1] "" XXX
 SG_ BR5_Giergeschw : 0|14@1+ (0.01,0) [0|100] "Grad/sec" XXX
 SG_ BR5_ANB_CM_Rueckk_Umsetz : 49|1@1+ (1,0) [0|1] "" XXX
 SG_ BR5_HDC_bereit : 50|1@1+ (1,0) [0|1] "" XXX
 SG_ ESP_Stat_FallBack_eBKV : 35|1@1+ (1,0) [0|1] "" XXX
 SG_ ESP_Anforderung_EPB : 36|2@1+ (1,0) [0|3] "" XXX
 SG_ ESP_Autohold_active : 38|1@1+ (1,0) [0|1] "" XXX
 SG_ ESP_Autohold_Standby : 39|1@1+ (1,0) [0|1] "" XXX
 SG_ BR5_Anhi_akt : 41|1@1+ (1,0) [0|1] "" XXX
 SG_ BR5_v_Ueberw : 42|1@1+ (1,0) [0|1] "" XXX
 SG_ BR5_Bremslicht : 43|1@1+ (1,0) [0|1] "" XXX
 SG_ BR5_Notbremsung : 44|1@1+ (1,0) [0|1] "" XXX
 SG_ BR5_Fahrer_tritt_ZBR_Schw : 45|1@1+ (1,0) [0|1] "" XXX
 SG_ BR5_AWV2_Bremsruck : 46|1@1+ (1,0) [0|1] "" XXX
 SG_ BR5_AWV2_Fehler : 47|1@1+ (1,0) [0|1] "" XXX

BO_ 672 Bremse_4: 3 XXX
 SG_ Frei_Bremse_4_1 : 17|7@1+ (1,0) [0|0] "" XXX
 SG_ Einheit_Kupplungssteifigkeit : 16|1@1+ (1,0) [0|0] "" XXX
 SG_ ABS_Vorgabewert_hinten_Kupplung : 8|8@1+ (0.7874,0) [0|100] "%" XXX
 SG_ ABS_Vorgabewert_mitte_Kupplungs : 0|8@1+ (3,-381) [-381|378] "Nm/min" XXX

BO_ 1184 Bremse_3: 8 XXX
 SG_ Radgeschw__HR_4_1 : 49|15@1+ (0.01,0) [0|326] "km/h" XXX
 SG_ Frei_Bremse_3_4 : 48|1@1+ (1,0) [0|0] "" XXX
 SG_ Radgeschw__HL_4_1 : 33|15@1+ (0.01,0) [0|326] "km/h" XXX
 SG_ Frei_Bremse_3_3 : 32|1@1+ (1,0) [0|0] "" XXX
 SG_ Radgeschw__VR_4_1 : 17|15@1+ (0.01,0) [0|326] "km/h" XXX
 SG_ Frei_Bremse_3_2 : 16|1@1+ (0.01,0) [0|325] "km/h" XXX
 SG_ Radgeschw__VL_4_1 : 1|15@1+ (0.01,0) [0|326] "km/h" XXX
 SG_ Frei_Bremse_3_1 : 0|1@1+ (1,0) [0|0] "" XXX

BO_ 1440 Bremse_2: 8 XXX
 SG_ gemessene_Querbeschleunigung : 63|1@1+ (1,0) [0|0] "" XXX
 SG_ Frei_Bremse_2_2 : 62|1@1+ (1,0) [0|0] "" XXX
 SG_ Impulszahl : 56|6@1+ (1,0) [0|63] "" XXX
 SG_ Fehlerstatus_Wegimpulse_4_1 : 55|1@1+ (1,0) [0|0] "" XXX
 SG_ Frei_Bremse_2_5 : 54|1@1+ (1,0) [0|0] "" XXX
 SG_ Warnlampe_DDS : 53|1@1+ (1,0) [0|0] "" XXX
 SG_ Fehlerspeichereintrag_Bremse : 52|1@1+ (1,0) [0|0] "" XXX
 SG_ Wegimpulszaehlerstatus : 51|1@1+ (1,0) [0|0] "" XXX
 SG_ Wegimpulse_Vorderachse : 40|11@1+ (1,0) [0|2047] "" XXX
 SG_ Zeitstempel : 24|16@1+ (1,0) [0|65535] "tics" XXX
 SG_ mittlere_Raddrehzahl__Bremse_2 : 9|15@1+ (0.002,0) [0|65.278] "U/sec" XXX
 SG_ Querbeschl__TimerTic M : 8|1@1+ (1,0) [0|0] "" XXX
 SG_ Timer m1 : 0|8@1+ (0.04,0) [0|10.2] "usec" XXX
 SG_ Querbeschleunigung m0 : 0|8@1+ (0.01,-1.27) [-1.27|1.27] "g" XXX

BO_ 416 Bremse_1: 8 XXX
 SG_ Geschwindigkeitsersatzwert : 63|1@1+ (1,0) [0|0] "" XXX
 SG_ ESP_Systemstatus_4_1 : 62|1@1+ (1,0) [0|0] "" XXX
 SG_ ESP_Passiv_getastet : 61|1@1+ (1,0) [0|0] "" XXX
 SG_ ASR_Steuerger_t : 60|1@1+ (1,0) [0|0] "" XXX
 SG_ COUNTER : 56|4@1+ (1,0) [0|15] "" XXX
 SG_ MSR_Eingriffsmoment : 48|8@1+ (0.39,0) [0|99.06] "MDI" XXX
 SG_ ASR_Eingriffsmoment_schnell : 40|8@1+ (0.39,0) [0|99.06] "MDI" XXX
 SG_ ASR_Eingriffsmoment_langsam : 32|8@1+ (0.39,0) [0|99.06] "MDI" XXX
 SG_ Geschwindigkeit_neu__Bremse_1_ : 17|15@1+ (0.01,0) [0|326.39] "km/h" XXX
 SG_ Aktiver_Bremskraftverstaerker : 16|1@1+ (1,0) [0|0] "" XXX
 SG_ ABS_in_Diagnose : 15|1@1+ (1,0) [0|0] "" XXX
 SG_ Fehlerstatus_Schlechtwegausblen : 14|1@1+ (1,0) [0|0] "" XXX
 SG_ Schlechtwegausblendung : 13|1@1+ (1,0) [0|0] "" XXX
 SG_ Fahrer_bremst__Bremse_1___4_1_ : 11|1@1+ (1,0) [0|0] "" XXX
 SG_ Bremskontroll_Lampe : 10|1@1+ (1,0) [0|0] "" XXX
 SG_ Lampe_ASR___ESP : 9|1@1+ (1,0) [0|0] "" XXX
 SG_ Lampe_ABS : 8|1@1+ (1,0) [0|0] "" XXX
 SG_ EBV_Eingriff : 7|1@1+ (1,0) [0|0] "" XXX
 SG_ ASR_Schaltbeeinflussung : 5|2@1+ (1,0) [0|0] "" XXX
 SG_ ESP_Eingriff : 4|1@1+ (1,0) [0|0] "" XXX
 SG_ EDS_Eingriff : 3|1@1+ (1,0) [0|0] "" XXX
 SG_ ABS_Bremsung__4_1_ : 2|1@1+ (1,0) [0|0] "" XXX
 SG_ MSR_Anforderung : 1|1@1+ (1,0) [0|0] "" XXX
 SG_ ASR_Anforderung : 0|1@1+ (1,0) [0|0] "" XXX

BO_ 680 Bremsbooster_1: 3 XXX
 SG_ Fehlerspeichereintrag_Booster : 23|1@1+ (1,0) [0|0] "" XXX
 SG_ Loseschalter_unplausibel_Boost : 22|1@1+ (1,0) [0|0] "" XXX
 SG_ Position_Standby : 21|1@1+ (1,0) [0|0] "" XXX
 SG_ ADR_Relais_ge_ffnet : 20|1@1+ (1,0) [0|0] "" XXX
 SG_ Status_Bremsbooster_Steuerung : 19|1@1+ (1,0) [0|0] "" XXX
 SG_ Bremsbooster_verf_gbar : 18|1@1+ (1,0) [0|0] "" XXX
 SG_ Eingriff_Bremsbooster : 17|1@1+ (1,0) [0|0] "" XXX
 SG_ Bremseingriff_Fahrer : 16|1@1+ (1,0) [0|0] "" XXX
 SG_ Frei_Bremsbooster_1_1 : 12|4@1+ (1,0) [0|0] "" XXX
 SG_ Zaehler_Booster_1 : 8|4@1+ (1,0) [0|15] "" XXX
 SG_ Checksumme_Booster_1 : 0|8@1+ (1,0) [0|0] "" XXX

BO_ 1400 BatMan_1: 1 XXX
 SG_ Fehlerspeichereintrag__BatMan_ : 7|1@1+ (1,0) [0|0] "" XXX
 SG_ Leistungsrelais : 6|1@1+ (1,0) [0|0] "" XXX
 SG_ Messung_Starterleitung : 5|1@1+ (1,0) [0|0] "" XXX
 SG_ Zustand_Starterleitung : 4|1@1+ (1,0) [0|0] "" XXX
 SG_ Umschaltrelais_Bordnetzbatterie : 3|1@1+ (1,0) [0|0] "" XXX
 SG_ Ladung_Starterbatterie : 1|2@1+ (1,0) [0|0] "" XXX
 SG_ Startmodus : 0|1@1+ (1,0) [0|0] "" XXX

BO_ 704 Allrad_1: 5 XXX
 SG_ Kupplungssteifigkeit_Hinten__Is : 32|8@1+ (0.7874,0) [0|100] "%" XXX
 SG_ Fehlerspeichereintrag_Allrad_1 : 31|1@1+ (1,0) [0|0] "" XXX
 SG_ Frei_Allrad_1_1 : 26|5@1+ (1,0) [0|0] "" XXX
 SG_ Schaltung_Vorwarnung : 25|1@1+ (1,0) [0|0] "" XXX
 SG_ Schaltung_aktiv__Allrad_1_ : 24|1@1+ (1,0) [0|0] "" XXX
 SG_ Ganginfo__PNG_ : 20|4@1+ (1,0) [0|0] "" XXX
 SG_ PNG_Anzeige_blinkend : 19|1@1+ (1,0) [0|0] "" XXX
 SG_ PNG_Status_4_1 : 16|3@1+ (1,0) [0|0] "" XXX
 SG_ Kupplungssteifigkeit_Mitte__Ist : 8|8@1+ (3,-381) [-381|378] "Nm/min" XXX
 SG_ Einheit_der_Kupplungssteifigkei : 7|1@1+ (1,0) [0|0] "" XXX
 SG_ Geschwindigkeitsbegrenzung : 6|1@1+ (1,0) [0|0] "" XXX
 SG_ Allrad_Warnlampe : 5|1@1+ (1,0) [0|0] "" XXX
 SG_ Notlauf : 4|1@1+ (1,0) [0|0] "" XXX
 SG_ Kupplung_komplett_offen : 3|1@1+ (1,0) [0|0] "" XXX
 SG_ Fehlerstatus_Kupplungssteifigke : 2|1@1+ (1,0) [0|0] "" XXX
 SG_ Ubertemperaturschutz__Allrad_1_ : 1|1@1+ (1,0) [0|0] "" XXX
 SG_ Fehler_Allrad_Kupplung : 0|1@1+ (1,0) [0|0] "" XXX

BO_ 1360 Airbag_2: 2 XXX
 SG_ OOP_Beifahrer : 14|2@1+ (1,0) [0|0] "" XXX
 SG_ OOP_Fahrer : 12|2@1+ (1,0) [0|0] "" XXX
 SG_ Belegungserkennung_hinten_mitte : 11|1@1+ (1,0) [0|0] "" XXX
 SG_ Belegungserkennung_hinten_recht : 10|1@1+ (1,0) [0|0] "" XXX
 SG_ Belegungserkennung_hinten_links : 9|1@1+ (1,0) [0|0] "" XXX
 SG_ Belegungserkennung_Beifahrersit : 8|1@1+ (1,0) [0|0] "" XXX
 SG_ Checksumme_Airbag_2__reserviert : 0|8@1+ (1,0) [0|0] "" XXX

BO_ 80 Airbag_1: 4 XXX
 SG_ CHECKSUM : 24|8@1+ (1,0) [0|0] "" XXX
 SG_ COUNTER : 20|4@1+ (1,0) [0|15] "" XXX
 SG_ Fehlerspeichereintrag : 19|1@1+ (1,0) [0|0] "" XXX
 SG_ Frei_Airbag_1_2 : 18|1@1+ (1,0) [0|0] "" XXX
 SG_ Airbag_im_Stellgliedtest : 17|1@1+ (1,0) [0|0] "" XXX
 SG_ Airbag_in_Diagnose : 16|1@1+ (1,0) [0|0] "" XXX
 SG_ Gurtwarnung_Beifahrer : 15|1@1+ (1,0) [0|0] "" XXX
 SG_ Gurtschalter_Beifahrer : 14|1@1+ (1,0) [0|0] "" XXX
 SG_ Gurtwarnung_Fahrer : 13|1@1+ (1,0) [0|0] "" XXX
 SG_ Gurtschalter_Fahrer : 12|1@1+ (1,0) [0|0] "" XXX
 SG_ Airbag_Systemfehler : 11|1@1+ (1,0) [0|0] "" XXX
 SG_ Kindersitzerkennung : 10|1@1+ (1,0) [0|0] "" XXX
 SG_ Airbag_deaktiviert : 9|1@1+ (1,0) [0|0] "" XXX
 SG_ Airbag_Lampe : 8|1@1+ (1,0) [0|0] "" XXX
 SG_ Crash_Intensitaet : 5|3@1+ (1,0) [0|111] "B" XXX
 SG_ Rollover : 4|1@1+ (1,0) [0|0] "" XXX
 SG_ Seiten_Crash_Beifahrer : 3|1@1+ (1,0) [0|0] "" XXX
 SG_ Seiten_Crash_Fahrer : 2|1@1+ (1,0) [0|0] "" XXX
 SG_ Heck_Crash : 1|1@1+ (1,0) [0|0] "" XXX
 SG_ Front_Crash : 0|1@1+ (1,0) [0|0] "" XXX

BO_ 864 ADR_System: 8 XXX
 SG_ S_Checksumme_ADR_1 : 56|8@1+ (1,0) [0|0] "" XXX
 SG_ S_Frei_ADR_1_1 : 52|4@1+ (1,0) [0|0] "" XXX
 SG_ S_Zeitluecke_gemessen : 48|4@1+ (1,0) [0|15] "" XXX
 SG_ S_Fehlerspeichereintrag_ADR : 47|1@1+ (1,0) [0|0] "" XXX
 SG_ S_Fehlerspeichereintrag_Bremsbo : 46|1@1+ (1,0) [0|0] "" XXX
 SG_ S_ADR_Relais_geoeffnet : 45|1@1+ (1,0) [0|0] "" XXX
 SG_ S_Bremsbooster_Status : 43|2@1+ (1,0) [0|0] "" XXX
 SG_ S_Eingriff_Bremsbooster : 42|1@1+ (1,0) [0|0] "" XXX
 SG_ S_Loeseschalter_unplausibel : 41|1@1+ (1,0) [0|0] "" XXX
 SG_ S_Bremseingriff_Fahrer : 40|1@1+ (1,0) [0|0] "" XXX
 SG_ S_Anzeige_Sensor_blind : 39|1@1+ (1,0) [0|0] "" XXX
 SG_ S_Ansteuerung_optischer_Fahrerh : 38|1@1+ (1,0) [0|0] "" XXX
 SG_ S_Ansteuerung_Gong_2 : 37|1@1+ (1,0) [0|0] "" XXX
 SG_ S_Ansteuerung_Gong_1 : 36|1@1+ (1,0) [0|0] "" XXX
 SG_ S_Schaltaufforderung : 34|2@1+ (1,0) [0|0] "" XXX
 SG_ S_Anzeige_Prioritaet : 33|1@1+ (1,0) [0|0] "" XXX
 SG_ S_Anzeige_Zeitluecke : 32|1@1+ (1,0) [0|0] "" XXX
 SG_ S_Wunschgeschwindigkeit : 24|8@1+ (1,0) [0|254] "km/h" XXX
 SG_ S_Objekt_erfasst : 22|2@1+ (1,0) [0|0] "" XXX
 SG_ S_Gesetzte_Zeitluecke__ADR_1_ : 18|4@1+ (1,0) [0|15] "" XXX
 SG_ S_Synchronisation_Bremsbooster : 17|1@1+ (1,0) [0|0] "" XXX
 SG_ S_Momentenanforderung_Freigabe : 16|1@1+ (1,0) [0|0] "" XXX
 SG_ S_Verhinderung_Schubabschaltung : 15|1@1+ (1,0) [0|0] "" XXX
 SG_ S_Status_ADR_1__S_ : 13|2@1+ (1,0) [0|0] "" XXX
 SG_ S_Fehler_ADR_1__S_ : 12|1@1+ (1,0) [0|0] "" XXX
 SG_ S_Zaehler_ADR_1 : 8|4@1+ (1,0) [0|15] "" XXX
 SG_ S_Momentenanforderung_ADR : 0|8@1+ (0.39,0) [0|99] "MDI" XXX

BO_ 608 ADR_2: 4 XXX
 SG_ Frei_ADR_2_2 : 27|5@1+ (1,0) [0|0] "" XXX
 SG_ Anforderung_Bremsdruck : 16|11@1+ (0.0625,0) [0|127.9375] "bar" XXX
 SG_ Frei_ADR_2_1 : 14|2@1+ (1,0) [0|0] "" XXX
 SG_ Relais_Test_Fehler : 13|1@1+ (1,0) [0|0] "" XXX
 SG_ Standby : 12|1@1+ (1,0) [0|0] "" XXX
 SG_ Zaehler_ADR_2 : 8|4@1+ (1,0) [0|15] "" XXX
 SG_ Checksumme_ADR_2 : 0|8@1+ (1,0) [0|0] "" XXX

BO_ 1324 ADR_1: 8 XXX
 SG_ Checksumme_ADR_1 : 56|8@1+ (1,0) [0|0] "" XXX
 SG_ Frei_ADR_1_5 : 52|4@1+ (1,0) [0|0] "" XXX
 SG_ Zeitluecke_gemessen : 48|4@1+ (1,0) [0|15] "" XXX
 SG_ Fehlerspeichereintrag_ADR : 47|1@1+ (1,0) [0|0] "" XXX
 SG_ Reserviert_ADR_1_1 : 40|7@1+ (1,0) [0|0] "" XXX
 SG_ Anzeige_Sensor_blind : 39|1@1+ (1,0) [0|0] "" XXX
 SG_ Ansteuerung_optischer_Fahrerhin : 38|1@1+ (1,0) [0|0] "" XXX
 SG_ Ansteuerung_Gong_2 : 37|1@1+ (1,0) [0|0] "" XXX
 SG_ Ansteuerung_Gong_1 : 36|1@1+ (1,0) [0|0] "" XXX
 SG_ Schaltaufforderung : 34|2@1+ (1,0) [0|0] "" XXX
 SG_ Anzeige_Prioritaet : 33|1@1+ (1,0) [0|0] "" XXX
 SG_ Anzeige_Zeitluecke : 32|1@1+ (1,0) [0|0] "" XXX
 SG_ Wunschgeschwindigkeit : 24|8@1+ (1,0) [0|254] "km/h" XXX
 SG_ Objekt_erfasst : 22|2@1+ (1,0) [0|0] "" XXX
 SG_ Gesetzte_Zeitluecke__ADR_1_ : 18|4@1+ (1,0) [0|15] "" XXX
 SG_ Synchronisation_Bremsbooster : 17|1@1+ (1,0) [0|0] "" XXX
 SG_ Momentenanforderung_Freigabe : 16|1@1+ (1,0) [0|0] "" XXX
 SG_ Verhinderung_Schubabschaltung : 15|1@1+ (1,0) [0|0] "" XXX
 SG_ Status_ADR_1 : 13|2@1+ (1,0) [0|0] "" XXX
 SG_ Fehler_ADR_1 : 12|1@1+ (1,0) [0|0] "" XXX
 SG_ Zaehler_ADR_1 : 8|4@1+ (1,0) [0|15] "" XXX
 SG_ Momentenanforderung_ADR : 0|8@1+ (0.39,0) [0|99] "MDI" XXX

BO_ 1550 Einheiten_1: 2 XXX
 SG_ MFA_v_Einheit_02 : 0|1@1+ (1,0) [0|1] "" XXX

BO_ 872 ACC_System: 8 XXX
 SG_ CHECKSUM : 0|8@1+ (1,0) [0|255] "" XXX
 SG_ COUNTER : 8|4@1+ (1,0) [0|15] "" XXX
 SG_ ACS_Sta_ADR : 12|2@1+ (1,0) [0|3] "" XXX
 SG_ ACS_ADR_Schub : 14|1@1+ (1,0) [0|1] "" XXX
 SG_ ACS_Schubabsch : 15|1@1+ (1,0) [0|1] "" XXX
 SG_ ACS_StSt_Info : 16|2@1+ (1,0) [0|3] "" XXX
 SG_ ACS_MomEingriff : 18|1@1+ (1,0) [0|1] "" XXX
 SG_ ACS_Typ_ACC : 19|2@1+ (1,0) [0|3] "" XXX
 SG_ ACS_FreigSollB : 23|1@1+ (1,0) [0|1] "" XXX
 SG_ ACS_Sollbeschl : 24|11@1+ (0.005,-7.22) [-7.22|3.005] "Unit_MeterPerSeconSquar" XXX
 SG_ ACS_Anhaltewunsch : 38|1@1+ (1,0) [0|1] "" XXX
 SG_ ACS_Fehler : 39|1@1+ (1,0) [0|1] "" XXX
 SG_ ACS_zul_Regelabw : 40|8@1+ (0.005,0) [0|1.265] "Unit_MeterPerSeconSquar" XXX
 SG_ ACS_max_AendGrad : 48|8@1+ (0.02,0) [0.02|5.06] "Unit_MeterPerSeconSquar" XXX

BO_ 1386 ACC_GRA_Anzeige: 8 XXX
 SG_ CHECKSUM : 0|8@1+ (1,0) [0|255] "" XXX
 SG_ ACA_StaACC : 8|3@1+ (1,0) [0|7] "" XXX
 SG_ ACA_ID_StaACC : 11|5@1+ (1,0) [0|31] "" XXX
 SG_ ACA_Fahrerhinw : 16|1@1+ (1,0) [0|1] "" XXX
 SG_ ACA_AnzDisplay : 17|1@1+ (1,0) [0|1] "" XXX
 SG_ ACA_Zeitluecke : 18|4@1+ (1,0) [0|15] "" XXX
 SG_ ACA_V_Wunsch : 24|8@1+ (1,0) [0|255] "Unit_KiloMeterPerHour" XXX
 SG_ ACA_kmh_mph : 32|1@1+ (1,0) [0|1] "" XXX
 SG_ ACA_Akustik1 : 33|1@1+ (1,0) [0|1] "" XXX
 SG_ ACA_Akustik2 : 34|1@1+ (1,0) [0|1] "" XXX
 SG_ ACA_PrioDisp : 35|2@1+ (1,0) [0|3] "" XXX
 SG_ ACA_gemZeitl : 40|4@1+ (1,0) [0|15] "" XXX
 SG_ ACA_ACC_Verz : 44|1@1+ (1,0) [0|1] "" XXX
 SG_ ACA_StaGRA : 48|3@1+ (1,0) [0|7] "" XXX
 SG_ ACA_ID_StaGRA : 51|5@1+ (1,0) [0|31] "" XXX
 SG_ ACA_Codierung : 56|1@1+ (1,0) [0|1] "" XXX
 SG_ ACA_Tachokranz : 57|1@1+ (1,0) [0|1] "" XXX
 SG_ ACA_Aend_Zeitluecke : 58|1@1+ (1,0) [0|1] "" XXX
 SG_ COUNTER : 60|4@1+ (1,0) [0|15] "" XXX

BO_ 208 Lenkhilfe_3: 6 XXX
 SG_ CHECKSUM : 0|8@1+ (1,0) [0|255] "" XXX
 SG_ LH3_BS_Spiegel : 8|4@1+ (1,0) [0|15] "" XXX
 SG_ COUNTER : 12|4@1+ (1,0) [0|15] "" XXX
 SG_ LH3_LM : 16|10@1+ (1,0) [0|1023] "" XXX
 SG_ LH3_LMSign : 26|1@1+ (1,0) [0|1] "" XXX
 SG_ LH3_LMValid : 27|1@1+ (1,0) [0|1] "" XXX
 SG_ LH3_Sta_DSR : 28|4@1+ (1,0) [0|15] "" XXX
 SG_ LH3_BLW : 32|12@1+ (0.15,0) [0|615] "" XXX
 SG_ LH3_BLWSign : 44|1@1+ (1,0) [0|1] "" XXX
 SG_ LH3_BLWValid : 45|1@1+ (1,0) [0|1] "" XXX
 SG_ LH3_Lenkungstyp : 46|2@1+ (1,0) [0|3] "" XXX

BO_ 978 Lenkhilfe_2: 7 XXX
 SG_ CHECKSUM : 0|8@1+ (1,0) [0|255] "" XXX
 SG_ COUNTER : 8|4@1+ (1,0) [0|15] "" XXX
 SG_ LH2_Geradeaus : 12|1@1+ (1,0) [0|1] "" XXX
 SG_ LH2_Sta_Charisma : 13|3@1+ (1,0) [0|7] "" XXX
 SG_ LH2_Sta_HCA : 16|4@1+ (1,0) [0|15] "" XXX
 SG_ LH2_Ausg_LW1 : 20|1@1+ (1,0) [0|1] "" XXX
 SG_ LH2_Ausg_LW1_gue : 21|1@1+ (1,0) [0|1] "" XXX
 SG_ LH2_StatEPS_PLA : 24|4@1+ (1,0) [0|15] "" XXX
 SG_ LH2_aktLenkeingriff : 32|8@1+ (1,0) [0|255] "" XXX
 SG_ LH2_PLA_Err : 48|4@1+ (1,0) [0|15] "" XXX
 SG_ LH2_PLA_Abbr : 52|4@1+ (1,0) [0|15] "" XXX

BO_ 980 PLA_1: 8 XXX
 SG_ CHECKSUM : 0|8@1+ (1,0) [0|255] "" XXX
 SG_ COUNTER : 8|4@1+ (1,0) [0|15] "" XXX
 SG_ PL1_Status_EPS : 12|4@1+ (1,0) [0|15] "" XXX
 SG_ PL1_ArcAngleReq : 16|15@1+ (0.04375,0) [0|1433.55625] "Unit_DegreeOfArc" XXX
 SG_ PL1_AngleReqSign : 31|1@1+ (1,0) [0|1] "" XXX
 SG_ PL1_Stat_PLA_ESP : 32|4@1+ (1,0) [0|10] "" Vector__XXX
 SG_ PL1_Bremsmoment : 40|13@1+ (4,0) [0|32760] "Unit_NewtoMeter" Vector__XXX
 SG_ PL1_void : 53|11@1+ (1,0) [0|2047] "" XXX

BO_ 210 HCA_1: 5 XXX
 SG_ CHECKSUM : 0|8@1+ (1,0) [0|15] "" XXX
 SG_ COUNTER : 8|4@1+ (1,0) [0|15] "" XXX
 SG_ HCA_Status : 12|4@1+ (1,0) [0|15] "" XXX
 SG_ LM_Offset : 16|15@1+ (0.03125,0) [0|300] "cNm" XXX
 SG_ LM_OffSign : 31|1@1+ (1,0) [0|1] "" XXX
 SG_ Vib_Freq : 32|4@1+ (4,0) [0|60] "Hz" XXX
 SG_ Vib_Amp : 36|4@1+ (0.5,0) [0|7.5] "Nm" XXX

BO_ 644 Motor_Bremse: 6 XXX
 SG_ MOB_Standby : 12|1@1+ (1,0) [0|1] ""  XXX
 SG_ MOB_Freigabe : 14|1@1+ (1,0) [0|1] "" BCM_Gateway,Bremse_MK25AESP,Gateway_separat
 SG_ MOB_Anhaltewunsch : 13|1@1+ (1,0) [0|1] "" Bremse_MK25AESP,Getriebe_DQ
 SG_ MOB_CHECKSUM : 0|8@1+ (1,0) [0|255] "" Bremse_MK25AESP
 SG_ MOB_COUNTER : 8|4@1+ (1,0) [0|15] "" Bremse_MK25AESP
 SG_ TSK_v_Begrenzung_aktiv : 15|1@0+ (1,0) [0|1] "" XXX
 SG_ TSK_ax_Getriebe_01 : 40|8@1+ (0.048,0) [0|255] "m/s2" XXX
 SG_ MOB_Bremsstgr : 16|11@1+ (0.048852,0) [0|100] "Unit_PerCent" Vector__XXX
 SG_ MOB_Bremsmom : 27|13@1+ (4,0) [0|32760] "Unit_NewtoMeter"  Bremse_MK25AESP

BO_ 870 AWV: 8 XXX
 SG_ CHECKSUM : 0|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ COUNTER : 8|4@1+ (1,0) [0|15] "" Vector__XXX
 SG_ AWV_Text : 12|4@1+ (1,0) [0|14] "" Vector__XXX
 SG_ AWV_1_Freigabe : 16|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ AWV_1_Prefill : 17|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ AWV_1_Parameter : 18|2@1+ (1,0) [0|3] "" Vector__XXX
 SG_ AWV_only : 20|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ AWV_CityANB_Auspraegung : 21|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ AWV_Halten : 22|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ ANB_Teilbremsung_Freigabe : 23|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ AWV_2_Status : 24|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ AWV_2_Fehler : 25|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ AWV_2_SU_Warnzeit : 26|2@1+ (1,0) [0|3] "" Vector__XXX
 SG_ AWV_2_SU_Bremsruck : 28|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ AWV_2_SU_Gong : 29|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ AWV_2_SU_Lampe : 30|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ AWV_2_Umfeldwarn : 31|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ AWV_2_Freigabe : 32|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ AWV_2_Ruckprofil : 33|3@1+ (1,0) [0|7] "" Vector__XXX
 SG_ AWV_2_Warnton : 36|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ AWV_2_Warnsymbol : 37|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ AWV_Infoton : 38|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ AWV_2_Gurtstraffer : 39|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ AWV_Konfiguration_Menueanf : 40|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ AWV_Konfiguration_Vorw_Menueanf : 41|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ AWV_Konfiguration_Status : 42|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ AWV_Konfiguration_Vorw_Status : 43|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ AWV_2_Abstandswarnung : 51|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ ANB_Zielbremsung_Freigabe : 52|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ ANB_CM_Anforderung : 53|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ ANB_Ziel_Teilbrems_Verz_Anf : 54|10@1+ (0.024,-20.016) [0|1023] "Unit_MeterPerSeconSquar" Vector__XXX
 
BO_ 1470 LDW_Status: 8 XXX
 SG_ LDW_Lernmodus_rechts : 0|2@1+ (1,0) [0|3] "" XXX
 SG_ LDW_Lernmodus_links : 2|2@1+ (1,0) [0|3] "" XXX
 SG_ LDW_Lernmodus : 9|3@1+ (1,0) [0|3] "" XXX
 SG_ LDW_Textbits : 12|4@1+ (1,0) [0|15] "" XXX
 SG_ LDW_Gong : 16|2@1+ (1,0) [0|3] "" XXX
 SG_ LDW_Kameratyp : 18|1@1+ (1,0) [0|1] "" XXX
 SG_ LDW_Lampe_gelb : 19|1@1+ (1,0) [0|1] "" XXX
 SG_ LDW_Lampe_gruen : 20|1@1+ (1,0) [0|1] "" XXX
 SG_ LDW_SW_Warnung_links : 21|1@1+ (1,0) [0|1] "" XXX
 SG_ LDW_SW_Warnung_rechts : 22|1@1+ (1,0) [0|1] "" XXX
 SG_ LDW_KD_Fehler : 23|1@1+ (1,0) [0|1] "" XXX
 SG_ LDW_DLC : 24|8@1+ (0.01,-1.25) [-1.25|1.25] "" XXX
 SG_ LDW_TLC : 32|5@1+ (0.1,0) [0|3] "" XXX
 SG_ LDW_Seite_DLCTLC : 37|1@1+ (1,0) [0|1] "" XXX
 SG_ LDW_Frueh_Spaet : 38|2@1+ (1,0) [0|3] "" XXX

BO_ 428 Bremse_8: 8 XXX
 SG_ BR8_Checksumme : 0|8@1+ (1,0) [0|255] "" XXX
 SG_ BR8_Zaehler : 8|4@1+ (1,0) [0|15] "" XXX
 SG_ BR8_Sta_ACC_Anf : 12|1@1+ (1,0) [0|1] "" XXX
 SG_ BR8_Verz_EPB_akt : 13|1@1+ (1,0) [0|1] "" XXX
 SG_ BR8_Sta_Br_temp : 14|1@1+ (1,0) [0|1] "" XXX
 SG_ BR8_Sta_Br_Druck : 15|1@1+ (1,0) [0|1] "" XXX
 SG_ BR8_TolAbgl_HL : 16|8@1+ (0.048828125,-6.201171875) [-6.201171875|6.15234375] "Unit_PerCent" XXX
 SG_ BR8_TolAbgl_HR : 24|8@1+ (0.048828125,-6.201171875) [-6.201171875|6.15234375] "Unit_PerCent" XXX
 SG_ BR8_Istbeschl : 32|9@1+ (0.02,-7.22) [-7.22|2.98] "Unit_MeterPerSeconSquar"  XXX
 SG_ BR8_Sta_HW_BLS : 41|1@1+ (1,0) [0|1] "" XXX
 SG_ BR8_QB_LBeschl : 42|1@1+ (1,0) [0|1] "" XXX
 SG_ BR8_ESC_Mode : 43|2@1+ (1,0) [0|3] "" XXX
 SG_ BR8_aktBrSyst : 45|1@1+ (1,0) [0|1] "" XXX
 SG_ BR8_Fa_bremst : 46|1@1+ (1,0) [0|1] "" XXX
 SG_ BR8_StaBrSyst : 47|1@1+ (1,0) [0|1] "" XXX
 SG_ BR8_Laengsbeschl : 48|10@1+ (0.03125,-16) [-15.96875|15.9375] "Unit_MeterPerSeconSquar" XXX
 SG_ BR8_Sta_ADR_BR : 58|1@1+ (1,0) [0|1] "" XXX
 SG_ BR8_Quattro : 59|1@1+ (1,0) [0|1] "" XXX
 SG_ BR8_Sta_VerzReg : 60|1@1+ (1,0) [0|1] "" XXX
 SG_ BR8_Sta_BLS : 61|1@1+ (1,0) [0|1] "" XXX
 SG_ BR8_Verz_EPB : 62|1@1+ (1,0) [0|1] "" XXX
 SG_ BR8_Check_EPB : 63|1@1+ (1,0) [0|1] "" XXX

BO_ 928 Bremse_10: 8 XXX
 SG_ B10_Checksumme : 0|8@1+ (1,0) [0|255] "" XXX
 SG_ B10_Zaehler : 8|4@1+ (1,0) [0|15] "" XXX
 SG_ B10_QB_Wegimp_VL : 12|1@1+ (1,0) [0|1] "" XXX
 SG_ B10_QB_Wegimp_VR : 13|1@1+ (1,0) [0|1] "" XXX
 SG_ B10_QB_Wegimp_HL : 14|1@1+ (1,0) [0|1] "" XXX
 SG_ B10_QB_Wegimp_HR : 15|1@1+ (1,0) [0|1] "" XXX
 SG_ B10_Wegimp_VL : 16|10@1+ (1,0) [0|1000] "" XXX
 SG_ B10_Wegimp_VR : 26|10@1+ (1,0) [0|1000] "" XXX
 SG_ B10_Wegimp_HL : 36|10@1+ (1,0) [0|1000] "" XXX
 SG_ B10_Wegimp_HR : 46|10@1+ (1,0) [0|1000] "" XXX
 SG_ B10_QB_Fahrtr_VL : 56|1@1+ (1,0) [0|1] "" XXX
 SG_ B10_QB_Fahrtr_VR : 57|1@1+ (1,0) [0|1] "" XXX
 SG_ B10_QB_Fahrtr_HL : 58|1@1+ (1,0) [0|1] "" XXX
 SG_ B10_QB_Fahrtr_HR : 59|1@1+ (1,0) [0|1] "" XXX
 SG_ B10_Fahrtr_VL : 60|1@1+ (1,0) [0|1] "" XXX
 SG_ B10_Fahrtr_VR : 61|1@1+ (1,0) [0|1] "" XXX
 SG_ B10_Fahrtr_HL : 62|1@1+ (1,0) [0|1] "" XXX
 SG_ B10_Fahrtr_HR : 63|1@1+ (1,0) [0|1] "" XXX

BO_ 835 RDK_Status: 3 XXX
 SG_ RKS_Reifen_VL : 0|1@1+ (1,0) [0|1] "" XXX
 SG_ RKS_Reifen_VR : 1|1@1+ (1,0) [0|1] "" XXX
 SG_ RKS_Reifen_HL : 2|1@1+ (1,0) [0|1] "" XXX
 SG_ RKS_Reifen_HR : 3|1@1+ (1,0) [0|1] "" XXX
 SG_ RKS_Reifen_RR : 4|1@1+ (1,0) [0|1] "" XXX
 SG_ RKS_Warnung_2 : 5|1@1+ (1,0) [0|1] "" XXX
 SG_ RKS_Warnung_1 : 6|1@1+ (1,0) [0|1] "" XXX
 SG_ RKS_Systemfehler : 7|1@1+ (1,0) [0|1] "" XXX
 SG_ RKS_Kalibrier_abgew : 8|1@1+ (1,0) [0|1] "" XXX
 SG_ RKS_Druckdiff_Vorn : 9|1@1+ (1,0) [0|1] "" XXX
 SG_ RKS_Druckdiff_Hinten : 10|1@1+ (1,0) [0|1] "" XXX
 SG_ RKS_Befuellung_RR_low : 11|1@1+ (1,0) [0|1] "" XXX
 SG_ RKS_Funkstoerung : 12|1@1+ (1,0) [0|1] "" XXX
 SG_ RKS_System_Aus : 13|1@1+ (1,0) [0|1] "" XXX
 SG_ RKS_KD_Fehler : 15|1@1+ (1,0) [0|1] "" XXX
 SG_ RKS_Lampe : 16|1@1+ (1,0) [0|1] "" XXX
 SG_ RKS_Ton : 17|1@1+ (1,0) [0|1] "" XXX
 SG_ RKS_Gong : 18|1@1+ (1,0) [0|1] "" XXX
 SG_ RKS_RDK_Blinkbit : 19|1@1+ (1,0) [0|1] "" XXX
 SG_ RKS_Teillast : 20|1@1+ (1,0) [0|1] "" XXX

BO_ 914 Gate_Komf_2: 8 XXX
 SG_ GK2_Sta_LSM : 0|1@1+ (1,0) [0|1] "" XXX
 SG_ GK2_Sta_Lichtsensor : 1|1@1+ (1,0) [0|1] "" XXX
 SG_ GK2_Sta_Licht1 : 2|1@1+ (1,0) [0|1] "" XXX
 SG_ GK2_Sta_VSG : 3|1@1+ (1,0) [0|1] "" XXX
 SG_ GK2_Sta_Schluessel : 4|1@1+ (1,0) [0|1] "" XXX
 SG_ GK2_Sta_Profil : 5|1@1+ (1,0) [0|1] "" XXX
 SG_ GK2_Sta_Clima2 : 6|1@1+ (1,0) [0|1] "" XXX
 SG_ GK2_Sta_BSG4 : 7|1@1+ (1,0) [0|1] "" XXX
 SG_ GK2_Sta_Kessy_4 : 8|1@1+ (1,0) [0|1] "" XXX
 SG_ BS4_Gleitende_Leuchtw_Anf : 9|1@1+ (1,0) [0|1] "" XXX
 SG_ BS4_GLW_Fernlicht_Anf : 10|1@1+ (1,0) [0|1] "" XXX
 SG_ GK2_Blk_L_Kontrolle : 11|1@1+ (1,0) [0|1] "" XXX
 SG_ GK2_Blk_R_Kontrolle : 12|1@1+ (1,0) [0|1] "" XXX
 SG_ GK2_LS_KomFehler : 14|1@1+ (1,0) [0|1] "" XXX
 SG_ GK2_LS_def : 15|1@1+ (1,0) [0|1] "" XXX
 SG_ GK2_Helligkeit : 16|3@1+ (714.286,0) [0|5000.002] "Unit_Lux" XXX
 SG_ GK2_VD_zu_ver : 19|1@1+ (1,0) [0|1] "" XXX
 SG_ GK2_VD_entriegelt : 20|1@1+ (1,0) [0|1] "" XXX
 SG_ GK2_VD_offen_ver : 21|1@1+ (1,0) [0|1] "" XXX
 SG_ GK2_Verdeck_Anf : 22|1@1+ (1,0) [0|1] "" XXX
 SG_ GK2_VDKD_auf : 23|1@1+ (1,0) [0|1] "" XXX
 SG_ GK2_Schluessel : 24|4@1+ (1,0) [0|15] "" XXX
 SG_ GK2_Hardtop : 28|1@1+ (1,0) [0|1] "" XXX
 SG_ GK2_AFL_Schalter : 29|1@1+ (1,0) [0|1] "" XXX
 SG_ GK2_Nebelschluss : 30|1@1+ (1,0) [0|1] "" XXX
 SG_ GK2_EM_LIN_ungueltig : 31|1@1+ (1,0) [0|1] "" XXX
 SG_ GK2_Profil : 32|4@1+ (1,0) [0|15] "" XXX
 SG_ GK2_Kl_StSt_Info : 36|2@1+ (1,0) [0|3] "" XXX
 SG_ GK2_BSG_StSt_Info : 38|2@1+ (1,0) [0|3] "" XXX
 SG_ GK2_BEM_P_Generator : 40|8@1+ (50,0) [0|12700] "Unit_Watt" XXX
 SG_ GK2_BEM_Abschaltstufen : 48|3@1+ (1,0) [0|7] "" XXX
 SG_ GK2_BEM_DFM : 51|5@1+ (3.225,0.025) [0.025|100] "Unit_PerCent" XXX
 SG_ GK2_Kessy_StSt_Info : 56|2@1+ (1,0) [0|3] "" XXX
 SG_ GK2_BEM_StSt_Info : 58|2@1+ (1,0) [0|3] "" XXX

BO_ 954 SWA_1: 8 SWA
 SG_ SWA_Textbits : 12|4@1+ (1,0) [0|15] ""  XXX
 SG_ SWA_Gong : 16|2@1+ (1,0) [0|3] ""  XXX
 SG_ SWA_Sta_passiv : 19|1@1+ (1,0) [0|1] ""  XXX
 SG_ SWA_Sta_aktiv : 20|1@1+ (1,0) [0|1] ""  XXX
 SG_ SWA_Infostufe_SWA_li : 26|1@1+ (1,0) [0|1] ""  XXX
 SG_ SWA_Warnung_SWA_li : 27|1@1+ (1,0) [0|1] ""  XXX
 SG_ SWA_Infostufe_SWA_re : 42|1@1+ (1,0) [0|1] ""  XXX
 SG_ SWA_Warnung_SWA_re : 43|1@1+ (1,0) [0|1] ""  XXX
 SG_ SWA_KD_Fehler : 59|1@1+ (1,0) [0|1] ""  XXX

BO_ 1175 Parkhilfe_01: 8 XXX
 SG_ PH_Abschaltursache : 13|3@1+ (1,0) [0|7] "" XXX
 SG_ PH_Opt_Anzeige_V_ein : 16|1@1+ (1,0) [0|1] "" XXX
 SG_ PH_Opt_Anzeige_H_ein : 17|1@1+ (1,0) [0|1] "" XXX
 SG_ PH_Opt_Anz_V_Hindernis : 18|1@1+ (1,0) [0|1] "" XXX
 SG_ PH_Opt_Anz_H_Hindernis : 19|1@1+ (1,0) [0|1] "" XXX
 SG_ PH_Tongeber_V_aktiv : 20|1@1+ (1,0) [0|1] "" XXX
 SG_ PH_Tongeber_H_aktiv : 21|1@1+ (1,0) [0|1] "" XXX
 SG_ PH_Tongeber_mute : 22|1@1+ (1,0) [0|1] "" XXX
 SG_ PH_Anf_Audioabsenkung : 23|1@1+ (1,0) [0|1] "" XXX
 SG_ PH_Frequenz_hinten : 32|4@1+ (1,0) [0|15] "" XXX
 SG_ PH_Lautstaerke_hinten : 36|4@1+ (1,0) [0|15] "" XXX
 SG_ PH_Frequenz_vorn : 40|4@1+ (1,0) [0|15] "" XXX
 SG_ PH_Lautstaerke_vorn : 44|4@1+ (1,0) [0|15] "" XXX
 SG_ PH_Trigger_Bildaufschaltung : 48|1@1+ (1,0) [0|1] "" XXX
 SG_ PH_StartStopp_Info : 49|2@1+ (1,0) [0|3] "" XXX
 SG_ PH_Aufbauten_erk : 51|1@1+ (1,0) [0|1] "" XXX
 SG_ PH_BerErk_vorn : 52|2@1+ (1,0) [0|3] "" XXX
 SG_ PH_BerErk_hinten : 54|2@1+ (1,0) [0|3] "" XXX
 SG_ PH_defekt : 56|1@1+ (1,0) [0|1] "" XXX
 SG_ PH_gestoert : 57|1@1+ (1,0) [0|1] "" XXX
 SG_ PH_Systemzustand : 58|3@1+ (1,0) [0|7] "" XXX
 SG_ PH_Display_Kundenwunsch : 61|2@1+ (1,0) [0|3] "" XXX
 SG_ PH_KD_Fehler : 63|1@1+ (1,0) [0|1] "" XXX

BO_ 1463 Bremse_11: 8 XXX
 SG_ B11_HydHalten : 13|1@1+ (1,0) [0|1] "" XXX
 SG_ B11_Br_StSt_Info : 14|2@1+ (1,0) [0|3] "" XXX
 SG_ B11_OBD_Nib_VL : 16|4@1+ (1,0) [0|15] "" XXX
 SG_ B11_OBD_Nib_VR : 20|4@1+ (1,0) [0|15] "" XXX
 SG_ B11_OBD_Nib_HL : 24|4@1+ (1,0) [0|15] "" XXX
 SG_ B11_OBD_Nib_HR : 28|4@1+ (1,0) [0|15] "" XXX
 SG_ B11_EPB_Steller_akt : 32|1@1+ (1,0) [0|1] "" XXX
 SG_ B11_EPB_Steller_gue : 33|1@1+ (1,0) [0|1] "" XXX

BO_ 1500 Soll_Verbauliste_neu: 8 XXX
 SG_ VL1_Motor_SG : 0|1@1+ (1,0) [0|1] "" XXX
 SG_ VL1_Getr_SG : 1|1@1+ (1,0) [0|1] "" XXX
 SG_ VL1_ABS : 2|1@1+ (1,0) [0|1] "" XXX
 SG_ VL1_Kombi : 3|1@1+ (1,0) [0|1] "" XXX
 SG_ VL1_LSM : 4|1@1+ (1,0) [0|1] "" XXX
 SG_ VL1_Airbag : 5|1@1+ (1,0) [0|1] "" XXX
 SG_ VL1_Lenkhilfe : 6|1@1+ (1,0) [0|1] "" XXX
 SG_ VL1_dyn_LWR : 7|1@1+ (1,0) [0|1] "" XXX
 SG_ VL1_res_08 : 8|1@1+ (1,0) [0|1] "" XXX
 SG_ VL1_Allrad : 9|1@1+ (1,0) [0|1] "" XXX
 SG_ VL1_ADR : 10|1@1+ (1,0) [0|1] "" XXX
 SG_ VL1_ADR_getrennt : 11|1@1+ (1,0) [0|1] "" XXX
 SG_ VL1_EPB : 12|1@1+ (1,0) [0|1] "" XXX
 SG_ VL1_res_13 : 13|1@1+ (1,0) [0|1] "" XXX
 SG_ VL1_Daempfer : 14|1@1+ (1,0) [0|1] "" XXX
 SG_ VL1_Quersperre : 15|1@1+ (1,0) [0|1] "" XXX
 SG_ VL1_MotorSlave : 16|1@1+ (1,0) [0|1] "" XXX
 SG_ VL1_SWA : 17|1@1+ (1,0) [0|1] "" XXX
 SG_ VL1_HCA : 18|1@1+ (1,0) [0|1] "" XXX
 SG_ VL1_RKA_Plus : 19|1@1+ (1,0) [0|1] "" XXX
 SG_ VL1_PLA : 20|1@1+ (1,0) [0|1] "" XXX
 SG_ VL1_WFS_KBI : 21|1@1+ (1,0) [0|1] "" XXX
 SG_ VL1_Kombi_KBI : 22|1@1+ (1,0) [0|1] "" XXX
 SG_ VL1_Soll_eq_Ist : 23|1@1+ (1,0) [0|1] "" XXX
 SG_ VL1_BSG_Komf : 24|1@1+ (1,0) [0|1] "" XXX
 SG_ VL1_ZKE : 25|1@1+ (1,0) [0|1] "" XXX
 SG_ VL1_TSG_FT : 26|1@1+ (1,0) [0|1] "" XXX
 SG_ VL1_TSG_BT : 27|1@1+ (1,0) [0|1] "" XXX
 SG_ VL1_TSG_HL : 28|1@1+ (1,0) [0|1] "" XXX
 SG_ VL1_TSG_HR : 29|1@1+ (1,0) [0|1] "" XXX
 SG_ VL1_Memory : 30|1@1+ (1,0) [0|1] "" XXX
 SG_ VL1_Dachmodul_K : 31|1@1+ (1,0) [0|1] "" XXX
 SG_ VL1_Zentralelektrik_II : 32|1@1+ (1,0) [0|1] "" XXX
 SG_ VL1_RDK : 33|1@1+ (1,0) [0|1] "" XXX
 SG_ VL1_Lenksaeule : 34|1@1+ (1,0) [0|1] "" XXX
 SG_ VL1_Gateway : 35|1@1+ (1,0) [0|1] "" XXX
 SG_ VL1_Clima_Komf : 36|1@1+ (1,0) [0|1] "" XXX
 SG_ VL1_Einparkhilfe : 37|1@1+ (1,0) [0|1] "" XXX
 SG_ VL1_PTC_Heizung : 38|1@1+ (1,0) [0|1] "" XXX
 SG_ VL1_Standheiz : 39|1@1+ (1,0) [0|1] "" XXX
 SG_ VL1_Verdeck : 40|1@1+ (1,0) [0|1] "" XXX
 SG_ VL1_RSE_I : 41|1@1+ (1,0) [0|1] "" XXX
 SG_ VL1_res_42 : 42|1@1+ (1,0) [0|1] "" XXX
 SG_ VL1_MDI_I : 43|1@1+ (1,0) [0|1] "" XXX
 SG_ VL1_Anhaenger : 44|1@1+ (1,0) [0|1] ""  SWA
 SG_ VL1_Memory_BF : 45|1@1+ (1,0) [0|1] "" XXX
 SG_ VL1_Easy_Entry_VF : 46|1@1+ (1,0) [0|1] "" XXX
 SG_ VL1_Easy_Entry_VB : 47|1@1+ (1,0) [0|1] "" XXX
 SG_ VL1_Heckdeckel : 48|1@1+ (1,0) [0|1] "" XXX
 SG_ VL1_Rearview : 49|1@1+ (1,0) [0|1] "" XXX
 SG_ VL1_Sonderfzg_SG : 50|1@1+ (1,0) [0|1] "" XXX
 SG_ VL1_Tastenmodul : 51|1@1+ (1,0) [0|1] "" XXX
 SG_ VL1_Kompass : 52|1@1+ (1,0) [0|1] "" XXX
 SG_ VL1_WFS_K : 53|1@1+ (1,0) [0|1] "" XXX
 SG_ VL1_GSM_Pager : 54|1@1+ (1,0) [0|1] "" XXX
 SG_ VL1_InfoElektronik : 55|1@1+ (1,0) [0|1] "" XXX
 SG_ VL1_DSP : 56|1@1+ (1,0) [0|1] "" XXX
 SG_ VL1_DAB : 57|1@1+ (1,0) [0|1] "" XXX
 SG_ VL1_Telematik : 58|1@1+ (1,0) [0|1] "" XXX
 SG_ VL1_Navigation : 59|1@1+ (1,0) [0|1] "" XXX
 SG_ VL1_TV_Tuner : 60|1@1+ (1,0) [0|1] "" XXX
 SG_ VL1_Neigungsmodul_I : 61|1@1+ (1,0) [0|1] "" XXX
 SG_ VL1_Radio : 62|1@1+ (1,0) [0|1] "" XXX
 SG_ VL1_Telefon : 63|1@1+ (1,0) [0|1] "" XXX

BO_ 1490 Ident: 8 XXX
 SG_ IDT_Mux M : 0|2@1+ (1,0) [0|2] "" XXX
 SG_ IDT_Geheimnis_1 m0 : 8|8@1+ (1,0) [0|255] "" XXX
 SG_ IDT_VIN_4 m1 : 8|8@1+ (1,0) [0|255] "" XXX
 SG_ IDT_VIN_11 m2 : 8|8@1+ (1,0) [0|255] "" XXX
 SG_ IDT_Geheimnis_2 m0 : 16|8@1+ (1,0) [0|255] "" XXX
 SG_ IDT_VIN_5 m1 : 16|8@1+ (1,0) [0|255] "" XXX
 SG_ IDT_VIN_12 m2 : 16|8@1+ (1,0) [0|255] "" XXX
 SG_ IDT_Geheimnis_3 m0 : 24|8@1+ (1,0) [0|255] "" XXX
 SG_ IDT_VIN_6 m1 : 24|8@1+ (1,0) [0|255] "" XXX
 SG_ IDT_VIN_13 m2 : 24|8@1+ (1,0) [0|255] "" XXX
 SG_ IDT_Geheimnis_4 m0 : 32|8@1+ (1,0) [0|255] "" XXX
 SG_ IDT_VIN_7 m1 : 32|8@1+ (1,0) [0|255] "" XXX
 SG_ IDT_VIN_14 m2 : 32|8@1+ (1,0) [0|255] "" XXX
 SG_ IDT_VIN_1 m0 : 40|8@1+ (1,0) [0|255] "" XXX
 SG_ IDT_VIN_8 m1 : 40|8@1+ (1,0) [0|255] "" XXX
 SG_ IDT_VIN_15 m2 : 40|8@1+ (1,0) [0|255] "" XXX
 SG_ IDT_VIN_2 m0 : 48|8@1+ (1,0) [0|255] "" XXX
 SG_ IDT_VIN_9 m1 : 48|8@1+ (1,0) [0|255] "" XXX
 SG_ IDT_VIN_16 m2 : 48|8@1+ (1,0) [0|255] "" XXX
 SG_ IDT_VIN_3 m0 : 56|8@1+ (1,0) [0|255] "" XXX
 SG_ IDT_VIN_10 m1 : 56|8@1+ (1,0) [0|255] "" XXX
 SG_ IDT_VIN_17 m2 : 56|8@1+ (1,0) [0|255] "" XXX

BO_ 2000 Diagnose_1: 8 XXX
 SG_ DI1_VerlernZaehl : 0|8@1+ (1,0) [0|254] "" XXX
 SG_ DI1_km_Stand : 8|20@1+ (1,0) [0|1048575] "Unit_KiloMeter" XXX
 SG_ DI1_Jahr : 28|7@1+ (1,2000) [2000|2127] "Unit_Year" XXX
 SG_ DI1_Monat : 35|4@1+ (1,0) [1|12] "Unit_Month" XXX
 SG_ DI1_Tag : 39|5@1+ (1,0) [0|31] "Unit_Day" XXX
 SG_ DI1_Stunde : 44|5@1+ (1,0) [0|23] "Unit_Hours" XXX
 SG_ DI1_Minute : 49|6@1+ (1,0) [0|59] "Unit_Minut" XXX
 SG_ DI1_Sekunde : 55|6@1+ (1,0) [0|59] "Unit_Secon" XXX
 SG_ DI1_KM_Stand_alt : 62|1@1+ (1,0) [0|1] "" XXX
 SG_ DI1_Zeit_alt : 63|1@1+ (1,0) [0|1] "" XXX

CM_ SG_ 80 Checksumme_Airbag_1 "Checksum Airbag_1";
CM_ SG_ 80 Zaehler_Airbag_1 "Counter Airbag_1";

CM_ SG_ 210 LM_Offset "centiNewton-meters for ease of calculation without FP math in Panda";
CM_ SG_ 210 Vib_Amp "Steering wheel haptic, amplitude";
CM_ SG_ 210 Vib_Freq "Steering wheel haptic, frequency";

CM_ SG_ 416 Zaehler_Bremse_1 "Counter Bremse_1";

CM_ SG_ 640 inneres_Motor_Moment "Engine Indicated Torque";
CM_ SG_ 640 Fahrerwunschmoment "Driver Requested Torque";
CM_ SG_ 640 mechanisches_Motor_Verlustmomen "Mechanical Torque Loss";
CM_ SG_ 640 Fahrpedalwert_oder_Drosselklapp "Accelerator Pedal or Throttle Position";
CM_ SG_ 640 Motordrehzahl "Engine Speed";
CM_ SG_ 640 Momentenangaben_ungenau "Approximate Torque Values";
CM_ SG_ 640 inneres_Motor_Moment_ohne_exter "Inner torque without external";

CM_ SG_ 644 MOB_CHECKSUM "Checksum MOB";
CM_ SG_ 644 MOB_COUNTER "Counter MOB";

CM_ SG_ 648 Minimales_Motormoment_bei_Zuend "Minimum Torque Adjustment";
CM_ SG_ 648 Begrenzungsmoment "Maximum Inner Torque";
CM_ SG_ 648 Bremstestschalter "Brake Test Switch";
CM_ SG_ 648 Soll_Geschwindigkeit_bei_GRA_Be "Desired Vehicle Speed";
CM_ SG_ 648 Bremslichtschalter "Brake Light Switch";
CM_ SG_ 648 Leerlaufsolldrehzahl__Motor_2_ "Target Idle Speed";
CM_ SG_ 648 Fahrzeuggeschwindigkeit "Vehicle Speed";
CM_ SG_ 648 Kuehlmitteltemperatur__Motor_2_ "Coolant Temperature";

CM_ SG_ 896 Drosselklappenpoti "Throttle Position";
CM_ SG_ 896 Motor_Wunschdrehzahl "Desired engine speed";
CM_ SG_ 896 Motordrehzahlbeeinflussung "Shift Target Influence";
CM_ SG_ 896 Fahrpedal_Rohsignal "Accelerator Pedal Position";
CM_ SG_ 896 Ansauglufttemperatur "Intake Air Temperature";
CM_ SG_ 896 Kein_E_Gas "ETB flag";
CM_ SG_ 896 Kein_Start_Stop "Start/stop flag";
CM_ SG_ 896 Rad_Wunschmoment "Desired wheel torque";

CM_ SG_ 912 GK1_Fa_Tuerkont "Status of the driver's door rotary latch";
CM_ SG_ 912 BSK_HL_geoeffnet "Status of the rear left door rotary latch";
CM_ SG_ 912 BSK_HR_geoeffnet "Status of the rear right door rotary latch";
CM_ SG_ 912 BSK_BT_geoeffnet "Status of the passenger door rotary latch";
CM_ SG_ 912 BSK_HD_Hauptraste "Status of trunk lid main detent";

CM_ SG_ 1088 Zaehler_Getriebe_1 "Counter Getriebe_1";
CM_ SG_ 1088 Waehlhebelposition__Getriebe_1_ "Gear Selector Position";
CM_ SG_ 1088 inneres_Soll_Motormoment "Desired Inner Torque";
CM_ SG_ 1088 Gang_eingelegt "Gear Engaged";
CM_ SG_ 1088 Schaltabsicht "Shift Intent";
CM_ SG_ 1088 Kuehlleistung "Cooling Power";
CM_ SG_ 1088 Wandlerverlustmoment "Converter Torque Loss";
CM_ SG_ 1088 Getriebe_Notlauf "Transmission_Notlauf";
CM_ SG_ 1088 Zielgang_oder_eingelegter_Gang "target_gear_or_gear_in_engagement";
CM_ SG_ 1088 Uebertragungsfunktion "transfer function";
CM_ SG_ 1088 EGS_Anforderung "EGS Requirement";
CM_ SG_ 1088 Schaltung_aktiv__Getriebe_1_ "Shift Activity";

CM_ SG_ 1056 Fehlerstatus_Aussentemp__4_1 "ambient temp error";
CM_ SG_ 1056 Fehlerstatus_Oeltemperatur_4_1 "oil temp error";
CM_ SG_ 1056 Fehlerst__Kuehlmitteltemp__4_1 "water temp error";
CM_ SG_ 1056 Aussentemperatur_gefiltert "outside temp, filtered";
CM_ SG_ 1056 Oeltemperatur_4_1 "kombi oil temperature";
CM_ SG_ 1056 Kuehlmitteltemp__4_1__Kombi_2_ "kombi coolant temperature";

CM_ SG_ 1096 Zaehler_Waehlhebel_1 "Counter Waehlhebel_1";

CM_ SG_ 1152 CHECKSUM "Checksum Motor_5";
CM_ SG_ 1152 Anlasser_Ausspuren "Starter Disable";
CM_ SG_ 1152 Anlasser_Freigabe "Starter Release";
CM_ SG_ 1152 Klimadrucksignal__Motor_5_ "Air conditioning pressure signal";
CM_ SG_ 1152 Kraftstoffverbrauchssignal "Fuel consumption signal";
CM_ SG_ 1152 K_hlerluefteransteuerung "Cooling fan control signal";
CM_ SG_ 1152 Klimakompressor_Leistungsreduzi "Air conditioning compressor power reduction flag";
CM_ SG_ 1152 Klimakompressor_aus__Motor_5_ "Air conditioning compressor";
CM_ SG_ 1152 Anlasser_Freigabe "Starter release";
CM_ SG_ 1152 OBD_2_Lampe "OBD light";
CM_ SG_ 1152 E_Gas_Lampe "ETB light";
CM_ SG_ 1152 Ladekontroll_Lampe "Charge light";
CM_ SG_ 1152 Vorgluehlampe__Motor_5_ "Glow light";

CM_ SG_ 1160 Zaehler_Motor_6 "Counter Motor_6";
CM_ SG_ 1160 Hoeheninfo__Motor_6_ "Altitude Correction";
CM_ SG_ 1160 Istmoment_f_r_Getriebe "Actual torque for gear";
CM_ SG_ 1160 Sollmoment_f_r_Getriebe "Target torque for gearbox";
CM_ SG_ 1160 Checksumme_Motor_6 "Checksum Motor_6";
CM_ SG_ 1160 GRA_Sollbeschleunigung "GRA target acceleration";
CM_ SG_ 1160 Ruckmeldung_Momenten "Feedback torque-integral gear intervention";

CM_ SG_ 1344 Zahler_Getriebe_2 "Counter Getriebe_2";
CM_ SG_ 1344 Hochschaltlampe "Upshift Flag";

CM_ SG_ 1386 ACA_V_Wunsch "255=unset";

CM_ SG_ 1408 Zaehler_Motor_Flexia "Counter Motor_Flexia";
CM_ SG_ 1408 Verbrennungsart "Type of combustion";
CM_ SG_ 1408 Max_Drehmoment "Maximum torque";
CM_ SG_ 1408 Drehzahl_MaxNorm "RPM of maximum torque";
CM_ SG_ 1408 Hubraum "Displacement";
CM_ SG_ 1408 Anzahl_Zylinder "Number of cylinders";
CM_ SG_ 1408 Anzahl_Ventile "Number of valves";
CM_ SG_ 1408 Ansaugsystem "Induction System";
CM_ SG_ 1408 Motorleistung "Maximum engine power";

CM_ SG_ 1416 Ladedruck "Boost Pressure";
CM_ SG_ 1416 Motordrehzahlgradient "Engine speed gradient";
CM_ SG_ 1416 Hoeheninfo__Motor_7_ "Altitude correction factor";
CM_ SG_ 1416 Oltemperatur "Oil temperature";

CM_ SG_ 1470 LDW_Direction "0=right,1=left";
CM_ SG_ 1470 XX_DLCORTLC1 "Might be DLC or TLC";
CM_ SG_ 1470 XX_DLCORTLC2 "Might be DLC or TLC, might have wrong size";

CM_ SG_ 1550 MFA_v_Signal_02 "0=km/h, 1=mph";

VAL_ 870 AWV_Text 0 "kein_Text" 1 "FrontAssist_aus" 2 "FrontAssist_startet" 3 "FrontAssist_Warnung" 4 "FrontAssist_Sens_reinig" 5 "FrontAssist_Failure" 6 "FrontAssist_Demo" 7 "Vorhalt" 8 "Bremsung_wird_gerade_durchgefuehrt" 9 "Sensor_not_verfuegbar" 10 "Sensor_reinigen" 11 "Service_notwendig_Failure" 12 "Funktion_vom_Fahrer_deactivated" 13 "Funktion_vom_Fahrer_activated" 14 "FrontAssist_zur_Zeit_not_verfuegbar__rev_Failure";
VAL_ 870 AWV_1_Freigabe 0 "nicht_freigegeben" 1 "freigegeben";
VAL_ 870 AWV_1_Prefill 0 "keine_Prefill_Anf" 1 "Prefill_Anf";
VAL_ 870 AWV_1_Parameter 0 "Defaultparametersatz" 1 "Par_leicht_erh_Empf" 2 "Par_erh_Empf" 3 "Par_hoechster_Empf";
VAL_ 870 AWV_only 0 "ACC_und_AWV_verbaut" 1 "AWV_ohne_ACC_verbaut";
VAL_ 870 AWV_CityANB_Auspraegung 0 "autom_Bremsung_im_ges_vBereich" 1 "autom_Bremsung_im_def_vBereich";
VAL_ 870 AWV_Halten 0 "keine_Anforderung" 1 "Anforderung_das_Fzg_im_Stillstand_zu_halten";
VAL_ 870 ANB_Teilbremsung_Freigabe 0 "Teilbremsung_nicht_freigegeben" 1 "Teilbremsung_freigegeben";
VAL_ 870 AWV_2_Status 0 "Lampe_aus" 1 "Lampe_ein";
VAL_ 870 AWV_2_Fehler 0 "Lampe_aus" 1 "Lampe_ein";
VAL_ 870 AWV_2_SU_Warnzeit 0 "frueh" 1 "normal" 2 "spaet" 3 "adaptiv";
VAL_ 870 AWV_2_SU_Bremsruck 0 "Bremsruck_deaktiviert" 1 "Bremsruck_aktiviert";
VAL_ 870 AWV_2_SU_Gong 0 "Gong_deaktiviert" 1 "Gong_aktiviert";
VAL_ 870 AWV_2_SU_Lampe 0 "Lampe_deaktiviert" 1 "Lampe_aktiviert";
VAL_ 870 AWV_2_Umfeldwarn 0 "keine_Warnung" 1 "Warnung";
VAL_ 870 AWV_2_Freigabe 0 "keine_Ruckfreigabe" 1 "Ruckfreigabe";
VAL_ 870 AWV_2_Ruckprofil 0 "kein_Ruck" 1 "Ruckprofil_1" 2 "Ruckprofil_2" 3 "Ruckprofil_3" 4 "Ruckprofil_4" 5 "Ruckprofil_5" 6 "not_erlaubt" 7 "not_erlaubt";
VAL_ 870 AWV_2_Warnton 0 "Aus" 1 "Ein";
VAL_ 870 AWV_2_Warnsymbol 0 "Aus" 1 "Ein";
VAL_ 870 AWV_Infoton 0 "Aus" 1 "Ein";
VAL_ 870 AWV_2_Gurtstraffer 0 "Gurt_not_straffen" 1 "Gurt_straffen";
VAL_ 870 AWV_Konfiguration_Menueanf 0 "Menue_deaktivieren" 1 "Menue_aktivieren";
VAL_ 870 AWV_Konfiguration_Vorw_Menueanf 0 "Menue_deaktivieren" 1 "Menue_aktivieren";
VAL_ 870 AWV_Konfiguration_Status 0 "AWV_inaktiv" 1 "AWV_aktiv";
VAL_ 870 AWV_Konfiguration_Vorw_Status 0 "AWV_Vorwarnung_inaktiv" 1 "AWV_Vorwarnung_aktiv";
VAL_ 870 AWV_2_Abstandswarnung 0 "kein_Warnhinweis" 1 "Warnhinweis";
VAL_ 870 ANB_Zielbremsung_Freigabe 0 "Zielbremsung_nicht_freigegeben" 1 "Zielbremsung_freigegeben";
VAL_ 870 ANB_CM_Anforderung 0 "keine_Anforderung" 1 "Anforderung_aktiv";

VAL_ 872 ACS_Sta_ADR 2 "ADR_passiv" 0 "ADR_nicht_aktiv" 1 "ADR_aktiv" 3 "irrev_Fehler" ;
VAL_ 872 ACS_ADR_Schub 1 "Verz_begr_auf_Schub" 0 "Verz_nicht_begr_auf_Schub" ;
VAL_ 872 ACS_Schubabsch 1 "SA_nicht_zulaessig" 0 "SA_zulaessig" ;
VAL_ 872 ACS_StSt_Info 3 "Systemfehler" 0 "Motorlauf_nn" 1 "Stoppverbot_Motoranlauf_nn" 2 "Motoranlauf_notwendig" ;
VAL_ 872 ACS_MomEingriff 1 "MomEingr_verhindern" 0 "keine_Beeinfl_MomEingr_Mot" ;
VAL_ 872 ACS_Typ_ACC 0 "Basis_ACC" 1 "ACC_mit_FollowToStop" 3 "frei" 2 "frei" ;
VAL_ 872 ACS_FreigSollB 0 "Sollbeschl_nicht_freigeg" 1 "Sollbeschl_freigeg" ;
VAL_ 872 ACS_Sollbeschl 2046 "ADR_nicht_aktiv" 2047 "Fehler" ;
VAL_ 872 ACS_Anhaltewunsch 0 "kein_Haltewunsch" 1 "Fzg_haelt_an" ;
VAL_ 872 ACS_Fehler 1 "Fehlerspeichereintrag" 0 "kein_Fehlerspeichereintrag" ;
VAL_ 872 ACS_zul_Regelabw 254 "ADR_nicht_aktiv" 255 "Fehler" ;
VAL_ 872 ACS_max_AendGrad 254 "Neutralwert" 0 "Neutralwert" 255 "Fehler" ;

VAL_ 978 LH2_Sta_HCA 0 "disabled" 1 "initializing" 2 "fault" 3 "ready" 4 "rejected" 5 "active";
VAL_ 1088 Waehlhebelposition__Getriebe_1_ 8 "P" 7 "R" 6 "N" 5 "D" 9 "U" 12 "S" 14 "T" 10 "T" 11 "T";

VAL_ 1386 ACA_StaACC 6 "ACC_rev_aus" 0 "Hauptschalter_aus" 4 "ACC_im_Hintergrund" 3 "ACC_aktiv" 1 "Reserve" 2 "ACC_passiv" 7 "ACC_irrev_aus" 5 "frei" ;
VAL_ 1386 ACA_ID_StaACC 0 "keine_Anzeige" ;
VAL_ 1386 ACA_Fahrerhinw 1 "Ein" 0 "Aus" ;
VAL_ 1386 ACA_AnzDisplay 1 "Anzeige_erw" 0 "Anzeige_nicht_erw" ;
VAL_ 1386 ACA_Zeitluecke 3 "Zeitluecke3" 10 "Zeitluecke10" 4 "Zeitluecke4" 14 "Zeitluecke14" 11 "Zeitluecke11" 2 "Zeitluecke2" 13 "Zeitluecke13" 9 "Zeitluecke9" 1 "Zeitluecke1" 8 "Zeitluecke8" 5 "Zeitluecke5" 15 "Zeitluecke15" 0 "nicht_definiert" 12 "Zeitluecke12" 6 "Zeitluecke6" 7 "Zeitluecke7" ;
VAL_ 1386 ACA_V_Wunsch 255 "kein_Wert_im_Speicher" ;
VAL_ 1386 ACA_kmh_mph 0 "km_h" 1 "mph" ;
VAL_ 1386 ACA_Akustik1 0 "kein_Gong" 1 "Gong" ;
VAL_ 1386 ACA_Akustik2 0 "kein_Summer" 1 "Summer" ;
VAL_ 1386 ACA_PrioDisp 1 "mittlere_Prio" 3 "keine_Anzeige_Anf" 0 "hohe_Prio" 2 "niedrige_Prio" ;
VAL_ 1386 ACA_gemZeitl 6 "Zeitluecke6" 2 "Zeitluecke2" 7 "Zeitluecke7" 13 "Zeitluecke13" 11 "Zeitluecke11" 4 "Zeitluecke4" 8 "Zeitluecke8" 12 "Zeitluecke12" 10 "Zeitluecke10" 0 "Kein_Objekt_erfasst" 1 "Zeitluecke1" 3 "Zeitluecke3" 9 "Zeitluecke9" 15 "Zeitluecke15" 14 "Zeitluecke14" 5 "Zeitluecke5" ;
VAL_ 1386 ACA_ACC_Verz 0 "ACC_verzoegert_nicht" 1 "ACC_verzoegert" ;
VAL_ 1386 ACA_StaGRA 3 "GRA_aktiv" 4 "GRA_uebertreten" 2 "GRA_passiv" 0 "Hauptschalter_aus" 6 "frei" 7 "GRA_Fehler" 1 "Reserve" 5 "frei" ;
VAL_ 1386 ACA_ID_StaGRA 0 "keine_Anzeige" ;
VAL_ 1386 ACA_Codierung 0 "ACC" 1 "GRA" ;
VAL_ 1386 ACA_Tachokranz 0 "nicht_beleuchtet" 1 "beleuchtet" ;
VAL_ 1386 ACA_Aend_Zeitluecke 1 "Anzeige_angef" 0 "keine_Anzeige" ;

VAL_ 1472 EP1_Fehler_Sta 0 "volle_Funktion" 1 "linke_Seite_fehlerhaft" 2 "rechte_Seite_fehlerhaft" 3 "beide_Seiten_fehlerhaft";
VAL_ 1472 EP1_Sta_EPB 0 "Bremse_geoeffnet" 1 "Bremse_geschlossen";
VAL_ 1472 EP1_Sta_Schalter 0 "volle_Funktion" 1 "Schalter_ausser_Funktion";
VAL_ 1472 EP1_Spannkraft 31 "Fehler";
VAL_ 1472 EP1_Schalterinfo 0 "keine_Fahreranforderung" 1 "Fahreranforderung_oeffnen" 2 "Fahreranforderung_schliessen" 3 "Schalterfehler";
VAL_ 1472 EP1_Sta_NWS 0 "volle_Funktion" 1 "keine_Funktion";
VAL_ 1472 EP1_Fehlereintr 0 "kein_Fehlerspeichereintrag" 1 "Fehlerspeichereintrag";
VAL_ 1472 EP1_Freigabe_Ver 0 "Verzoegerungsanf_nicht_freigegeb" 1 "Verzoegerungsanf_freigegeben";
VAL_ 1472 EP1_AutoHold_zul 0 "Pers_nicht_zulaessig" 1 "Pers_zulaessig";
VAL_ 1472 EP1_AutoHold_aktiv 0 "nein" 1 "ja";
VAL_ 1472 EP1_SleepInd 0 "CAN_wird_benoetigt" 1 "Sleep_bereit";
VAL_ 1472 EP1_Status_Kl_15 0 "Kl_15_aus" 1 "Kl_15_ein";
VAL_ 1472 EP1_Lampe_AutoP 0 "Lampe_aus" 1 "Lampe_ein";
VAL_ 1472 EP1_Bremslicht 0 "Aus" 1 "Ein";
VAL_ 1472 EP1_Warnton1 0 "Aus" 1 "Ein";
VAL_ 1472 EP1_Warnton2 0 "Aus" 1 "Ein";
VAL_ 1472 EP1_AnfShLock 0 "Aus" 1 "Ein";
VAL_ 1472 EPB_Autoholdlampe 0 "Lampe_aus" 1 "Lampe_ein";
VAL_ 1472 EP1_QualNeigWi 0 "gueltiger_Wert" 1 "Ersatz_Init_oder_Fehlerwert";
VAL_ 1472 EP1_KuppModBer 0 "Kuppsensor_aus_Modbereich" 1 "Kupplsensor_im_Modbereich" 2 "Sensorsignal_ungenau" 3 "Sensor_defekt";
VAL_ 1472 EP1_HydrHalten 0 "Fzg_nicht_hydr_geh" 1 "Fzg_hydr_geh";
VAL_ 1472 EP1_Fkt_Lampe 0 "Lampe_aus" 1 "Lampe_ein";
VAL_ 1472 EP1_Warnton 0 "Warnton_aus" 1 "Warnton_an";
VAL_ 1472 EP1_Fehler_BKL 0 "BKL_aus" 1 "BKL_an";
VAL_ 1472 EP1_Fehler_gelb 0 "Lampe_aus" 1 "Lampe_ein";
VAL_ 1472 EP1__Text 0 "kein_Text" 1 "Text_1" 2 "Text_2" 3 "Text_3" 4 "Text_4" 5 "Text_5" 6 "reserviert" 7 "reserviert" 8 "Text_8";
