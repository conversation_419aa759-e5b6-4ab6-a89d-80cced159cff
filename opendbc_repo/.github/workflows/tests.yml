name: tests

on:
  push:
    branches:
      - master
  pull_request:

jobs:
  tests:
    name: ./test.sh
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        include:
          - os: ${{ github.repository == 'commaai/opendbc' && 'namespace-profile-amd64-8x16' || 'ubuntu-24.04' }}
            flags: ''
          - os: ${{ github.repository == 'commaai/opendbc' && 'namespace-profile-amd64-8x16' || 'ubuntu-24.04' }}
            flags: '--ubsan'
          - os: ${{ github.repository == 'commaai/opendbc' && 'namespace-profile-macos-8x14' || 'macos-latest' }}
            flags: ''
    steps:
    - uses: commaai/timeout@v1
      with:
        timeout: ${{ github.repository == 'commaai/opendbc' && '60' || '999' }}
    - uses: actions/checkout@v4
    - uses: ./.github/workflows/cache
    - run: ./test.sh ${{ matrix.flags }}

  mutation:
    name: Safety mutation tests
    runs-on: ${{ github.repository == 'commaai/opendbc' && 'namespace-profile-amd64-8x16' || 'ubuntu-latest' }}
    timeout-minutes: 20
    env:
      GIT_REF: ${{ github.event_name == 'push' && github.ref == format('refs/heads/{0}', github.event.repository.default_branch) && github.event.before || format('origin/{0}', github.event.repository.default_branch) }}
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0  # need master to get diff
      - uses: ./.github/workflows/cache
      - name: Run mutation tests
        timeout-minutes: 5
        run: |
          source setup.sh
          scons -j8
          cd opendbc/safety/tests && ./mutation.sh
